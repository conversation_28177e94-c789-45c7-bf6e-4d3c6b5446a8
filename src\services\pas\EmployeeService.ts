import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";
import { User, createEmptyUser } from "@/services/common/userService";
import { Promotion, createEmptyPromotion } from "@/services/pas/PromotionService";

// 員工介面
export interface Employee {
    userId: string;
    idNo: string;
    idType: string;
    idTypeName: string;
    errorMark: string;
    errorMarkName: string;
    empNo: string;
    birthday: string;
    bloodType: string;
    bloodTypeName: string;
    spouseIdNo: string;
    spouseName: string;
    eduLevel: string;
    eduLevelName: string;
    hireDate: string;
    probStartDate: string;
    officialHireDate: string;
    leaveDate: string;
    laborInsStartDate: string;
    healthInsStartDate: string;
    transferDate: string;
    remark: string;
    createTime?: number | null;
    createUserId?: string | null;
    updateTime?: number | null;
    updateUserId?: string | null;
    deleteTime?: number | null;
    deleteUserId?: string | null;
    isDeleted: boolean;
    usersDTO: User;
    // 新增：當前生效的升遷資料
    currentPromotion?: Promotion;
}

export const createEmptyEmployee = (): Employee => ({
    userId: '',
    idNo: '',
    idType: '',
    idTypeName: '',
    errorMark: '',
    errorMarkName: '',
    empNo: '',
    birthday: '',
    bloodType: '',
    bloodTypeName: '',
    spouseIdNo: '',
    spouseName: '',
    eduLevel: '',
    eduLevelName: '',
    hireDate: '',
    probStartDate: '',
    officialHireDate: '',
    leaveDate: '',
    laborInsStartDate: '',
    healthInsStartDate: '',
    transferDate: '',
    remark: '',
    createTime: null,
    createUserId: null,
    updateTime: null,
    updateUserId: null,
    deleteTime: null,
    deleteUserId: null,
    isDeleted: false,
    usersDTO: createEmptyUser(),
    currentPromotion: createEmptyPromotion(''),
});

export interface FilterData {
    filterType: string;
    filterValue: string;
}


// 搜尋員工列表
export async function getEmployeeList(filterData: Partial<FilterData>): Promise<ApiResponse<Employee[]>> {
    try {
        const response = await httpClient(apiEndpoints.getEmployeeList, {
            method: "POST",
            body: JSON.stringify(filterData),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋員工列表失敗",
        };
    }
}

// 搜尋員工明細
export async function getEmployeeDetail(userId: string): Promise<ApiResponse<Employee>> {
    return await httpClient(`${apiEndpoints.getEmployeeDetail}/${userId}`, {
        method: "GET",
    });
}

// 新增員工資料
export async function addEmployee(data: Partial<Employee>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addEmployee, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增員工資料失敗",
        };
    }
}

// 編輯員工資料
export async function editEmployee(data: Partial<Employee>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editEmployee, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯員工資料失敗",
        };
    }
}

// 補全員工資料
export async function completeEmployee(data: Partial<Employee>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.completeEmployee, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(JSON.stringify(data));
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "補全員工資料失敗",
        };
    }
}

// 刪除員工資料
export async function deleteEmployee(userId: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteEmployee, {
            method: "POST",
            body: JSON.stringify(userId),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除員工資料失敗",
        };
    }
} 
