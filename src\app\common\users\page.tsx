"use client";

import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  message,
  Select,
} from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  getUsers,
  createUser,
  updateUser,
  deleteUser,
  User,
} from "@/services/common/userService";
import { getRoles, Role } from "@/services/common/RolesService";
import { notifySuccess, notifyError } from "@/utils/notification";

const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [form] = Form.useForm();

  // 加載用戶列表
  const loadUsers = async () => {
    setLoading(true);
    try {
      const response = await getUsers();
      if (response.success && response.data) {
        setUsers(response.data);
      } else {
        notifyError("獲取用戶列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取用戶列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 加載角色列表
  const loadRoles = async () => {
    setLoading(true);
    try {
      const rolesResponse = await getRoles();

      if (rolesResponse.success && rolesResponse.data) {
        setRoles(rolesResponse.data);
      } else {
        notifyError("獲取角色列表失敗", rolesResponse.message);
      }
    } catch (error) {
      notifyError("獲取資料失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    loadUsers();
    loadRoles();
  }, []);

  // 表格列定義
  const columns: ColumnsType<User> = [
    {
      title: "帳號",
      dataIndex: "account",
      key: "account",
      sorter: (a, b) => a.account.localeCompare(b.account),
    },
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: "電子郵件",
      dataIndex: "eMail",
      key: "eMail",
      sorter: (a, b) => a.eMail.localeCompare(b.eMail),
    },
    {
      title: "電話",
      dataIndex: "telNo",
      key: "telNo",
      sorter: (a, b) => a.telNo.localeCompare(b.telNo),
    },
    {
      title: "手機",
      dataIndex: "phone",
      key: "phone",
      sorter: (a, b) => a.phone.localeCompare(b.phone),
    },
    {
      title: "身分",
      dataIndex: "rolesId",
      key: "rolesId",
      sorter: (a, b) => a.rolesId.localeCompare(b.rolesId),
      render: (rolesId: string) => {
        const role = roles.find((r) => r.rolesId === rolesId);
        return role ? (
          role.name
        ) : (
          <span style={{ color: "#ff4d4f" }}>角色不存在</span>
        );
      },
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  // 處理新增/編輯表單提交
  const handleSubmit = async (values: any) => {
    try {
      if (editingUser) {
        // 更新用戶 - 先帶入原始資料，再覆蓋表單變更的資料
        const response = await updateUser({
          ...editingUser,
          ...values,
        });
        if (response.success) {
          notifySuccess("更新成功", "用戶資料已更新");
          loadUsers();
        } else {
          notifyError("更新失敗", response.message);
        }
      } else {
        // 新增用戶
        const response = await createUser(values);
        if (response.success) {
          notifySuccess("新增成功", "用戶已新增");
          loadUsers();
        } else {
          notifyError("新增失敗", response.message);
        }
      }
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 處理編輯
  const handleEdit = (user: User) => {
    setEditingUser(user);
    form.setFieldsValue(user);
    setIsModalVisible(true);
  };

  // 處理刪除
  const handleDelete = (user: User) => {
    Modal.confirm({
      title: "確認刪除",
      content: `確定要刪除用戶 ${user.name} 嗎？`,
      okText: "確認",
      cancelText: "取消",
      onOk: async () => {
        try {
          const response = await deleteUser(user.userId);
          if (response.success) {
            notifySuccess("刪除成功", "用戶已刪除");
            loadUsers();
          } else {
            notifyError("刪除失敗", response.message);
          }
        } catch (error) {
          notifyError("刪除失敗", "請稍後再試");
        }
      },
    });
  };

  return (
    <Card title="用戶管理">
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={() => {
          setEditingUser(null);
          form.resetFields();
          setIsModalVisible(true);
        }}
        style={{ marginBottom: 16 }}
      >
        新增用戶
      </Button>

      <Table
        columns={columns}
        dataSource={users}
        rowKey="userId"
        loading={loading}
      />

      <Modal
        title={editingUser ? "編輯用戶" : "新增用戶"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        okText="確認"
        cancelText="取消"
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="account"
            label="帳號"
            rules={[{ required: true, message: "請輸入帳號" }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: "請輸入姓名" }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="eMail"
            label="電子郵件"
            rules={[
              { required: true, message: "請輸入電子郵件" },
              { type: "email", message: "請輸入有效的電子郵件地址" },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item name="telNo" label="電話">
            <Input />
          </Form.Item>
          <Form.Item name="phone" label="手機">
            <Input />
          </Form.Item>
          <Form.Item
            name="rolesId"
            label="身分"
            rules={[{ required: true, message: "請選擇角色" }]}
            extra={
              editingUser &&
                !roles.find((r) => r.rolesId === editingUser.rolesId) ? (
                <span style={{ color: "#ff4d4f" }}>
                  當前角色不存在，請重新選擇
                </span>
              ) : null
            }
          >
            <Select
              placeholder="請選擇角色"
              options={roles.map((role) => ({
                label: role.name,
                value: role.rolesId,
              }))}
            />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default UserManagementPage;
