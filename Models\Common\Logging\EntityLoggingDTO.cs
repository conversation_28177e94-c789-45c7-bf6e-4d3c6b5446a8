using System;
using System.Collections.Generic;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 實體日誌記錄 DTO 基礎類別
    /// 專門用於日誌記錄的序列化安全資料結構，避免循環引用問題
    /// </summary>
    public class EntityLoggingDTO
    {
        /// <summary> 實體類型名稱 </summary>
        public string EntityType { get; set; } = string.Empty;

        /// <summary> 實體主鍵 </summary>
        public string EntityId { get; set; } = string.Empty;

        /// <summary> 實體狀態 (Added, Modified, Deleted) </summary>
        public string EntityState { get; set; } = string.Empty;

        /// <summary> 變更時間戳記 </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary> 使用者 ID </summary>
        public string? UserId { get; set; }

        /// <summary> 實體屬性資料 (僅包含標量屬性) </summary>
        public Dictionary<string, object?> Properties { get; set; } = new();

        /// <summary> 變更前的屬性值 (僅用於 Modified 狀態) </summary>
        public Dictionary<string, object?>? OriginalProperties { get; set; }

        /// <summary> 變更的屬性清單 (僅用於 Modified 狀態) </summary>
        public List<string>? ChangedProperties { get; set; }

        /// <summary> 額外的中繼資料 </summary>
        public Dictionary<string, object>? Metadata { get; set; }
    }

    /// <summary>
    /// 變更日誌 DTO
    /// 用於記錄實體變更的詳細資訊
    /// </summary>
    public class EntityChangeLogDTO
    {
        /// <summary> 交易 ID </summary>
        public string TransactionId { get; set; } = string.Empty;

        /// <summary> 變更的實體清單 </summary>
        public List<EntityLoggingDTO> ChangedEntities { get; set; } = new();

        /// <summary> 變更總數 </summary>
        public int TotalChanges { get; set; }

        /// <summary> 變更時間 </summary>
        public DateTime ChangeTime { get; set; } = DateTime.UtcNow;

        /// <summary> 操作來源 </summary>
        public string Source { get; set; } = "System";
    }

    /// <summary>
    /// 日誌資料介面
    /// 定義可記錄資料的基本結構
    /// </summary>
    public interface ILoggingData
    {
        /// <summary> 轉換為序列化安全的字典 </summary>
        Dictionary<string, object?> ToSafeDictionary();
    }

    /// <summary>
    /// 序列化結果
    /// 包含序列化狀態和結果資料
    /// </summary>
    public class SerializationResult
    {
        /// <summary> 序列化是否成功 </summary>
        public bool IsSuccess { get; set; }

        /// <summary> 序列化後的資料 </summary>
        public Dictionary<string, object?>? Data { get; set; }

        /// <summary> 錯誤訊息 (如果序列化失敗) </summary>
        public string? ErrorMessage { get; set; }

        /// <summary> 使用的序列化方法 </summary>
        public string SerializationMethod { get; set; } = "Unknown";

        /// <summary> 序列化耗時 (毫秒) </summary>
        public long ElapsedMilliseconds { get; set; }

        /// <summary> 建立成功結果 </summary>
        public static SerializationResult Success(Dictionary<string, object?> data, string method, long elapsed)
        {
            return new SerializationResult
            {
                IsSuccess = true,
                Data = data,
                SerializationMethod = method,
                ElapsedMilliseconds = elapsed
            };
        }

        /// <summary> 建立失敗結果 </summary>
        public static SerializationResult Failure(string errorMessage, string method, long elapsed)
        {
            return new SerializationResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                SerializationMethod = method,
                ElapsedMilliseconds = elapsed
            };
        }
    }
}
