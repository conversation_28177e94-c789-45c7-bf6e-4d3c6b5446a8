# CategoryManagement UI Standardization 文檔

## 概述

CategoryManagement UI Standardization 將 CategoryManagement 組件的 UI 設計更新為與 Supplier/Customer 分類管理組件一致的風格，確保整個 FastERP 系統中分類管理功能的視覺和交互一致性。

## 更新內容

### 🎨 視覺設計統一

#### Modal 配置標準化
```typescript
// 更新前
footer={[
  <Button key="cancel" onClick={onClose}>取消</Button>,
  <Button key="submit" type="primary">關閉</Button>
]}
width={isMobile ? '95%' : 900}
style={isMobile ? { top: 20, margin: '0 auto' } : {}}

// 更新後
footer={null}
width={isMobile ? '95%' : 1000}
style={{ top: 20 }}
```

#### 佈局結構統一
```typescript
// 更新前
<Row gutter={isMobile ? [8, 8] : [24, 16]}>
  <Col xs={24} lg={12}>  // 左側
  <Col xs={24} lg={12}>  // 右側

// 更新後
<Row gutter={16} style={{ height: '70vh' }}>
  <Col span={isMobile ? 24 : 14}>  // 左側
  <Col span={isMobile ? 24 : 10}>  // 右側
```

### 🏗️ 組件結構優化

#### 左側分類列表
```typescript
// 標題結構統一
title={
  <Space>
    <ApartmentOutlined />
    <span>分類結構</span>
    <Badge count={categories.length} style={{ backgroundColor: '#52c41a' }} />
  </Space>
}

// 樣式配置統一
styles={{ body: { padding: '12px', maxHeight: '60vh', overflowY: 'auto' } }}
```

#### 右側操作表單
```typescript
// 標題動態顯示
title={
  <Space>
    <NodeIndexOutlined />
    <span>
      {operationMode === 'edit' ? '編輯分類' : 
       operationMode === 'addChild' ? '新增子分類' : '新增分類'}
    </span>
  </Space>
}

// 操作按鈕統一
extra={
  <Button type="primary" icon={<PlusOutlined />} onClick={handleAddNew} size="small">
    新增分類
  </Button>
}
```

### 🚨 空狀態處理統一

#### Alert 組件替代自定義空狀態
```typescript
// 更新前
{(!categories || categories.length === 0) && (
  <div style={{ textAlign: 'center', padding: '40px 20px', color: '#8c8c8c' }}>
    <div>尚無庫存品分類</div>
    <div>請點擊右側表單新增分類</div>
  </div>
)}

// 更新後
{!categories || categories.length === 0 ? (
  <Alert
    message="尚無分類"
    description="點擊右側「新增分類」按鈕開始建立分類結構。"
    type="info"
    showIcon
  />
) : (
  <div>{/* 分類列表內容 */}</div>
)}
```

## 技術改進

### 代碼簡化
- 移除複雜的 `modeConfig` 配置對象
- 簡化操作模式邏輯
- 統一按鈕文字和樣式

### 響應式優化
- 統一移動端和桌面端的佈局比例
- 優化高度和滾動處理
- 改善觸控設備的操作體驗

### 性能提升
- 移除不必要的樣式計算
- 簡化條件渲染邏輯
- 優化組件重渲染

## 設計原則

### 一致性原則
1. **視覺一致性**: 與 Supplier/Customer 分類管理組件保持相同的視覺風格
2. **交互一致性**: 統一的操作流程和用戶體驗
3. **結構一致性**: 相同的組件佈局和層次結構

### 可用性原則
1. **清晰的視覺層次**: 明確的主次關係和信息法人
2. **直觀的操作流程**: 符合用戶預期的交互模式
3. **有效的狀態反饋**: 清楚的操作結果和狀態提示

### 可維護性原則
1. **代碼簡潔性**: 移除冗餘和複雜的配置
2. **結構清晰性**: 統一的組件結構便於維護
3. **擴展性**: 為未來功能擴展預留空間

## 更新對比

### Modal 配置
| 項目 | 更新前 | 更新後 |
|------|--------|--------|
| Footer | 自定義按鈕 | null |
| Width | 900px | 1000px |
| Style | 複雜條件樣式 | 簡化統一樣式 |

### 佈局配置
| 項目 | 更新前 | 更新後 |
|------|--------|--------|
| Gutter | 動態間距 | 固定 16px |
| Height | 無限制 | 70vh |
| 左側寬度 | 50% (lg=12) | 58% (span=14) |
| 右側寬度 | 50% (lg=12) | 42% (span=10) |

### 卡片配置
| 項目 | 更新前 | 更新後 |
|------|--------|--------|
| 左側標題 | 簡單文字 | 圖標+文字+徽章 |
| 右側標題 | 動態配置 | 動態文字 |
| 內容高度 | 400px | 60vh |
| 空狀態 | 自定義組件 | Alert 組件 |

## 使用方法

### 基本使用
```tsx
<CategoryManagement
  visible={visible}
  onClose={onClose}
  categories={categories}
  categoryTreeData={categoryTreeData}
  sortedCategoriesForDisplay={sortedCategories}
  onDataChange={handleDataChange}
/>
```

### 響應式適配
組件會自動根據螢幕尺寸調整佈局：
- **移動端 (≤768px)**: 垂直堆疊，全寬顯示
- **桌面端 (>768px)**: 水平分割，14:10 比例

### 操作模式
- **新增模式**: 創建新的分類項目
- **編輯模式**: 修改現有分類信息
- **子分類模式**: 為現有分類添加子項目

## 測試建議

### 視覺測試
1. 與 Supplier/Customer 分類管理組件進行視覺對比
2. 驗證在不同螢幕尺寸下的顯示效果
3. 確認圖標、顏色、間距的一致性

### 功能測試
1. 測試所有操作模式的正常運行
2. 驗證空狀態的正確顯示
3. 確認響應式佈局的正確切換

### 兼容性測試
1. 確保與現有功能的兼容性
2. 驗證數據流的正確性
3. 測試錯誤處理的有效性

## 維護指南

### 樣式更新
- 遵循統一的設計規範
- 保持與其他分類管理組件的一致性
- 注意響應式設計的適配

### 功能擴展
- 基於現有結構進行擴展
- 保持組件接口的穩定性
- 考慮向後兼容性

### 問題排查
- 檢查與其他組件的樣式衝突
- 驗證響應式佈局的正確性
- 確認數據綁定的有效性

## 版本歷史

### v2.0.0 (2024-01-07)
- 完全重構 UI 設計，與 Supplier/Customer 組件保持一致
- 簡化組件結構和配置邏輯
- 優化響應式設計和性能表現
- 統一空狀態處理和錯誤提示

### v1.x.x (之前版本)
- 原始設計，使用自定義配置和樣式
- 複雜的模式配置系統
- 不一致的視覺設計
