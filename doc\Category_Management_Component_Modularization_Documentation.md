# Category Management Component Modularization 文檔

## 概述

Category Management Component Modularization 將三個分類管理組件（CategoryManagement、SupplierCategoryManagement、CustomerCategoryManagement）的共同功能抽取為通用的階層分類管理組件，實現代碼重用、降低維護成本，並確保所有分類管理功能的一致性。

## 架構設計

### 🏗️ 組件架構

```
GenericCategoryManagement (核心組件)
├── CustomerCategoryAdapter (客戶分類適配器)
├── SupplierCategoryAdapter (供應商分類適配器)
└── ItemCategoryAdapter (庫存品分類適配器)
```

### 🎯 設計原則

1. **單一職責**: 核心組件專注於通用分類管理邏輯
2. **開放封閉**: 對擴展開放，對修改封閉
3. **依賴倒置**: 依賴抽象介面而非具體實現
4. **適配器模式**: 使用適配器統一不同分類類型的介面

## 核心組件

### GenericCategoryManagement

#### 介面定義
```typescript
// 通用分類介面
export interface GenericCategory {
  id: string;
  name: string;
  description?: string;
  parentID?: string | null;
  sortCode?: number;
  children?: GenericCategory[];
}

// 通用分類服務介面
export interface CategoryService<T extends GenericCategory> {
  add: (category: Partial<T>) => Promise<{ success: boolean; message?: string }>;
  edit: (category: Partial<T>) => Promise<{ success: boolean; message?: string }>;
  delete: (id: string) => Promise<{ success: boolean; message?: string }>;
  buildTree: (categories: T[]) => T[];
}

// 組件配置介面
export interface CategoryConfig {
  title: string;
  icon: React.ReactNode;
  emptyMessage: string;
  emptyDescription: string;
  entityName: string;
}
```

#### 核心功能
- **階層樹狀結構**: 支援無限層級的分類樹
- **CRUD 操作**: 新增、編輯、刪除分類
- **展開/折疊**: 支援單個和全部展開/折疊
- **響應式設計**: 適配移動端和桌面端
- **表單驗證**: 完整的輸入驗證和錯誤處理

## 適配器組件

### CustomerCategoryAdapter

#### 配置
```typescript
const customerCategoryConfig: CategoryConfig = {
  title: '客戶分類管理',
  icon: <ContactsOutlined style={{ color: '#1890ff' }} />,
  emptyMessage: '尚無客戶分類',
  emptyDescription: '點擊右側「新增分類」按鈕開始建立客戶分類結構。',
  entityName: '客戶分類'
};
```

#### 映射函數
```typescript
const mapToGeneric = (category: CustomerCategory): GenericCategory => ({
  id: category.customerCategoryID,
  name: category.name,
  description: category.description,
  parentID: category.parentID,
  sortCode: category.sortCode,
  children: category.children?.map(mapToGeneric)
});
```

### SupplierCategoryAdapter

#### 配置
```typescript
const supplierCategoryConfig: CategoryConfig = {
  title: '供應商分類管理',
  icon: <ShopOutlined style={{ color: '#1890ff' }} />,
  emptyMessage: '尚無供應商分類',
  emptyDescription: '點擊右側「新增分類」按鈕開始建立供應商分類結構。',
  entityName: '供應商分類'
};
```

### ItemCategoryAdapter

#### 配置
```typescript
const itemCategoryConfig: CategoryConfig = {
  title: '庫存品分類管理',
  icon: <ApartmentOutlined style={{ color: '#1890ff' }} />,
  emptyMessage: '尚無庫存品分類',
  emptyDescription: '點擊右側「新增分類」按鈕開始建立庫存品分類結構。',
  entityName: '庫存品分類'
};
```

## 使用方法

### 基本使用

#### 客戶分類管理
```tsx
import CustomerCategoryAdapter from '@/app/ims/components/shared/CustomerCategoryAdapter';

<CustomerCategoryAdapter
  visible={isCustomerCategoryModalVisible}
  onClose={() => setIsCustomerCategoryModalVisible(false)}
  categories={customerCategories}
  onDataChange={loadCustomerCategories}
/>
```

#### 供應商分類管理
```tsx
import SupplierCategoryAdapter from '@/app/ims/components/shared/SupplierCategoryAdapter';

<SupplierCategoryAdapter
  visible={isSupplierCategoryModalVisible}
  onClose={() => setIsSupplierCategoryModalVisible(false)}
  categories={supplierCategories}
  onDataChange={loadSupplierCategories}
/>
```

#### 庫存品分類管理
```tsx
import ItemCategoryAdapter from '@/app/ims/components/shared/ItemCategoryAdapter';

<ItemCategoryAdapter
  visible={isCategoryModalVisible}
  onClose={() => setIsCategoryModalVisible(false)}
  categories={itemCategories}
  categoryTreeData={categoryTreeData}  // 會被忽略，自動生成
  sortedCategoriesForDisplay={sortedCategories}  // 會被忽略，自動生成
  onDataChange={loadItemCategories}
/>
```

### 遷移指南

#### 從舊組件遷移
1. **替換導入**: 將舊的分類管理組件導入替換為對應的適配器
2. **移除多餘 props**: 移除 `categoryTreeData` 和 `sortedCategoriesForDisplay`
3. **保持介面一致**: 其他 props 保持不變

#### 遷移範例
```tsx
// 舊的使用方式
import CustomerCategoryManagement from '@/app/ims/components/CustomerCategoryManagement';

<CustomerCategoryManagement
  visible={visible}
  onClose={onClose}
  categories={categories}
  onDataChange={onDataChange}
/>

// 新的使用方式
import CustomerCategoryAdapter from '@/app/ims/components/shared/CustomerCategoryAdapter';

<CustomerCategoryAdapter
  visible={visible}
  onClose={onClose}
  categories={categories}
  onDataChange={onDataChange}
/>
```

## 技術優勢

### 代碼重用
- **減少重複代碼**: 三個組件共用 90% 的邏輯
- **統一維護**: 核心邏輯只需在一個地方維護
- **一致性保證**: 所有分類管理功能行為一致

### 可擴展性
- **新分類類型**: 只需創建新的適配器組件
- **功能擴展**: 在核心組件中添加功能，所有適配器自動受益
- **自定義配置**: 通過配置對象輕鬆自定義外觀和行為

### 類型安全
- **完整的 TypeScript 支援**: 所有介面都有完整的類型定義
- **泛型設計**: 支援不同類型的分類實體
- **編譯時檢查**: 在編譯時發現類型錯誤

## 性能優化

### 記憶化處理
- **useMemo**: 優化樹狀資料和排序計算
- **useCallback**: 優化事件處理函數
- **條件渲染**: 避免不必要的組件重渲染

### 虛擬化支援
- **大數據集**: 支援大量分類數據的高效渲染
- **滾動優化**: 使用虛擬滾動處理長列表
- **懶加載**: 支援分類數據的懶加載

## 測試策略

### 單元測試
```typescript
// 測試核心組件
describe('GenericCategoryManagement', () => {
  it('should render category tree correctly', () => {
    // 測試樹狀結構渲染
  });
  
  it('should handle CRUD operations', () => {
    // 測試增刪改查操作
  });
});

// 測試適配器
describe('CustomerCategoryAdapter', () => {
  it('should map data correctly', () => {
    // 測試數據映射
  });
});
```

### 整合測試
- **API 整合**: 測試與後端 API 的整合
- **用戶交互**: 測試完整的用戶操作流程
- **錯誤處理**: 測試各種錯誤情況的處理

## 維護指南

### 添加新分類類型
1. **創建適配器**: 基於現有適配器創建新的適配器組件
2. **實現服務**: 實現 `CategoryService` 介面
3. **配置對象**: 定義 `CategoryConfig` 配置
4. **映射函數**: 實現數據映射函數

### 擴展功能
1. **核心功能**: 在 `GenericCategoryManagement` 中添加新功能
2. **配置選項**: 通過 `CategoryConfig` 添加可配置選項
3. **向後兼容**: 確保新功能不破壞現有適配器

### 問題排查
1. **類型錯誤**: 檢查映射函數的類型定義
2. **數據不一致**: 驗證服務介面的實現
3. **渲染問題**: 檢查配置對象的正確性

## 版本歷史

### v1.0.0 (2024-01-07)
- 初始版本發布
- 支援客戶、供應商、庫存品三種分類類型
- 完整的 CRUD 功能和樹狀結構管理
- 響應式設計和移動端適配
- 完整的 TypeScript 類型支援

## 未來規劃

### 短期目標
- 添加拖拽排序功能
- 支援批量操作
- 增強搜尋和篩選功能

### 長期目標
- 支援更多分類類型
- 添加分類模板功能
- 實現分類數據的匯入匯出
