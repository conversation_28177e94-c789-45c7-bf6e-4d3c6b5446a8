using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("開支部門異動資料管理")]
    public class ExpenseDepartmentChangeController : ControllerBase
    {
        private readonly IExpenseDepartmentChangeService _expenseDepartmentChangeService;

        public ExpenseDepartmentChangeController(IExpenseDepartmentChangeService expenseDepartmentChangeService)
        {
            _expenseDepartmentChangeService = expenseDepartmentChangeService;
        }

        [HttpGet]
        [Route("GetAll/{userId}")]
        [SwaggerOperation(Summary = "取得開支部門異動列表", Description = "取得指定使用者的所有開支部門異動資料列表")]
        public async Task<IActionResult> GetExpenseDepartmentChangeList(string userId)
        {
            try
            {
                var result = await _expenseDepartmentChangeService.GetExpenseDepartmentChangeListAsync(userId);
                return Ok(new { success = true, message = "取得開支部門異動列表成功", data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpGet]
        [Route("Get/{uid}")]
        [SwaggerOperation(Summary = "取得開支部門異動明細", Description = "依開支部門異動編號取得明細資料")]
        public async Task<IActionResult> GetExpenseDepartmentChangeDetail(string uid)
        {
            try
            {
                var result = await _expenseDepartmentChangeService.GetExpenseDepartmentChangeDetailAsync(uid);
                if (result == null)
                {
                    return NotFound(new { success = false, message = "找不到指定的開支部門異動資料" });
                }
                return Ok(new { success = true, message = "取得開支部門異動明細成功", data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增開支部門異動", Description = "新增開支部門異動資料")]
        public async Task<IActionResult> AddExpenseDepartmentChange([FromForm] ExpenseDepartmentChangeFormDTO formData)
        {
            try
            {
                var changeData = MapFormToDTO(formData);
                var uid = await _expenseDepartmentChangeService.AddExpenseDepartmentChangeAsync(changeData, formData.UserId ?? "");
                return Ok(new { success = !string.IsNullOrEmpty(uid), message = !string.IsNullOrEmpty(uid) ? "新增成功" : "新增失敗", data = new { uid } });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯開支部門異動", Description = "編輯開支部門異動資料")]
        public async Task<IActionResult> EditExpenseDepartmentChange([FromForm] ExpenseDepartmentChangeFormDTO formData)
        {
            try
            {
                var changeData = MapFormToDTO(formData);
                await _expenseDepartmentChangeService.UpdateExpenseDepartmentChangeAsync(changeData, formData.Uid ?? "");
                return Ok(new { success = true, message = "更新成功", data = (object?)null });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除開支部門異動", Description = "刪除開支部門異動資料")]
        public async Task<IActionResult> DeleteExpenseDepartmentChange([FromBody] string uid)
        {
            try
            {
                await _expenseDepartmentChangeService.DeleteExpenseDepartmentChangeAsync(uid);
                return Ok(new { success = true, message = "刪除成功", data = (object?)null });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        #region 私有方法

        private ExpenseDepartmentChangeDTO MapFormToDTO(ExpenseDepartmentChangeFormDTO formData)
        {
            return new ExpenseDepartmentChangeDTO
            {
                ExpenseDepartmentId = formData.ExpenseDepartmentId ?? "",
                ChangeDate = formData.ChangeDate ?? "",
                EffectiveDate = formData.EffectiveDate ?? "",
                ChangeReason = formData.ChangeReason ?? "",
                Remark = formData.Remark ?? ""
            };
        }

        #endregion
    }

    /// <summary>
    /// 開支部門異動表單DTO
    /// </summary>
    public class ExpenseDepartmentChangeFormDTO
    {
        public string? Uid { get; set; }
        public string? UserId { get; set; }
        public string? ExpenseDepartmentId { get; set; }
        public string? ChangeDate { get; set; }
        public string? EffectiveDate { get; set; }
        public string? ChangeReason { get; set; }
        public string? Remark { get; set; }
    }
}