/**
 * FastERP 簡化 MongoDB 日誌測試腳本
 * 用於驗證新的簡化日誌格式是否正常工作
 * 
 * 注意: 這是測試代碼，完成驗證後應移除
 */

const baseUrl = 'http://localhost:7136/api';

// 測試新增 Item 記錄
async function testCreateItem() {
    console.log('🧪 測試新增 Item 記錄...');
    
    try {
        const newItem = {
            customNO: `TEST-${Date.now()}`,
            name: '測試庫存品-簡化日誌',
            unit: '個',
            description: '測試簡化日誌格式',
            taxType: 'Taxable'
        };

        const response = await fetch(`${baseUrl}/Item`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(newItem)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Item 新增成功');
            console.log(`   Item ID: ${result.itemID}`);
            return result.itemID;
        } else {
            console.log('❌ Item 新增失敗:', result.message || result);
            return null;
        }
    } catch (error) {
        console.log('❌ Item 新增異常:', error.message);
        return null;
    }
}

// 測試修改 Item 記錄
async function testUpdateItem(itemId) {
    if (!itemId) return false;
    
    console.log('🧪 測試修改 Item 記錄...');
    
    try {
        const updateData = {
            itemID: itemId,
            name: '測試庫存品-簡化日誌-已修改',
            description: '測試簡化日誌格式 - 修改版本',
            unit: '組'
        };

        const response = await fetch(`${baseUrl}/Item`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        });
        
        if (response.ok) {
            console.log('✅ Item 修改成功');
            return true;
        } else {
            const result = await response.json();
            console.log('❌ Item 修改失敗:', result.message || result);
            return false;
        }
    } catch (error) {
        console.log('❌ Item 修改異常:', error.message);
        return false;
    }
}

// 測試刪除 Item 記錄
async function testDeleteItem(itemId) {
    if (!itemId) return false;
    
    console.log('🧪 測試刪除 Item 記錄...');
    
    try {
        const response = await fetch(`${baseUrl}/Item/${itemId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            console.log('✅ Item 刪除成功');
            return true;
        } else {
            const result = await response.json();
            console.log('❌ Item 刪除失敗:', result.message || result);
            return false;
        }
    } catch (error) {
        console.log('❌ Item 刪除異常:', error.message);
        return false;
    }
}

// 檢查 MongoDB 日誌格式
async function checkMongoDBLogs() {
    console.log('📊 檢查 MongoDB 日誌格式...');
    console.log('');
    console.log('請手動檢查 MongoDB 中的日誌記錄，確認格式是否簡化：');
    console.log('');
    console.log('期望的簡化格式應該包含：');
    console.log('1. 乾淨的 JSON 結構，沒有 _t 和 _v 標記');
    console.log('2. 簡化的屬性名稱 (camelCase)');
    console.log('3. 格式化的日期時間字串');
    console.log('4. 只記錄關鍵屬性和變更資訊');
    console.log('5. 避免深層嵌套結構');
    console.log('');
    console.log('範例簡化格式：');
    console.log(JSON.stringify({
        transactionId: "example-transaction-id",
        source: "ERPDbContext",
        changeTime: "2025-07-11 16:30:00",
        totalChanges: 1,
        entities: [{
            entityType: "Item",
            entityId: "example-item-id",
            entityState: "Modified",
            userId: "example-user-id",
            timestamp: "2025-07-11 16:30:00",
            data: {
                changes: {
                    name: {
                        from: "舊名稱",
                        to: "新名稱"
                    },
                    description: {
                        from: "舊描述",
                        to: "新描述"
                    }
                }
            }
        }]
    }, null, 2));
}

// 主測試函數
async function runSimplifiedLoggingTest() {
    console.log('🚀 開始 FastERP 簡化 MongoDB 日誌測試');
    console.log('=' .repeat(60));
    
    let itemId = null;
    let testsPassed = 0;
    let totalTests = 3;
    
    // 測試新增
    itemId = await testCreateItem();
    if (itemId) testsPassed++;
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 測試修改
    const updateSuccess = await testUpdateItem(itemId);
    if (updateSuccess) testsPassed++;
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 測試刪除
    const deleteSuccess = await testDeleteItem(itemId);
    if (deleteSuccess) testsPassed++;
    
    console.log('');
    console.log('=' .repeat(60));
    console.log(`📊 API 測試結果: ${testsPassed}/${totalTests} 通過`);
    
    if (testsPassed === totalTests) {
        console.log('🎉 所有 API 測試通過！');
    } else {
        console.log('⚠️  部分 API 測試失敗');
    }
    
    console.log('');
    checkMongoDBLogs();
    
    console.log('');
    console.log('💡 下一步：');
    console.log('   1. 檢查 MongoDB Logger 集合中的最新記錄');
    console.log('   2. 確認日誌格式是否簡化且易讀');
    console.log('   3. 驗證沒有複雜的 _t 和 _v 嵌套結構');
    console.log('   4. 確認只記錄了必要的變更資訊');
}

// 執行測試
if (typeof window === 'undefined') {
    // Node.js 環境
    const fetch = require('node-fetch');
    runSimplifiedLoggingTest().catch(console.error);
} else {
    // 瀏覽器環境
    runSimplifiedLoggingTest().catch(console.error);
}
