using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Models.Common.Logging;
using System;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Controllers.Common
{
    /// <summary>
    /// 日誌測試控制器 - 用於驗證新的日誌架構
    /// 注意: 這是測試代碼，生產環境中應移除
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class LoggingTestController : ControllerBase
    {
        private readonly ILoggerService _logger;

        public LoggingTestController(ILoggerService logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 測試基本日誌記錄功能
        /// </summary>
        [HttpPost("test-basic-logging")]
        public async Task<IActionResult> TestBasicLogging()
        {
            try
            {
                var transactionId = Guid.NewGuid().ToString();

                // 測試各種日誌級別
                await _logger.LogInfoAsync("測試資訊日誌", "LoggingTestController");
                await _logger.LogWarningAsync("測試警告日誌", "LoggingTestController");
                await _logger.LogErrorAsync("測試錯誤日誌", new Exception("測試異常"), "LoggingTestController");

                return Ok(new { success = true, message = "基本日誌測試完成", transactionId });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 測試實體序列化功能
        /// </summary>
        [HttpPost("test-entity-serialization")]
        public async Task<IActionResult> TestEntitySerialization()
        {
            try
            {
                var transactionId = Guid.NewGuid().ToString();

                // 建立測試 Partner 實體 (包含複雜的導航屬性)
                var testPartner = new Partner
                {
                    PartnerID = Guid.NewGuid(),
                    IsStop = false,
                    CreateTime = DateTime.UtcNow,
                    CreateUserId = "TestUser",
                    UpdateTime = DateTime.UtcNow,
                    UpdateUserId = "TestUser",
                    // 故意不設定導航屬性，避免實際的循環引用
                    IndividualDetail = null,
                    EnterpriseDetail = null,
                    CustomerDetail = null,
                    SupplierDetail = null
                };

                // 測試資料序列化
                await _logger.LogDataAsync("測試 Partner 實體序列化", testPartner, transactionId, "LoggingTestController");

                // 測試複雜物件序列化
                var complexObject = new
                {
                    TestId = Guid.NewGuid(),
                    TestName = "複雜物件測試",
                    TestData = new
                    {
                        NestedProperty = "嵌套屬性",
                        NestedNumber = 12345,
                        NestedDate = DateTime.UtcNow
                    },
                    TestArray = new[] { "項目1", "項目2", "項目3" }
                };

                await _logger.LogDataAsync("測試複雜物件序列化", complexObject, transactionId, "LoggingTestController");

                return Ok(new { success = true, message = "實體序列化測試完成", transactionId });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 測試序列化策略
        /// </summary>
        [HttpPost("test-serialization-strategies")]
        public async Task<IActionResult> TestSerializationStrategies()
        {
            try
            {
                var results = new List<object>();

                // 測試 null 物件
                var nullResult = SafeBsonSerializer.SafeSerialize(null);
                results.Add(new { test = "null", result = nullResult });

                // 測試簡單物件
                var simpleObject = new { Name = "測試", Value = 123 };
                var simpleResult = SafeBsonSerializer.SafeSerialize(simpleObject);
                results.Add(new { test = "simple", result = simpleResult });

                // 測試 EntityLoggingDTO
                var entityDto = new EntityLoggingDTO
                {
                    EntityType = "TestEntity",
                    EntityId = Guid.NewGuid().ToString(),
                    EntityState = "Added",
                    Properties = new Dictionary<string, object?>
                    {
                        { "TestProperty", "測試值" },
                        { "TestNumber", 456 }
                    }
                };
                var entityResult = SafeBsonSerializer.SafeSerialize(entityDto);
                results.Add(new { test = "entityDto", result = entityResult });

                // 測試大型物件 (觸發限制機制)
                var largeObject = new Dictionary<string, object>();
                for (int i = 0; i < 100; i++)
                {
                    largeObject[$"Property{i}"] = $"Value{i}";
                }
                var largeResult = SafeBsonSerializer.SafeSerialize(largeObject);
                results.Add(new { test = "large", result = largeResult });

                return Ok(new { success = true, message = "序列化策略測試完成", results });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 測試資料提取器功能
        /// </summary>
        [HttpPost("test-data-extractor")]
        public async Task<IActionResult> TestDataExtractor()
        {
            try
            {
                // 建立測試實體
                var testEntity = new Partner
                {
                    PartnerID = Guid.NewGuid(),
                    IsStop = false,
                    CreateTime = DateTime.UtcNow,
                    CreateUserId = "TestUser"
                };

                // 測試安全屬性提取
                var safeProperties = LoggingDataExtractor.ExtractSafeProperties(testEntity);

                // 測試複雜物件提取
                var complexEntity = new
                {
                    Id = Guid.NewGuid(),
                    Name = "測試實體",
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true,
                    // 這些應該被過濾掉
                    NavigationProperty = new Partner(),
                    Collection = new List<Partner>()
                };

                var complexProperties = LoggingDataExtractor.ExtractSafeProperties(complexEntity);

                var result = new
                {
                    testEntity = new
                    {
                        type = testEntity.GetType().Name,
                        extractedProperties = safeProperties,
                        propertyCount = safeProperties.Count
                    },
                    complexEntity = new
                    {
                        type = complexEntity.GetType().Name,
                        extractedProperties = complexProperties,
                        propertyCount = complexProperties.Count
                    }
                };

                return Ok(new { success = true, message = "資料提取器測試完成", result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }
    }
}
