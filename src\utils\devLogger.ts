/**
 * 開發環境專用日誌工具 - 統一日誌系統
 *
 * 提供全局的console.log包裝器，只在開發環境中輸出日誌
 * 生產環境中會自動禁用所有console輸出
 *
 * 特色功能：
 * - 統一的錯誤符號和格式化
 * - 自動時間戳記
 * - 模組化日誌分類
 * - 一致的視覺呈現
 *
 * <AUTHOR> Team
 * @version 2.0.0 - 統一日誌系統重構版本
 */

/**
 * 檢查是否為開發環境
 */
const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

/**
 * 日誌符號常數 - 統一視覺呈現
 */
export const LOG_SYMBOLS = {
  SUCCESS: '✅',
  ERROR: '❌',
  WARNING: '⚠️',
  INFO: 'ℹ️',
  DEBUG: '🔍',
  LOADING: '🔄',
  ROCKET: '🚀',
  PAGE: '📄',
  CHART: '📊',
  TAG: '🏷️',
  WAVE: '👋',
  EYES: '👀',
  TOOLS: '🛠️'
} as const;

/**
 * 開發環境專用console.log包裝器
 * 只在開發環境中輸出日誌，生產環境中不會有任何輸出
 * 自動簡化複雜物件，避免過於冗長的日誌輸出
 *
 * @param args - 要輸出的參數
 *
 * @example
 * ```typescript
 * import { devLog } from '@/utils/devLogger';
 *
 * devLog('這是開發環境的日誌');
 * devLog('用戶資料:', userData);
 * devLog('API回應:', response, '狀態:', status);
 * ```
 */
export const devLog = (...args: any[]): void => {
  if (isDevelopment()) {
    const simplifiedArgs = args.map(arg => simplifyLogData(arg));
    console.log(...simplifiedArgs);
  }
};

/**
 * 成功日誌 - 使用統一的成功符號
 * @param message - 主要訊息
 * @param data - 可選的額外資料
 */
export const devSuccess = (message: string, ...data: any[]): void => {
  if (isDevelopment()) {
    console.log(`${LOG_SYMBOLS.SUCCESS} ${message}`, ...data);
  }
};

/**
 * 載入中日誌 - 使用統一的載入符號
 * @param message - 主要訊息
 * @param data - 可選的額外資料
 */
export const devLoading = (message: string, ...data: any[]): void => {
  if (isDevelopment()) {
    console.log(`${LOG_SYMBOLS.LOADING} ${message}`, ...data);
  }
};

/**
 * 資訊日誌 - 使用統一的資訊符號
 * @param message - 主要訊息
 * @param data - 可選的額外資料
 */
export const devInfo = (message: string, ...data: any[]): void => {
  if (isDevelopment()) {
    console.log(`${LOG_SYMBOLS.INFO} ${message}`, ...data);
  }
};

/**
 * 除錯日誌 - 使用統一的除錯符號
 * @param message - 主要訊息
 * @param data - 可選的額外資料
 */
export const devDebug = (message: string, ...data: any[]): void => {
  if (isDevelopment()) {
    console.log(`${LOG_SYMBOLS.DEBUG} ${message}`, ...data);
  }
};

/**
 * 開發環境專用console.error包裝器
 * 只在開發環境中輸出錯誤日誌，生產環境中不會有任何輸出
 * 
 * @param args - 要輸出的參數
 * 
 * @example
 * ```typescript
 * import { devError } from '@/utils/devLogger';
 * 
 * devError('API錯誤:', error);
 * devError('表單驗證失敗:', validationErrors);
 * ```
 */
export const devError = (...args: any[]): void => {
  if (isDevelopment()) {
    console.error(...args);
  }
};

/**
 * 開發環境專用console.warn包裝器
 * 只在開發環境中輸出警告日誌，生產環境中不會有任何輸出
 * 
 * @param args - 要輸出的參數
 * 
 * @example
 * ```typescript
 * import { devWarn } from '@/utils/devLogger';
 * 
 * devWarn('已棄用的API:', apiName);
 * devWarn('性能警告:', performanceData);
 * ```
 */
export const devWarn = (...args: any[]): void => {
  if (isDevelopment()) {
    console.warn(...args);
  }
};





/**
 * 開發環境專用console.table包裝器
 * 只在開發環境中輸出表格日誌，生產環境中不會有任何輸出
 * 
 * @param data - 要以表格形式顯示的資料
 * @param columns - 可選的欄位名稱
 * 
 * @example
 * ```typescript
 * import { devTable } from '@/utils/devLogger';
 * 
 * devTable(users);
 * devTable(products, ['name', 'price', 'category']);
 * ```
 */
export const devTable = (data: any, columns?: string[]): void => {
  if (isDevelopment()) {
    console.table(data, columns);
  }
};

/**
 * 開發環境專用console.group包裝器
 * 只在開發環境中創建日誌群組，生產環境中不會有任何輸出
 * 
 * @param label - 群組標籤
 * 
 * @example
 * ```typescript
 * import { devGroup, devGroupEnd, devLog } from '@/utils/devLogger';
 * 
 * devGroup('API呼叫');
 * devLog('請求URL:', url);
 * devLog('請求參數:', params);
 * devGroupEnd();
 * ```
 */
export const devGroup = (label?: string): void => {
  if (isDevelopment()) {
    console.group(label);
  }
};

/**
 * 開發環境專用console.groupEnd包裝器
 * 只在開發環境中結束日誌群組，生產環境中不會有任何輸出
 */
export const devGroupEnd = (): void => {
  if (isDevelopment()) {
    console.groupEnd();
  }
};

/**
 * 開發環境專用console.time包裝器
 * 只在開發環境中開始計時，生產環境中不會有任何輸出
 * 
 * @param label - 計時器標籤
 * 
 * @example
 * ```typescript
 * import { devTime, devTimeEnd } from '@/utils/devLogger';
 * 
 * devTime('API呼叫');
 * // ... 執行API呼叫
 * devTimeEnd('API呼叫');
 * ```
 */
export const devTime = (label?: string): void => {
  if (isDevelopment()) {
    console.time(label);
  }
};

/**
 * 開發環境專用console.timeEnd包裝器
 * 只在開發環境中結束計時，生產環境中不會有任何輸出
 * 
 * @param label - 計時器標籤
 */
export const devTimeEnd = (label?: string): void => {
  if (isDevelopment()) {
    console.timeEnd(label);
  }
};

/**
 * 簡化日誌資料，避免過於複雜的物件輸出
 * @param data - 要簡化的資料
 * @returns 簡化後的資料
 */
const simplifyLogData = (data: any): any => {
  if (data === null || data === undefined) {
    return data;
  }

  // 如果是字串、數字、布林值，直接返回
  if (typeof data === 'string' || typeof data === 'number' || typeof data === 'boolean') {
    return data;
  }

  // 如果是陣列
  if (Array.isArray(data)) {
    // 如果陣列太長，只顯示前幾個元素
    if (data.length > 5) {
      return [
        ...data.slice(0, 3).map(item => simplifyLogData(item)),
        `... 還有 ${data.length - 3} 個項目`
      ];
    }
    return data.map(item => simplifyLogData(item));
  }

  // 如果是物件
  if (typeof data === 'object') {
    const keys = Object.keys(data);

    // 如果屬性太多，只顯示重要的屬性
    if (keys.length > 8) {
      const importantKeys = keys.filter(key =>
        key.toLowerCase().includes('id') ||
        key.toLowerCase().includes('name') ||
        key.toLowerCase().includes('type') ||
        key.toLowerCase().includes('status') ||
        key.toLowerCase().includes('state') ||
        key.toLowerCase().includes('success') ||
        key.toLowerCase().includes('message') ||
        key.toLowerCase().includes('error')
      ).slice(0, 5);

      const simplified: any = {};
      importantKeys.forEach(key => {
        simplified[key] = simplifyLogData(data[key]);
      });

      if (keys.length > importantKeys.length) {
        simplified['...'] = `還有 ${keys.length - importantKeys.length} 個屬性`;
      }

      return simplified;
    }

    // 遞迴簡化物件屬性
    const simplified: any = {};
    keys.forEach(key => {
      simplified[key] = simplifyLogData(data[key]);
    });
    return simplified;
  }

  return data;
};

/**
 * 簡化的資料轉換日誌 - 專門用於記錄資料轉換過程
 * @param message - 日誌訊息
 * @param data - 要記錄的資料
 */
export const devLogDataTransform = (message: string, data: any): void => {
  if (isDevelopment()) {
    const simplified = {
      count: Array.isArray(data) ? data.length : (data ? 1 : 0),
      sample: Array.isArray(data) && data.length > 0 ? simplifyLogData(data[0]) : simplifyLogData(data),
      type: Array.isArray(data) ? 'array' : typeof data
    };
    console.log(`${LOG_SYMBOLS.DEBUG} ${message}`, simplified);
  }
};

/**
 * 簡化的 API 回應日誌 - 專門用於記錄 API 回應
 * @param message - 日誌訊息
 * @param response - API 回應資料
 */
export const devLogApiResponse = (message: string, response: any): void => {
  if (isDevelopment()) {
    const simplified = {
      success: response?.success,
      message: response?.message,
      dataCount: Array.isArray(response?.data) ? response.data.length : (response?.data ? 1 : 0),
      status: response?.status || response?.statusCode
    };
    console.log(`${LOG_SYMBOLS.INFO} ${message}`, simplified);
  }
};

/**
 * 預設導出devLog作為主要的日誌函數
 * 這樣可以簡化導入：import devLog from '@/utils/devLogger'
 */
export default devLog;
