/**
 * 開發環境專用日誌工具 - 統一日誌系統
 *
 * 提供全局的console.log包裝器，只在開發環境中輸出日誌
 * 生產環境中會自動禁用所有console輸出
 *
 * 特色功能：
 * - 統一的錯誤符號和格式化
 * - 自動時間戳記
 * - 模組化日誌分類
 * - 一致的視覺呈現
 *
 * <AUTHOR> Team
 * @version 2.0.0 - 統一日誌系統重構版本
 */

/**
 * 檢查是否為開發環境
 */
const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

/**
 * 日誌符號常數 - 統一視覺呈現
 */
export const LOG_SYMBOLS = {
  SUCCESS: '✅',
  ERROR: '❌',
  WARNING: '⚠️',
  INFO: 'ℹ️',
  DEBUG: '🔍',
  LOADING: '🔄',
  ROCKET: '🚀',
  PAGE: '📄',
  CHART: '📊',
  TAG: '🏷️',
  WAVE: '👋',
  EYES: '👀',
  TOOLS: '🛠️'
} as const;

/**
 * 開發環境專用console.log包裝器
 * 只在開發環境中輸出日誌，生產環境中不會有任何輸出
 *
 * @param args - 要輸出的參數
 *
 * @example
 * ```typescript
 * import { devLog } from '@/utils/devLogger';
 *
 * devLog('這是開發環境的日誌');
 * devLog('用戶資料:', userData);
 * devLog('API回應:', response, '狀態:', status);
 * ```
 */
export const devLog = (...args: any[]): void => {
  if (isDevelopment()) {
    console.log(...args);
  }
};

/**
 * 成功日誌 - 使用統一的成功符號
 * @param message - 主要訊息
 * @param data - 可選的額外資料
 */
export const devSuccess = (message: string, ...data: any[]): void => {
  if (isDevelopment()) {
    console.log(`${LOG_SYMBOLS.SUCCESS} ${message}`, ...data);
  }
};

/**
 * 載入中日誌 - 使用統一的載入符號
 * @param message - 主要訊息
 * @param data - 可選的額外資料
 */
export const devLoading = (message: string, ...data: any[]): void => {
  if (isDevelopment()) {
    console.log(`${LOG_SYMBOLS.LOADING} ${message}`, ...data);
  }
};

/**
 * 資訊日誌 - 使用統一的資訊符號
 * @param message - 主要訊息
 * @param data - 可選的額外資料
 */
export const devInfo = (message: string, ...data: any[]): void => {
  if (isDevelopment()) {
    console.log(`${LOG_SYMBOLS.INFO} ${message}`, ...data);
  }
};

/**
 * 除錯日誌 - 使用統一的除錯符號
 * @param message - 主要訊息
 * @param data - 可選的額外資料
 */
export const devDebug = (message: string, ...data: any[]): void => {
  if (isDevelopment()) {
    console.log(`${LOG_SYMBOLS.DEBUG} ${message}`, ...data);
  }
};

/**
 * 開發環境專用console.error包裝器
 * 只在開發環境中輸出錯誤日誌，生產環境中不會有任何輸出
 * 
 * @param args - 要輸出的參數
 * 
 * @example
 * ```typescript
 * import { devError } from '@/utils/devLogger';
 * 
 * devError('API錯誤:', error);
 * devError('表單驗證失敗:', validationErrors);
 * ```
 */
export const devError = (...args: any[]): void => {
  if (isDevelopment()) {
    console.error(...args);
  }
};

/**
 * 開發環境專用console.warn包裝器
 * 只在開發環境中輸出警告日誌，生產環境中不會有任何輸出
 * 
 * @param args - 要輸出的參數
 * 
 * @example
 * ```typescript
 * import { devWarn } from '@/utils/devLogger';
 * 
 * devWarn('已棄用的API:', apiName);
 * devWarn('性能警告:', performanceData);
 * ```
 */
export const devWarn = (...args: any[]): void => {
  if (isDevelopment()) {
    console.warn(...args);
  }
};





/**
 * 開發環境專用console.table包裝器
 * 只在開發環境中輸出表格日誌，生產環境中不會有任何輸出
 * 
 * @param data - 要以表格形式顯示的資料
 * @param columns - 可選的欄位名稱
 * 
 * @example
 * ```typescript
 * import { devTable } from '@/utils/devLogger';
 * 
 * devTable(users);
 * devTable(products, ['name', 'price', 'category']);
 * ```
 */
export const devTable = (data: any, columns?: string[]): void => {
  if (isDevelopment()) {
    console.table(data, columns);
  }
};

/**
 * 開發環境專用console.group包裝器
 * 只在開發環境中創建日誌群組，生產環境中不會有任何輸出
 * 
 * @param label - 群組標籤
 * 
 * @example
 * ```typescript
 * import { devGroup, devGroupEnd, devLog } from '@/utils/devLogger';
 * 
 * devGroup('API呼叫');
 * devLog('請求URL:', url);
 * devLog('請求參數:', params);
 * devGroupEnd();
 * ```
 */
export const devGroup = (label?: string): void => {
  if (isDevelopment()) {
    console.group(label);
  }
};

/**
 * 開發環境專用console.groupEnd包裝器
 * 只在開發環境中結束日誌群組，生產環境中不會有任何輸出
 */
export const devGroupEnd = (): void => {
  if (isDevelopment()) {
    console.groupEnd();
  }
};

/**
 * 開發環境專用console.time包裝器
 * 只在開發環境中開始計時，生產環境中不會有任何輸出
 * 
 * @param label - 計時器標籤
 * 
 * @example
 * ```typescript
 * import { devTime, devTimeEnd } from '@/utils/devLogger';
 * 
 * devTime('API呼叫');
 * // ... 執行API呼叫
 * devTimeEnd('API呼叫');
 * ```
 */
export const devTime = (label?: string): void => {
  if (isDevelopment()) {
    console.time(label);
  }
};

/**
 * 開發環境專用console.timeEnd包裝器
 * 只在開發環境中結束計時，生產環境中不會有任何輸出
 * 
 * @param label - 計時器標籤
 */
export const devTimeEnd = (label?: string): void => {
  if (isDevelopment()) {
    console.timeEnd(label);
  }
};

/**
 * 預設導出devLog作為主要的日誌函數
 * 這樣可以簡化導入：import devLog from '@/utils/devLogger'
 */
export default devLog;
