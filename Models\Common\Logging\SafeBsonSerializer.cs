using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.Json;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using FAST_ERP_Backend.Models.Common.Logging;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 安全 BSON 序列化器
    /// 提供多層次的序列化策略，確保日誌記錄不會因序列化失敗而中斷
    /// </summary>
    public static class SafeBsonSerializer
    {
        #region 靜態建構子和初始化

        /// <summary> 是否已初始化 </summary>
        private static bool _isInitialized = false;

        /// <summary> 初始化鎖 </summary>
        private static readonly object _initLock = new object();

        /// <summary>
        /// 初始化 BSON 序列化設定
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;

            lock (_initLock)
            {
                if (_isInitialized) return;

                try
                {
                    // 註冊序列化約定
                    RegisterSerializationConventions();

                    // 註冊自定義序列化器
                    RegisterCustomSerializers();

                    _isInitialized = true;
                }
                catch (Exception ex)
                {
                    // 初始化失敗不應該影響應用程式啟動
                    Console.WriteLine($"[SafeBsonSerializer] 初始化失敗: {ex.Message}");
                }
            }
        }

        /// <summary> 註冊序列化約定 </summary>
        private static void RegisterSerializationConventions()
        {
            var conventionPack = new ConventionPack
            {
                new CamelCaseElementNameConvention(),
                new IgnoreExtraElementsConvention(true),
                new IgnoreIfNullConvention(true),
                new EnumRepresentationConvention(BsonType.String)
            };

            ConventionRegistry.Register("FastERPLoggingConventions", conventionPack, t => true);
        }

        /// <summary> 註冊自定義序列化器 </summary>
        private static void RegisterCustomSerializers()
        {
            try
            {
                // Guid 序列化為字串
                BsonSerializer.RegisterSerializer(typeof(Guid), new MongoDB.Bson.Serialization.Serializers.GuidSerializer(BsonType.String));
                BsonSerializer.RegisterSerializer(typeof(Guid?), new MongoDB.Bson.Serialization.Serializers.NullableSerializer<Guid>(new MongoDB.Bson.Serialization.Serializers.GuidSerializer(BsonType.String)));
            }
            catch (BsonSerializationException)
            {
                // 序列化器已經註冊，忽略錯誤
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SafeBsonSerializer] 註冊自定義序列化器失敗: {ex.Message}");
            }
        }

        #endregion

        #region 公開序列化方法

        /// <summary>
        /// 安全序列化物件為字典
        /// 使用多層次策略確保序列化成功
        /// </summary>
        /// <param name="obj">要序列化的物件</param>
        /// <returns>序列化結果</returns>
        public static SerializationResult SafeSerialize(object? obj)
        {
            if (obj == null)
            {
                return SerializationResult.Success(new Dictionary<string, object?>(), "Null", 0);
            }

            var stopwatch = Stopwatch.StartNew();

            // 策略 1: 嘗試直接 BSON 序列化
            var bsonResult = TryBsonSerialization(obj);
            if (bsonResult.IsSuccess)
            {
                stopwatch.Stop();
                bsonResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                return bsonResult;
            }

            // 策略 2: 嘗試使用 LoggingDataExtractor
            var extractorResult = TryExtractorSerialization(obj);
            if (extractorResult.IsSuccess)
            {
                stopwatch.Stop();
                extractorResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                return extractorResult;
            }

            // 策略 3: 嘗試 JSON 序列化
            var jsonResult = TryJsonSerialization(obj);
            if (jsonResult.IsSuccess)
            {
                stopwatch.Stop();
                jsonResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                return jsonResult;
            }

            // 策略 4: 基本資訊序列化 (最後手段)
            stopwatch.Stop();
            return CreateBasicInfo(obj, stopwatch.ElapsedMilliseconds);
        }

        /// <summary>
        /// 序列化實體變更日誌
        /// </summary>
        /// <param name="changeLog">變更日誌</param>
        /// <returns>序列化結果</returns>
        public static SerializationResult SerializeChangeLog(EntityChangeLogDTO changeLog)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                var result = new Dictionary<string, object?>
                {
                    ["TransactionId"] = changeLog.TransactionId,
                    ["Source"] = changeLog.Source,
                    ["ChangeTime"] = changeLog.ChangeTime,
                    ["TotalChanges"] = changeLog.TotalChanges,
                    ["ChangedEntities"] = SerializeEntityList(changeLog.ChangedEntities)
                };

                stopwatch.Stop();
                return SerializationResult.Success(result, "ChangeLog", stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return SerializationResult.Failure($"變更日誌序列化失敗: {ex.Message}", "ChangeLog", stopwatch.ElapsedMilliseconds);
            }
        }

        #endregion

        #region 私有序列化策略

        /// <summary> 嘗試 BSON 序列化 </summary>
        private static SerializationResult TryBsonSerialization(object obj)
        {
            try
            {
                Initialize(); // 確保已初始化

                // 將物件轉換為 BsonDocument 再轉回字典
                var bsonDoc = obj.ToBsonDocument();
                var dict = BsonDocumentToDictionary(bsonDoc);

                return SerializationResult.Success(dict, "BSON", 0);
            }
            catch (Exception ex)
            {
                return SerializationResult.Failure($"BSON 序列化失敗: {ex.Message}", "BSON", 0);
            }
        }

        /// <summary> 嘗試使用資料提取器序列化 </summary>
        private static SerializationResult TryExtractorSerialization(object obj)
        {
            try
            {
                Dictionary<string, object?> result;

                if (obj is EntityLoggingDTO entityDto)
                {
                    result = new Dictionary<string, object?>
                    {
                        ["EntityType"] = entityDto.EntityType,
                        ["EntityId"] = entityDto.EntityId,
                        ["EntityState"] = entityDto.EntityState,
                        ["Timestamp"] = entityDto.Timestamp,
                        ["UserId"] = entityDto.UserId,
                        ["Properties"] = entityDto.Properties,
                        ["OriginalProperties"] = entityDto.OriginalProperties,
                        ["ChangedProperties"] = entityDto.ChangedProperties,
                        ["Metadata"] = entityDto.Metadata
                    };
                }
                else
                {
                    result = LoggingDataExtractor.ExtractSafeProperties(obj);
                }

                return SerializationResult.Success(result, "Extractor", 0);
            }
            catch (Exception ex)
            {
                return SerializationResult.Failure($"資料提取器序列化失敗: {ex.Message}", "Extractor", 0);
            }
        }

        /// <summary> 嘗試 JSON 序列化 </summary>
        private static SerializationResult TryJsonSerialization(object obj)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = false,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                };

                var jsonString = JsonSerializer.Serialize(obj, options);
                var result = new Dictionary<string, object?>
                {
                    ["_jsonData"] = jsonString,
                    ["_serializationType"] = "JSON"
                };

                return SerializationResult.Success(result, "JSON", 0);
            }
            catch (Exception ex)
            {
                return SerializationResult.Failure($"JSON 序列化失敗: {ex.Message}", "JSON", 0);
            }
        }

        /// <summary> 建立基本資訊 (最後手段) </summary>
        private static SerializationResult CreateBasicInfo(object obj, long elapsed)
        {
            var result = new Dictionary<string, object?>
            {
                ["_objectType"] = obj.GetType().Name,
                ["_toString"] = obj.ToString(),
                ["_serializationType"] = "BasicInfo",
                ["_timestamp"] = DateTime.UtcNow,
                ["_error"] = "所有序列化策略均失敗，僅保留基本資訊"
            };

            return SerializationResult.Success(result, "BasicInfo", elapsed);
        }

        #endregion

        #region 輔助方法

        /// <summary> 將 BsonDocument 轉換為字典 </summary>
        private static Dictionary<string, object?> BsonDocumentToDictionary(BsonDocument doc)
        {
            var result = new Dictionary<string, object?>();

            foreach (var element in doc)
            {
                result[element.Name] = BsonValueToObject(element.Value);
            }

            return result;
        }

        /// <summary> 將 BsonValue 轉換為 .NET 物件 </summary>
        private static object? BsonValueToObject(BsonValue value)
        {
            return value.BsonType switch
            {
                BsonType.Null => null,
                BsonType.String => value.AsString,
                BsonType.Int32 => value.AsInt32,
                BsonType.Int64 => value.AsInt64,
                BsonType.Double => value.AsDouble,
                BsonType.Boolean => value.AsBoolean,
                BsonType.DateTime => value.ToUniversalTime(),
                BsonType.ObjectId => value.AsObjectId.ToString(),
                BsonType.Array => value.AsBsonArray.Select(BsonValueToObject).ToList(),
                BsonType.Document => BsonDocumentToDictionary(value.AsBsonDocument),
                _ => value.ToString()
            };
        }

        /// <summary> 序列化實體清單 </summary>
        private static List<Dictionary<string, object?>> SerializeEntityList(List<EntityLoggingDTO> entities)
        {
            var result = new List<Dictionary<string, object?>>();

            foreach (var entity in entities)
            {
                var serialized = SafeSerialize(entity);
                if (serialized.IsSuccess && serialized.Data != null)
                {
                    result.Add(serialized.Data);
                }
                else
                {
                    // 如果序列化失敗，至少保留基本資訊
                    result.Add(new Dictionary<string, object?>
                    {
                        ["EntityType"] = entity.EntityType,
                        ["EntityId"] = entity.EntityId,
                        ["EntityState"] = entity.EntityState,
                        ["_serializationError"] = serialized.ErrorMessage
                    });
                }
            }

            return result;
        }

        #endregion
    }
}
