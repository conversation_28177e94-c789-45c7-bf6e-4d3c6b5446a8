import { useEffect, useState } from 'react';
import { Mo<PERSON>, Button, Spin, message, Table, Form, Input, DatePicker, Popconfirm, Row, Col, Card, Typography, Space, Divider } from 'antd';
import dayjs from 'dayjs';
import {
    getSuspendList,
    getSuspendDetail,
    addSuspend,
    editSuspend,
    deleteSuspend,
    createEmptySuspend,
    Suspend,
} from '@/services/pas/SuspendService';
import { getSuspendTypeOptions, getSuspendKindOptions } from '@/services/pas/OptionParameterService';
import ApiSelect from '@/app/pas/components/ApiSelect';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    ProfileOutlined,
    CalendarOutlined,
    FileTextOutlined,
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    PlusOutlined,
    AuditOutlined,
    FileProtectOutlined
} from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;

type SuspendInfoProps = {
    userId: string;
    active: boolean;
    tabUpdateidx?: number;
};

const SuspendInfo: React.FC<SuspendInfoProps> = ({ userId, active, tabUpdateidx }) => {
    const [list, setList] = useState<Suspend[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [detail, setDetail] = useState<Suspend | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [form] = Form.useForm();

    useEffect(() => {
        if (active) {
            fetchSuspendList();
        }
    }, [active, userId, tabUpdateidx]);

    const fetchSuspendList = async () => {
        setLoading(true);
        try {
            const res = await getSuspendList(userId);
            if (res.success && res.data) setList(res.data);
            else message.error(res.message || '載入失敗');
        } catch (err: any) {
            setErrorMsg(err.message || '未知錯誤');
        } finally {
            setLoading(false);
        }
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        try {
            const res = await getSuspendDetail(uid);
            if (res.success && res.data) {
                const d = res.data;
                form.resetFields();
                form.setFieldsValue({
                    ...d,
                    suspendDate: d.suspendDate ? dayjs(d.suspendDate) : null,
                    approveDate: d.approveDate ? dayjs(d.approveDate) : null,
                });
                setDetail(d);
                setIsModalOpen(true);
            } else message.error(res.message || '載入失敗');
        } catch (err: any) {
            message.error(err.message || '錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleAddNew = () => {
        form.resetFields();
        form.setFieldsValue(createEmptySuspend());
        setDetail(null);
        setIsModalOpen(true);
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            const payload: Suspend = {
                ...values,
                userId,
                suspendDate: values.suspendDate ? values.suspendDate.format('YYYY-MM-DD') : '',
                approveDate: values.approveDate ? values.approveDate.format('YYYY-MM-DD') : '',
            };

            setModalLoading(true);
            const res = detail
                ? await editSuspend({ ...payload, uid: detail.uid })
                : await addSuspend(payload);

            if (res.success && res.data?.result) {
                message.success(detail ? '更新成功' : '新增成功');
                setIsModalOpen(false);
                fetchSuspendList();
            } else {
                message.error(res.data?.msg || res.message || '儲存失敗');
            }
        } catch (err: any) {
            if (!err?.errorFields) message.error(err.message || '儲存錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteSuspend(uid);
            if (res.success && res.data?.result) {
                message.success('刪除成功');
                fetchSuspendList();
            } else {
                message.error(res.data?.msg || '刪除失敗');
            }
        } catch (err: any) {
            message.error(err.message || '刪除錯誤');
        }
    };

    if (!active) return null;
    if (errorMsg) return (
        <div style={{
            color: '#ff4d4f',
            textAlign: 'center',
            padding: '40px 20px',
            background: '#fff1f0',
            borderRadius: '8px',
            border: '1px solid #ffccc7'
        }}>
            <ExclamationCircleOutlined style={{ marginRight: 8 }} />
            錯誤：{errorMsg}
        </div>
    );

    return (
        <>
            <Card
                title={<Title level={4} style={{ margin: 0 }}>留職停薪資料</Title>}
                loading={loading}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddNew}
                        style={{ borderRadius: '6px' }}
                    >
                        新增留職停薪資料
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <Table
                    rowKey="uid"
                    dataSource={list}
                    pagination={{ pageSize: 10 }}
                    onRow={(record) => ({
                        onClick: () => handleRowClick(record.uid),
                        className: record.uid === deleteUid ? 'row-deleting-pulse' : '',
                    })}
                    columns={[
                        {
                            title: '留停類型',
                            dataIndex: 'suspendTypeName',
                            render: (text) => (
                                <Space>
                                    <ProfileOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '留停種類',
                            dataIndex: 'suspendKindName',
                            render: (text) => (
                                <Space>
                                    <AuditOutlined style={{ color: '#52c41a' }} />
                                    <Text>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '留停原因',
                            dataIndex: 'suspendReason',
                            render: (text) => (
                                <Text>{text}</Text>
                            )
                        },
                        {
                            title: '留停日期',
                            dataIndex: 'suspendDate',
                            render: (text) => (
                                <Space>
                                    <CalendarOutlined style={{ color: '#722ed1' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '核准日期',
                            dataIndex: 'approveDate',
                            render: (text) => (
                                <Space>
                                    <FileProtectOutlined style={{ color: '#eb2f96' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '操作',
                            render: (_, record) => (
                                <Space onClick={(e) => e.stopPropagation()}>
                                    <Button
                                        type="text"
                                        icon={<EditOutlined />}
                                        onClick={() => handleRowClick(record.uid)}
                                    >
                                        編輯
                                    </Button>
                                    <Popconfirm
                                        title={
                                            <div>
                                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                                <Text>確定要刪除此筆資料嗎？</Text>
                                            </div>
                                        }
                                        onConfirm={() => setDeleteUid(record.uid)}
                                        okText="確認"
                                        cancelText="取消"
                                        okButtonProps={{ danger: true }}
                                    >
                                        <Button
                                            type="text"
                                            danger
                                            icon={<DeleteOutlined />}
                                        >
                                            刪除
                                        </Button>
                                    </Popconfirm>
                                </Space>
                            ),
                        },
                    ]}
                    expandable={{
                        expandedRowRender: (record) => (
                            <div style={{
                                padding: '16px 24px',
                                background: 'rgba(0, 0, 0, 0.02)',
                                borderRadius: '8px',
                                margin: '0 24px'
                            }}>
                                <Space direction="vertical" size={16}>
                                    <div>
                                        <Space>
                                            <FileProtectOutlined style={{ color: '#1890ff' }} />
                                            <Text strong>核准文號：</Text>
                                            <Text>{record.approveNumber || '-'}</Text>
                                        </Space>
                                    </div>
                                    {record.remark && (
                                        <div>
                                            <Space>
                                                <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                                <Text strong>備註：</Text>
                                                <Text>{record.remark}</Text>
                                            </Space>
                                        </div>
                                    )}
                                </Space>
                            </div>
                        ),
                        rowExpandable: (record) => !!(record.approveNumber || record.remark),
                    }}
                />
            </Card>

            <Modal
                title={
                    <Title level={5} style={{ margin: 0 }}>
                        {detail ? '編輯留停資料' : '新增留停資料'}
                    </Title>
                }
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                onOk={handleModalOk}
                confirmLoading={modalLoading}
                width={720}
                centered
                maskClosable={false}
                destroyOnClose
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <Form
                    layout="vertical"
                    form={form}
                    className="mt-4"
                >
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="suspendType"
                                label={<Text strong>留停類型</Text>}
                                rules={[{ required: true, message: '請選擇留停類型' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getSuspendTypeOptions}
                                    placeholder="請選擇留停類型"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="suspendKind"
                                label={<Text strong>留停種類</Text>}
                                rules={[{ required: true, message: '請選擇留停種類' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getSuspendKindOptions}
                                    placeholder="請選擇留停種類"
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item
                        name="suspendReason"
                        label={<Text strong>留停原因</Text>}
                        rules={[{ required: true, message: '請輸入留停原因' }]}
                    >
                        <Input.TextArea
                            rows={4}
                            placeholder="請輸入留停原因"
                            style={{ borderRadius: '6px' }}
                        />
                    </Form.Item>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="suspendDate"
                                label={<Text strong>留停日期</Text>}
                                rules={[{ required: true, message: '請選擇留停日期' }]}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    format="YYYY-MM-DD"
                                    placeholder="請選擇留停日期"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="approveDate"
                                label={<Text strong>核准日期</Text>}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    format="YYYY-MM-DD"
                                    placeholder="請選擇核准日期"
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item
                        name="approveNumber"
                        label={<Text strong>核准文號</Text>}
                    >
                        <Input placeholder="請輸入核准文號" />
                    </Form.Item>

                    <Form.Item
                        name="remark"
                        label={<Text strong>備註</Text>}
                    >
                        <Input.TextArea
                            rows={4}
                            placeholder="請輸入備註內容"
                            style={{ borderRadius: '6px' }}
                        />
                    </Form.Item>
                </Form>
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </>
    );
};

export default SuspendInfo;
