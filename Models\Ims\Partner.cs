using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 商業夥伴 </summary>
public class Partner : ModelBaseEntity
{
    /// <summary> 商業夥伴編號 </summary>
    [Key]
    [Comment("商業夥伴編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PartnerID { get; set; }

    /// <summary> 停用 </summary>
    [Comment("停用")]
    [Column(TypeName = "bit")]
    [DefaultValue(false)]
    public bool IsStop { get; set; }

    // === 導航屬性 ===
    /// <summary> 自然人詳細資訊 </summary>
    public IndividualDetail? IndividualDetail { get; set; }

    /// <summary> 法人詳細資訊 </summary>
    public EnterpriseDetail? EnterpriseDetail { get; set; }

    /// <summary> 客戶詳細資訊 </summary>
    public CustomerDetail? CustomerDetail { get; set; }

    /// <summary> 供應商詳細資訊 </summary>
    public SupplierDetail? SupplierDetail { get; set; }

    /// <summary> 聯絡人清單 </summary>
    public ICollection<PartnerContact> PartnerContacts { get; set; }

    /// <summary> 地址清單 </summary>
    public ICollection<PartnerAddress> Addresses { get; set; }

    /// <summary> 建構式 </summary>
    public Partner()
    {
        PartnerID = Guid.NewGuid();
        IsStop = false;
        PartnerContacts = new List<PartnerContact>();
        Addresses = new List<PartnerAddress>();
    }
}

/// <summary> 商業夥伴 DTO </summary>
public class PartnerDTO : ModelBaseEntityDTO
{
    /// <summary> 商業夥伴編號 </summary>
    public Guid PartnerID { get; set; }

    /// <summary> 停用 </summary>
    public bool IsStop { get; set; }

    /// <summary> 自然人詳細資訊 </summary>
    public IndividualDetailDTO? IndividualDetail { get; set; }

    /// <summary> 法人詳細資訊 </summary>
    public EnterpriseDetailDTO? EnterpriseDetail { get; set; }

    /// <summary> 客戶詳細資訊 </summary>
    public CustomerDetailDTO? CustomerDetail { get; set; }

    /// <summary> 供應商詳細資訊 </summary>
    public SupplierDetailDTO? SupplierDetail { get; set; }

    /// <summary> 聯絡人清單 </summary>
    public ICollection<PartnerContactDTO> PartnerContacts { get; set; }

    /// <summary> 地址清單 </summary>
    public ICollection<PartnerAddressDTO> Addresses { get; set; }

    /// <summary> 建構式 </summary>
    public PartnerDTO()
    {
        PartnerID = Guid.NewGuid();
        IsStop = false;
        PartnerContacts = new List<PartnerContactDTO>();
        Addresses = new List<PartnerAddressDTO>();
    }
}