using System;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Hosting;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Services.Ims;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Ims;

/// <summary> 庫存品管理 </summary>
[Route("api/[controller]")]
[ApiController]
[SwaggerTag("庫存品管理")]
public class ItemController(IItemService _Interface,TestDataService _testDataService,IWebHostEnvironment _environment) : ControllerBase
{
    /// <summary> 取得庫存品列表 </summary>
    [HttpGet]
    [Route("GetAll")]
    [SwaggerOperation(Summary = "取得庫存品列表", Description = "根據庫存品ID取得單一庫存品詳細資料")]
    public async Task<IActionResult> GetAll()
    {
        var items = await _Interface.GetAllAsync();
        return Ok(new { success = true, message = "取得庫存品列表成功", data = items });
    }

    /// <summary> 取得庫存品 </summary>
    [HttpGet]
    [Route("{ItemID}")]
    [SwaggerOperation(Summary = "取得庫存品", Description = "根據庫存品ID取得單一庫存品詳細資料")]
    public async Task<IActionResult> Get([FromRoute] Guid ItemID)
    {
        if (ItemID == Guid.Empty)
        {
            return BadRequest(new { success = false, message = "庫存品ID不能為空", data = (object?)null });
        }

        var item = await _Interface.GetAsync(ItemID);
        if (item == null)
        {
            return NotFound(new { success = false, message = "找不到指定的庫存品", data = (object?)null });
        }

        return Ok(new { success = true, message = "取得庫存品成功", data = item });
    }

    /// <summary> 新增庫存品 </summary>
    [HttpPost]
    [SwaggerOperation(Summary = "新增庫存品", Description = "新增庫存品")]
    public async Task<IActionResult> Add([FromBody] ItemDTO _data)
    {
        var (result, msg) = await _Interface.AddAsync(_data);
        if (!result)
        {
            return BadRequest(new { success = false, message = msg, data = (object?)null });
        }
        return Ok(new { success = true, message = msg, data = (object?)null });
    }

    /// <summary> 更新庫存品 </summary>
    [HttpPatch]
    [SwaggerOperation(Summary = "更新庫存品", Description = "更新庫存品")]
    public async Task<IActionResult> Update([FromBody] ItemDTO _data)
    {
        var (result, msg) = await _Interface.UpdateAsync(_data);
        if (!result)
        {
            return BadRequest(new { success = false, message = msg, data = (object?)null });
        }
        return Ok(new { success = true, message = msg, data = (object?)null });
    }

    /// <summary> 刪除庫存品 </summary>
    [HttpDelete]
    [SwaggerOperation(Summary = "刪除庫存品", Description = "刪除庫存品")]
    public async Task<IActionResult> Delete([FromBody] ItemDTO _data)
    {
        var (result, msg) = await _Interface.DeleteAsync(_data);
        if (!result)
        {
            return BadRequest(new { success = false, message = msg, data = (object?)null });
        }
        return Ok(new { success = true, message = msg, data = (object?)null });
    }

    /// <summary> 產生測試資料 </summary>
    [HttpPost]
    [Route("GenerateTestData")]
    [SwaggerOperation(Summary = "產生測試資料", Description = "產生大量測試庫存品資料，僅限開發環境使用")]
    public async Task<IActionResult> GenerateTestData([FromBody] GenerateTestDataRequest request)
    {
        // 環境安全檢查
        if (!_environment.IsDevelopment())
        {
            return BadRequest(new {
                success = false,
                message = "測試資料產生功能僅在開發環境中可用",
                data = (object?)null
            });
        }

        // 驗證請求參數
        if (request == null || request.Count <= 0 || request.Count > 50000)
        {
            return BadRequest(new {
                success = false,
                message = "請求參數無效，數量必須在 1 到 50,000 之間",
                data = (object?)null
            });
        }

        // 產生測試資料
        var result = await _testDataService.GenerateTestItems(request.Count);

        return Ok(new {
            success = true,
            message = $"成功產生 {result.CreatedCount} 筆測試庫存品資料",
            data = new {
                createdCount = result.CreatedCount,
                batchInfo = new {
                    totalBatches = result.BatchInfo.TotalBatches,
                    batchSize = result.BatchInfo.BatchSize,
                    processingTime = result.BatchInfo.ProcessingTime
                }
            }
        });
    }

    /// <summary> 取得庫存品稅別選項 </summary>
    [HttpGet]
    [Route("GetItemTaxTypes")]
    [SwaggerOperation(Summary = "取得庫存品稅別選項", Description = "取得所有可用的庫存品稅別選項")]
    public IActionResult GetItemTaxTypes()
    {
        var taxTypes = ItemTaxTypeExtensions.GetAllTypes().Select(type => new ItemTaxTypeDTO(type)).ToList();
        return Ok(new { success = true, message = "取得庫存品稅別選項成功", data = taxTypes });
    }
}

/// <summary> 產生測試資料請求 </summary>
public class GenerateTestDataRequest
{
    /// <summary> 要產生的庫存品數量 </summary>
    public int Count { get; set; } = 20000;
}