// =========================================================================================
// Service for Customer Category Management - 客戶分類管理服務
// =========================================================================================

import { CustomerCategory } from '@/services/ims/partner';
import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";

// 建立空的客戶分類
export const createEmptyCustomerCategory = (): Partial<CustomerCategory> => ({
    name: '',
    description: '',
    parentID: null,
    sortCode: 0,
});

/**
 * 取得客戶分類列表
 * @returns Promise<ApiResponse<CustomerCategory[]>>
 */
export async function getCustomerCategoryList(): Promise<ApiResponse<CustomerCategory[]>> {
    try {
        console.log('🔄 CustomerCategoryService: 開始載入客戶分類列表...');
        const response = await httpClient(apiEndpoints.getCustomerCategoryList, {
            method: "GET",
        });

        console.log(`✅ CustomerCategoryService: API回應完成`, response);
        return response;
    } catch (error: any) {
        console.error('❌ CustomerCategoryService: 載入客戶分類列表時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "取得客戶分類列表失敗",
            data: []
        };
    }
}

/**
 * 取得單一客戶分類
 * @param categoryId 分類ID
 * @returns Promise<ApiResponse<CustomerCategory>>
 */
export async function getCustomerCategory(categoryId: string): Promise<ApiResponse<CustomerCategory>> {
    try {
        if (!categoryId || typeof categoryId !== 'string') {
            return {
                success: false,
                message: "客戶分類ID不能為空",
            };
        }

        console.log(`🔄 CustomerCategoryService: 載入客戶分類詳細資料 (ID: ${categoryId})...`);
        const response = await httpClient(`${apiEndpoints.getCustomerCategory}/${categoryId}`, {
            method: "GET",
        });

        if (response.success) {
            console.log('✅ CustomerCategoryService: 客戶分類載入成功');
        } else {
            console.warn('⚠️ CustomerCategoryService: 客戶分類載入失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ CustomerCategoryService: 載入客戶分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "取得客戶分類失敗",
        };
    }
}

/**
 * 新增客戶分類
 * @param categoryData 分類資料
 * @returns Promise<ApiResponse>
 */
export async function addCustomerCategory(categoryData: Partial<CustomerCategory>): Promise<ApiResponse> {
    try {
        // 資料驗證
        if (!categoryData.name || categoryData.name.trim() === '') {
            return {
                success: false,
                message: "分類名稱不能為空",
            };
        }

        console.log('🔄 CustomerCategoryService: 新增客戶分類...', categoryData.name);
        const response = await httpClient(apiEndpoints.addCustomerCategory, {
            method: "POST",
            body: JSON.stringify({
                name: categoryData.name,
                description: categoryData.description || '',
                parentID: categoryData.parentID || null,
                sortCode: categoryData.sortCode || 0,
            }),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ CustomerCategoryService: 客戶分類新增成功');
        } else {
            console.warn('⚠️ CustomerCategoryService: 客戶分類新增失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ CustomerCategoryService: 新增客戶分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "新增客戶分類失敗",
        };
    }
}

/**
 * 修改客戶分類
 * @param categoryData 分類資料
 * @returns Promise<ApiResponse>
 */
export async function editCustomerCategory(categoryData: Partial<CustomerCategory>): Promise<ApiResponse> {
    try {
        // 檢查必要的ID
        if (!categoryData.customerCategoryID) {
            return {
                success: false,
                message: "客戶分類ID不能為空",
            };
        }

        // 資料驗證
        if (!categoryData.name || categoryData.name.trim() === '') {
            return {
                success: false,
                message: "分類名稱不能為空",
            };
        }

        console.log('🔄 CustomerCategoryService: 更新客戶分類...', categoryData.name);
        const response = await httpClient(apiEndpoints.editCustomerCategory, {
            method: "PUT",
            body: JSON.stringify({
                customerCategoryID: categoryData.customerCategoryID,
                name: categoryData.name,
                description: categoryData.description || '',
                parentID: categoryData.parentID || null,
                sortCode: categoryData.sortCode || 0,
            }),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ CustomerCategoryService: 客戶分類更新成功');
        } else {
            console.warn('⚠️ CustomerCategoryService: 客戶分類更新失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ CustomerCategoryService: 更新客戶分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "更新客戶分類失敗",
        };
    }
}

/**
 * 刪除客戶分類
 * @param categoryId 分類ID
 * @returns Promise<ApiResponse>
 */
export async function deleteCustomerCategory(categoryId: string): Promise<ApiResponse> {
    try {
        if (!categoryId || typeof categoryId !== 'string') {
            return {
                success: false,
                message: "客戶分類ID不能為空",
            };
        }

        console.log('🔄 CustomerCategoryService: 刪除客戶分類...', categoryId);
        const response = await httpClient(`${apiEndpoints.deleteCustomerCategory}/${categoryId}`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ CustomerCategoryService: 客戶分類刪除成功');
        } else {
            console.warn('⚠️ CustomerCategoryService: 客戶分類刪除失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ CustomerCategoryService: 刪除客戶分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "刪除客戶分類失敗",
        };
    }
}

// =========================================================================================
// 分類樹狀結構處理函數 (參考 ItemCategoryService 實現)
// =========================================================================================

/**
 * 建立分類樹狀結構
 * @param categories 分類列表
 * @returns 樹狀結構的分類列表
 */
export function buildCustomerCategoryTree(categories: CustomerCategory[]): CustomerCategory[] {
    if (!Array.isArray(categories)) {
        console.warn('⚠️ buildCustomerCategoryTree: categories 不是陣列');
        return [];
    }

    const categoryMap = new Map<string, CustomerCategory>();
    const rootCategories: CustomerCategory[] = [];

    // 建立 Map 以便快速查找
    categories.forEach(category => {
        categoryMap.set(category.customerCategoryID, { ...category, children: [] });
    });

    // 建立樹狀結構
    categories.forEach(category => {
        const categoryNode = categoryMap.get(category.customerCategoryID);
        if (!categoryNode) return;

        if (category.parentID && categoryMap.has(category.parentID)) {
            const parent = categoryMap.get(category.parentID);
            if (parent) {
                parent.children.push(categoryNode);
            }
        } else {
            rootCategories.push(categoryNode);
        }
    });

    return rootCategories;
}

/**
 * 取得分類階層名稱
 * @param categoryId 分類ID
 * @param categories 分類列表
 * @returns 階層名稱字串
 */
export function getCustomerCategoryHierarchyName(categoryId: string | null, categories: CustomerCategory[]): string {
    if (!categoryId || !Array.isArray(categories)) {
        return '';
    }

    const category = categories.find(c => c.customerCategoryID === categoryId);
    if (!category) {
        return '';
    }

    const hierarchy: string[] = [];
    let current: CustomerCategory | undefined = category;

    while (current) {
        hierarchy.unshift(current.name);
        current = current.parentID ? categories.find(c => c.customerCategoryID === current?.parentID) : undefined;
    }

    return hierarchy.join(' > ');
}
