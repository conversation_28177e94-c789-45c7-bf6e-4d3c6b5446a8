import React, { useEffect } from 'react';
import { Modal, Form, DatePicker, Select, Input, Row, Col, message } from 'antd';
import dayjs from 'dayjs';
import { ServiceDepartmentChange } from '@/services/pas/PromotionService';

const { Option } = Select;

export interface ServiceDepartmentChangeModalProps {
    /** Modal是否顯示 */
    open: boolean;
    /** 當前用戶ID */
    userId: string;
    /** 部門選項數據 */
    departmentOptions: Array<{ departmentId: string; name: string; }>;
    /** 組別選項數據 */
    divisionOptions: Array<{ divisionId: string; name: string; }>;
    /** 當前的服務部門異動數據（編輯時傳入） */
    serviceDepartmentChange?: ServiceDepartmentChange | null;
    /** 確認回調函數 */
    onOk: (data: ServiceDepartmentChange) => void;
    /** 取消回調函數 */
    onCancel: () => void;
    /** 載入狀態 */
    loading?: boolean;
}

const ServiceDepartmentChangeModal: React.FC<ServiceDepartmentChangeModalProps> = ({
    open,
    userId,
    departmentOptions,
    divisionOptions,
    serviceDepartmentChange,
    onOk,
    onCancel,
    loading = false
}) => {
    const [form] = Form.useForm();

    // 當Modal打開時，設置表單值或重置表單
    useEffect(() => {
        if (open) {
            if (serviceDepartmentChange) {
                // 編輯模式：設置表單值
                form.setFieldsValue({
                    serviceDepartmentId: serviceDepartmentChange.serviceDepartmentId,
                    serviceDivisionId: serviceDepartmentChange.serviceDivisionId,
                    changeDate: serviceDepartmentChange.changeDate ? dayjs(serviceDepartmentChange.changeDate) : null,
                    effectiveDate: serviceDepartmentChange.effectiveDate ? dayjs(serviceDepartmentChange.effectiveDate) : null,
                    changeReason: serviceDepartmentChange.changeReason,
                    remark: serviceDepartmentChange.remark,
                });
            } else {
                // 新增模式：重置表單
                form.resetFields();
            }
        } else {
            // Modal關閉時重置表單
            form.resetFields();
        }
    }, [open, serviceDepartmentChange, form]);

    const handleOk = async () => {
        try {
            // 檢查必要的 props
            if (!departmentOptions || departmentOptions.length === 0) {
                message.error('部門選項未載入，請稍後再試');
                return;
            }

            if (!divisionOptions) {
                message.error('組別選項未載入，請稍後再試');
                return;
            }

            const values = await form.validateFields();
            const departmentName = departmentOptions.find(dept => dept.departmentId === values.serviceDepartmentId)?.name || '';

            if (!departmentName) {
                message.error('無法找到所選部門，請重新選擇');
                return;
            }

            // 組別是可選的，但如果選擇了要驗證是否存在
            let divisionName = '';
            if (values.serviceDivisionId) {
                divisionName = divisionOptions.find(div => div.divisionId === values.serviceDivisionId)?.name || '';
                if (!divisionName) {
                    message.error('無法找到所選組別，請重新選擇');
                    return;
                }
            }

            const serviceData: ServiceDepartmentChange = {
                uid: serviceDepartmentChange?.uid || '',
                userId: userId,
                serviceDepartmentId: values.serviceDepartmentId,
                serviceDepartmentName: departmentName,
                serviceDivisionId: values.serviceDivisionId || '',
                serviceDivisionName: divisionName,
                changeDate: values.changeDate ? values.changeDate.format('YYYY-MM-DD') : '',
                effectiveDate: values.effectiveDate ? values.effectiveDate.format('YYYY-MM-DD') : '',
                changeReason: values.changeReason || '',
                remark: values.remark || ''
            };

            onOk(serviceData);
        } catch (error) {
            console.error('設定服務部門異動失敗:', error);
            if (error instanceof Error) {
                message.error(error.message || '設定服務部門異動失敗，請檢查輸入資料');
            } else {
                message.error('設定服務部門異動失敗，請稍後再試');
            }
        }
    };

    const handleCancel = () => {
        onCancel();
    };

    return (
        <Modal
            title="設定服務部門異動"
            open={open}
            onOk={handleOk}
            onCancel={handleCancel}
            confirmLoading={loading}
            width={600}
            maskClosable={false}
        >
            <Form
                form={form}
                layout="vertical"
                style={{ marginTop: 16 }}
            >
                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label="服務部門"
                            name="serviceDepartmentId"
                            rules={[{ required: true, message: '請選擇服務部門' }]}
                        >
                            <Select
                                placeholder="請選擇服務部門"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.children?.toString() || '')?.toLowerCase()?.includes(input.toLowerCase())
                                }
                            >
                                {departmentOptions.map(dept => (
                                    <Option key={dept.departmentId} value={dept.departmentId}>
                                        {dept.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="服務組別"
                            name="serviceDivisionId"
                        >
                            <Select
                                placeholder="請選擇服務組別"
                                allowClear
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.children?.toString() || '')?.toLowerCase()?.includes(input.toLowerCase())
                                }
                            >
                                {divisionOptions.map(div => (
                                    <Option key={div.divisionId} value={div.divisionId}>
                                        {div.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="異動日期"
                            name="changeDate"
                            rules={[{ required: true, message: '請選擇異動日期' }]}
                        >
                            <DatePicker placeholder="請選擇異動日期" style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="生效日期"
                            name="effectiveDate"
                            rules={[{ required: true, message: '請選擇生效日期' }]}
                        >
                            <DatePicker placeholder="請選擇生效日期" style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item
                            label="異動原因"
                            name="changeReason"
                            rules={[
                                { max: 200, message: '異動原因不能超過200個字元' }
                            ]}
                        >
                            <Input
                                placeholder="請輸入異動原因"
                                maxLength={200}
                                showCount
                            />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item
                            label="備註"
                            name="remark"
                            rules={[
                                { max: 500, message: '備註不能超過500個字元' }
                            ]}
                        >
                            <Input.TextArea
                                placeholder="請輸入備註"
                                rows={3}
                                maxLength={500}
                                showCount
                            />
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    );
};

export default ServiceDepartmentChangeModal; 