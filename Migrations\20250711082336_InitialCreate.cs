﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace FAST_ERP_Backend.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "dbo");

            migrationBuilder.CreateTable(
                name: "Common_AuditLogs",
                columns: table => new
                {
                    AuditLogsId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "日誌編號"),
                    LogContent = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "操作內容"),
                    IP = table.Column<string>(type: "nvarchar(50)", nullable: true, comment: "操作者IP"),
                    RequestUrl = table.Column<string>(type: "nvarchar(50)", nullable: true, comment: "連線路徑"),
                    Agent = table.Column<string>(type: "nvarchar(50)", nullable: true, comment: "操作資訊"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_AuditLogs", x => x.AuditLogsId);
                });

            migrationBuilder.CreateTable(
                name: "Common_Cities",
                columns: table => new
                {
                    CityId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "縣市編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "縣市名稱"),
                    EnglishName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "英文名稱"),
                    Description = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "說明描述"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_Cities", x => x.CityId);
                });

            migrationBuilder.CreateTable(
                name: "Common_Departments",
                columns: table => new
                {
                    DepartmentId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "部門編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "部門名稱"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    EnterpriseGroupId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "公司編號"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_Departments", x => x.DepartmentId);
                });

            migrationBuilder.CreateTable(
                name: "Common_Districts",
                columns: table => new
                {
                    DistrictId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "鄉鎮市區編號"),
                    CityId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "縣市編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "鄉鎮市區名稱"),
                    ZipCode = table.Column<string>(type: "nvarchar(5)", nullable: false, comment: "郵遞區號"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_Districts", x => x.DistrictId);
                });

            migrationBuilder.CreateTable(
                name: "Common_Divisions",
                columns: table => new
                {
                    DivisionId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "組別編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "組別名稱"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    DepartmentId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "部門編號"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_Divisions", x => x.DivisionId);
                });

            migrationBuilder.CreateTable(
                name: "Common_EnterpriseGroups",
                columns: table => new
                {
                    EnterpriseGroupsId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "公司編號"),
                    Name = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "公司名稱"),
                    UnifiedNumber = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "統一編號"),
                    Representative = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "公司負責人"),
                    EstablishDate = table.Column<long>(type: "bigint", nullable: false, comment: "成立日期"),
                    AccountingPeriod = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "結算週期"),
                    CompanyPhone = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "公司電話"),
                    Phone = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "電話"),
                    Email = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "電子郵件"),
                    MobilePhone = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "手機號碼"),
                    Fax = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "傳真"),
                    Website = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "網頁"),
                    Address1 = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "地址1"),
                    Address2 = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "地址2"),
                    EnglishName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "公司英文名稱"),
                    EnglishAddress = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "英文地址"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_EnterpriseGroups", x => x.EnterpriseGroupsId);
                });

            migrationBuilder.CreateTable(
                name: "Common_EnterpriseImage",
                columns: table => new
                {
                    ImageId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "圖片編號"),
                    ImageName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "圖片名稱"),
                    ImageType = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "圖片類型"),
                    ImagePath = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "圖片路徑"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_EnterpriseImage", x => x.ImageId);
                });

            migrationBuilder.CreateTable(
                name: "Common_FileList",
                columns: table => new
                {
                    FileId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "檔案列表編號"),
                    FileListId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "檔案來源編號"),
                    SourceTable = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "來源資料表"),
                    FileName = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "檔案名稱"),
                    FileType = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "檔案類型"),
                    FilePath = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "檔案路徑"),
                    Description = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "檔案說明"),
                    SortOrder = table.Column<int>(type: "int", nullable: false, comment: "排序編號"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_FileList", x => x.FileId);
                });

            migrationBuilder.CreateTable(
                name: "Common_Positions",
                columns: table => new
                {
                    PositionId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "職稱編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "職稱"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序編碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_Positions", x => x.PositionId);
                });

            migrationBuilder.CreateTable(
                name: "Common_Roles",
                columns: table => new
                {
                    RolesId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "角色編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "角色名稱"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_Roles", x => x.RolesId);
                });

            migrationBuilder.CreateTable(
                name: "Common_SystemGroups",
                columns: table => new
                {
                    SystemGroupId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "系統群組編號"),
                    SystemCode = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "系統代碼(如Common、Pms等)"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "系統名稱"),
                    Option = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "系統設定"),
                    Remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "系統備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_SystemGroups", x => x.SystemGroupId);
                });

            migrationBuilder.CreateTable(
                name: "Common_SystemMenu",
                columns: table => new
                {
                    SystemMenuId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "選單編號"),
                    SystemGroupId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "系統群組編號"),
                    Label = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "選單標籤"),
                    Key = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "選單鍵"),
                    Icon = table.Column<string>(type: "nvarchar(50)", nullable: true, comment: "選單圖示"),
                    ParentId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "父層選單編號"),
                    IsMenu = table.Column<bool>(type: "bit", nullable: false, comment: "為選單"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_SystemMenu", x => x.SystemMenuId);
                });

            migrationBuilder.CreateTable(
                name: "Common_SystemParameters",
                columns: table => new
                {
                    SystemParametersId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "參數編號"),
                    SystemGroupId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "系統群組編號"),
                    ParameterCode = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "參數代號"),
                    Description = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "參數說明"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_SystemParameters", x => x.SystemParametersId);
                });

            migrationBuilder.CreateTable(
                name: "Common_SystemParametersItem",
                columns: table => new
                {
                    SystemParametersItemId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "參數項目編號"),
                    SystemParametersId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "參數編號"),
                    Name = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "參數項目名稱"),
                    Value = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "參數項目值"),
                    SortOrder = table.Column<int>(type: "integer", nullable: true, comment: "排序編號"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_SystemParametersItem", x => x.SystemParametersItemId);
                });

            migrationBuilder.CreateTable(
                name: "Common_Units",
                columns: table => new
                {
                    UnitId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "單位流水號"),
                    UnitNo = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, comment: "單位編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "單位名稱"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_Units", x => x.UnitId);
                });

            migrationBuilder.CreateTable(
                name: "Common_Users",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    Account = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "帳號"),
                    Password = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "密碼"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "使用者名稱"),
                    EnterpriseGroupId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "公司群組編號"),
                    RolesId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "角色編號"),
                    PositionId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "職務編號"),
                    EMail = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "電子信箱"),
                    PermanentAddress = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "戶籍地址"),
                    MailingAddress = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "通訊地址"),
                    TelNo = table.Column<string>(type: "nvarchar(15)", nullable: false, comment: "電話"),
                    Phone = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "手機"),
                    AltPhone = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "備用電話"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序編碼"),
                    UnlockTime = table.Column<long>(type: "bigint", nullable: true, comment: "解鎖時間"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_Users", x => x.UserId);
                });

            migrationBuilder.CreateTable(
                name: "Ims_Contact",
                columns: table => new
                {
                    ContactID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "聯絡人編號"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "聯絡人姓名"),
                    Position = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true, comment: "職位"),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "電子郵件"),
                    Phone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "電話"),
                    IsPrimary = table.Column<bool>(type: "bit", nullable: false, comment: "是否為主要聯絡人"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_Contact", x => x.ContactID);
                });

            migrationBuilder.CreateTable(
                name: "Ims_ContactRole",
                columns: table => new
                {
                    ContactRoleID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "角色編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "角色名稱")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_ContactRole", x => x.ContactRoleID);
                });

            migrationBuilder.CreateTable(
                name: "Ims_CustomerCategory",
                columns: table => new
                {
                    CustomerCategoryID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "客戶分類編號"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "名稱"),
                    Description = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "描述"),
                    ParentID = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "父分類ID"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_CustomerCategory", x => x.CustomerCategoryID);
                    table.ForeignKey(
                        name: "FK_Ims_CustomerCategory_Ims_CustomerCategory_ParentID",
                        column: x => x.ParentID,
                        principalTable: "Ims_CustomerCategory",
                        principalColumn: "CustomerCategoryID");
                });

            migrationBuilder.CreateTable(
                name: "Ims_ItemCategory",
                columns: table => new
                {
                    ItemCategoryID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "庫存品分類編號"),
                    Name = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "名稱"),
                    Description = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "描述"),
                    ParentID = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "父分類ID"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_ItemCategory", x => x.ItemCategoryID);
                    table.ForeignKey(
                        name: "FK_Ims_ItemCategory_Ims_ItemCategory_ParentID",
                        column: x => x.ParentID,
                        principalTable: "Ims_ItemCategory",
                        principalColumn: "ItemCategoryID");
                });

            migrationBuilder.CreateTable(
                name: "Ims_PriceType",
                columns: table => new
                {
                    PriceTypeID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "價格類別編號"),
                    Name = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "價格類別名稱"),
                    Description = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "價格類別描述"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序"),
                    AllowStop = table.Column<bool>(type: "bit", nullable: false, comment: "允許停用"),
                    IsStop = table.Column<bool>(type: "bit", nullable: false, comment: "停用"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_PriceType", x => x.PriceTypeID);
                });

            migrationBuilder.CreateTable(
                name: "Ims_SupplierCategory",
                columns: table => new
                {
                    SupplierCategoryID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "供應商分類編號"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "名稱"),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false, comment: "描述"),
                    ParentID = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "父分類ID"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_SupplierCategory", x => x.SupplierCategoryID);
                    table.ForeignKey(
                        name: "FK_Ims_SupplierCategory_Ims_SupplierCategory_ParentID",
                        column: x => x.ParentID,
                        principalTable: "Ims_SupplierCategory",
                        principalColumn: "SupplierCategoryID");
                });

            migrationBuilder.CreateTable(
                name: "Pas_Certification",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    userId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    certificateName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "檢覈名稱"),
                    certificateYearMonth = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "檢覈年月"),
                    certificateInstitution = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "檢覈機關"),
                    certificateDate = table.Column<long>(type: "bigint", nullable: true, comment: "發證日期"),
                    certificateNumber = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "證書文號"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: true, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Certification", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Dependent",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    userId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "扶養者員工"),
                    dependentRocId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "被扶養者身分證字號"),
                    dependentName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "被扶養者姓名"),
                    dependentBirthday = table.Column<long>(type: "bigint", nullable: true, comment: "被扶養者生日"),
                    dependentRelationType = table.Column<string>(type: "nvarchar(3)", nullable: false, comment: "被扶養者關係類型"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Dependent", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Education",
                columns: table => new
                {
                    Uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    UserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    DegreeType = table.Column<string>(type: "nvarchar(3)", nullable: false, comment: "學位代號"),
                    GraduateType = table.Column<string>(type: "nvarchar(3)", nullable: false, comment: "結業代號"),
                    SchoolName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "學校名稱"),
                    DepartmentName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "院系科別"),
                    PeriodDateStart = table.Column<long>(type: "bigint", nullable: true, comment: "修業起日"),
                    PeriodDateEnd = table.Column<long>(type: "bigint", nullable: true, comment: "修業迄日"),
                    CertificateDate = table.Column<long>(type: "bigint", nullable: true, comment: "發證日期"),
                    CertificateNumber = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "證件字號"),
                    Remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Education", x => x.Uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Employee",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    IdNo = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "身分證字號"),
                    IdType = table.Column<string>(type: "nvarchar(1)", nullable: false, comment: "證號別"),
                    errorMark = table.Column<string>(type: "nvarchar(1)", nullable: false, comment: "錯誤註記"),
                    EmpNo = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "員工編號"),
                    Birthday = table.Column<long>(type: "bigint", nullable: true, comment: "出生日期"),
                    BloodType = table.Column<string>(type: "nvarchar(3)", nullable: false, comment: "血型類別"),
                    SpouseIdNo = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "配偶身分證"),
                    SpouseName = table.Column<string>(type: "nvarchar(12)", nullable: false, comment: "配偶姓名"),
                    EduLevel = table.Column<string>(type: "nvarchar(1)", nullable: false, comment: "最高學歷"),
                    HireDate = table.Column<long>(type: "bigint", nullable: true, comment: "到職日"),
                    ProbStartDate = table.Column<long>(type: "bigint", nullable: true, comment: "試用日"),
                    OfficialHireDate = table.Column<long>(type: "bigint", nullable: true, comment: "正式任用日"),
                    LeaveDate = table.Column<long>(type: "bigint", nullable: true, comment: "離職日"),
                    LaborInsStartDate = table.Column<long>(type: "bigint", nullable: true, comment: "勞保加保日"),
                    HealthInsStartDate = table.Column<long>(type: "bigint", nullable: true, comment: "健保加保日"),
                    TransferDate = table.Column<long>(type: "bigint", nullable: true, comment: "轉任日"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Employee", x => x.UserId);
                });

            migrationBuilder.CreateTable(
                name: "Pas_EmployeeRegularSalary",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    userId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "員工userid"),
                    salaryItemUid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "常態薪資資料編號"),
                    amount = table.Column<decimal>(type: "decimal(8,0)", nullable: false, comment: "設定金額"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    isEnable = table.Column<bool>(type: "bit", nullable: false, comment: "個別項目啟用"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_EmployeeRegularSalary", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Ensure",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    userId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    ensureNumber = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "保證書編號"),
                    guarantorName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "保證人姓名"),
                    guarantorPersonalId = table.Column<string>(type: "nvarchar(20)", nullable: true, comment: "保證人身分證"),
                    guarantorBirthday = table.Column<long>(type: "bigint", nullable: true, comment: "保證人生日"),
                    guarantorAddress = table.Column<string>(type: "nvarchar(250)", nullable: true, comment: "保證人地址"),
                    guarantorPhone = table.Column<string>(type: "nvarchar(50)", nullable: true, comment: "保證人電話"),
                    relationship = table.Column<string>(type: "nvarchar(50)", nullable: true, comment: "關係"),
                    guarantorProperty = table.Column<string>(type: "nvarchar(250)", nullable: true, comment: "保證人財產"),
                    propertyValue = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "財產價值"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: true, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Ensure", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Examination",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    userId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    examName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "考試名稱"),
                    examType = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "種類科別"),
                    admittanceGrade = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "錄取等第"),
                    examInstitution = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "考試機關"),
                    examStartDate = table.Column<long>(type: "bigint", nullable: true, comment: "考試起日"),
                    examEndDate = table.Column<long>(type: "bigint", nullable: true, comment: "考試迄日"),
                    certificateDate = table.Column<long>(type: "bigint", nullable: true, comment: "發證日期"),
                    certificateNumber = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "證書文號"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: true, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Examination", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_ExpenseDepartmentChange",
                columns: table => new
                {
                    Uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    UserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    ExpenseDepartmentId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "開支部門編號"),
                    ChangeDate = table.Column<long>(type: "bigint", nullable: true, comment: "異動日期"),
                    EffectiveDate = table.Column<long>(type: "bigint", nullable: true, comment: "生效日期"),
                    ChangeReason = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "異動原因"),
                    Remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_ExpenseDepartmentChange", x => x.Uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Hensure",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    userId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "依附使用者編號"),
                    dependentRocId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "眷屬身分證字號"),
                    dependentName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "眷屬姓名"),
                    dependentRelationType = table.Column<string>(type: "nvarchar(3)", nullable: false, comment: "依附關係類型"),
                    HealthInsStartDate = table.Column<long>(type: "bigint", nullable: true, comment: "健保投保起日"),
                    HealthInsEndDate = table.Column<long>(type: "bigint", nullable: true, comment: "健保投保迄日"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Hensure", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_InsuranceGrade",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    InsuranceType = table.Column<int>(type: "int", nullable: false, comment: "保險類型 (1=勞保, 2=健保, 3=職災)"),
                    MonthlySalary = table.Column<int>(type: "int", nullable: false, comment: "月投保薪資"),
                    StartDate = table.Column<long>(type: "bigint", nullable: true, comment: "生效日期"),
                    EndDate = table.Column<long>(type: "bigint", nullable: true, comment: "結束日期"),
                    Remark = table.Column<string>(type: "nvarchar(500)", nullable: true, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_InsuranceGrade", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_InsuranceHistory",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    UserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    InsuranceType = table.Column<int>(type: "int", nullable: false, comment: "保險類型 (1=勞保, 2=健保, 3=職災)"),
                    InsuranceGradeUid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "投保級距編號 (對應 InsuranceGrade.uid)"),
                    StartDate = table.Column<long>(type: "bigint", nullable: false, comment: "生效日期 (timestamp)"),
                    EndDate = table.Column<long>(type: "bigint", nullable: true, comment: "結束日期 (timestamp，可為 null)"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_InsuranceHistory", x => x.uid);
                },
                comment: "員工保險級距歷程記錄");

            migrationBuilder.CreateTable(
                name: "Pas_PerformancePointGroup",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "群組UID"),
                    groupName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "群組名稱"),
                    weightRatio = table.Column<decimal>(type: "decimal(5,2)", nullable: false, comment: "加權比例 (群組層級)"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_PerformancePointGroup", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_PerformancePointRecord",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    userId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    pointDate = table.Column<long>(type: "bigint", nullable: true, comment: "點數日期"),
                    pointUid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "點數類型UID"),
                    point = table.Column<decimal>(type: "decimal(10,2)", nullable: false, comment: "點數"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_PerformancePointRecord", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_PerformancePointType",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "點數類型UID"),
                    pointName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "點數名稱"),
                    groupUid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "所屬群組UID"),
                    weightRatio = table.Column<decimal>(type: "decimal(5,2)", nullable: false, comment: "加權比例 (點數項目層級)"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_PerformancePointType", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Promotion",
                columns: table => new
                {
                    Uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    UserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    PromotionType = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "升遷類型"),
                    JobTitle = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "職稱代號"),
                    JobLevel = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "職等"),
                    JobRank = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "級數"),
                    PromotionDate = table.Column<long>(type: "bigint", nullable: true, comment: "升遷日期"),
                    EffectiveDate = table.Column<long>(type: "bigint", nullable: true, comment: "生效日期"),
                    PromotionReason = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "升遷原因"),
                    ExpenseDepartmentChangeUid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "開支部門異動DATAUID"),
                    ServiceDepartmentChangeUid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "服務部門異動DATAUID"),
                    JobroleType = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "任用資格"),
                    SalaryType = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "薪俸類型"),
                    SalaryAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true, comment: "薪俸"),
                    CategoryType = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "錄用類別"),
                    Remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Promotion", x => x.Uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_RegularSalaryItem",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    itemName = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "項目名稱"),
                    itemType = table.Column<string>(type: "nvarchar(1)", nullable: false, comment: "加減項類型"),
                    isTaxable = table.Column<bool>(type: "bit", nullable: false, comment: "扣稅類型"),
                    description = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "項目描述"),
                    isEnable = table.Column<bool>(type: "bit", nullable: false, comment: "項目啟用"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_RegularSalaryItem", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Salary",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    SalaryStatus = table.Column<string>(type: "nvarchar(1)", nullable: false, comment: "發薪狀態 (0:停薪 1:正常)"),
                    EmployerContributionRate = table.Column<decimal>(type: "decimal(5,2)", nullable: true, comment: "雇主提撥比率"),
                    EmployeeContributionType = table.Column<string>(type: "nvarchar(1)", nullable: false, comment: "員工自提類型 (0:不提撥、1:提撥比率、2:固定自提金額)"),
                    EmployeeContributionRate = table.Column<decimal>(type: "decimal(5,2)", nullable: true, comment: "員工自提比例"),
                    EmployeeContributionAmount = table.Column<decimal>(type: "decimal(12,2)", nullable: true, comment: "員工自提金額"),
                    TaxType = table.Column<string>(type: "nvarchar(1)", nullable: false, comment: "計稅型式 (1:自動計算、2:固定稅率、3:固定稅額)"),
                    FixedTaxRate = table.Column<decimal>(type: "decimal(5,2)", nullable: true, comment: "固定稅率"),
                    FixedTaxAmount = table.Column<decimal>(type: "decimal(12,2)", nullable: true, comment: "固定稅額"),
                    TransferAccount = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "轉帳帳號"),
                    Remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Salary", x => x.UserId);
                });

            migrationBuilder.CreateTable(
                name: "Pas_SalaryPoint",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    PointLevel = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "薪點名稱"),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "薪點金額（每點對應金額）"),
                    EffectiveDate = table.Column<long>(type: "bigint", nullable: false, comment: "生效日期（timestamp）"),
                    AdjustmentReason = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "調整原因"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_SalaryPoint", x => x.uid);
                },
                comment: "薪點金額紀錄");

            migrationBuilder.CreateTable(
                name: "Pas_ServiceDepartmentChange",
                columns: table => new
                {
                    Uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    UserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    ServiceDepartmentId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "服務部門編號"),
                    ServiceDivisionId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "服務組別編號"),
                    ChangeDate = table.Column<long>(type: "bigint", nullable: true, comment: "異動日期"),
                    EffectiveDate = table.Column<long>(type: "bigint", nullable: true, comment: "生效日期"),
                    ChangeReason = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "異動原因"),
                    Remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_ServiceDepartmentChange", x => x.Uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Suspend",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    userId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    suspendType = table.Column<string>(type: "nvarchar(3)", nullable: false, comment: "留停類型"),
                    suspendKind = table.Column<string>(type: "nvarchar(3)", nullable: false, comment: "留停種類"),
                    suspendReason = table.Column<string>(type: "nvarchar(MAX)", nullable: true, comment: "留停原因"),
                    suspendDate = table.Column<long>(type: "bigint", nullable: true, comment: "留停日期"),
                    approveDate = table.Column<long>(type: "bigint", nullable: true, comment: "核准日期"),
                    approveNumber = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "核准文號"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: true, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Suspend", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Train",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    userId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    courseName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "課程名稱"),
                    cost = table.Column<int>(type: "int", nullable: false, comment: "費用"),
                    ranking = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "名次"),
                    score = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "成績"),
                    instructor = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "講師名稱"),
                    durationHours = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "時數"),
                    trainingInstitute = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "訓練機構名稱"),
                    courseStartDate = table.Column<long>(type: "bigint", nullable: true, comment: "課程起日"),
                    courseEndDate = table.Column<long>(type: "bigint", nullable: true, comment: "課程迄日"),
                    certificateDate = table.Column<long>(type: "bigint", nullable: true, comment: "發證日期"),
                    certificateNumber = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "證書文號"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Train", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pas_Undergo",
                columns: table => new
                {
                    uid = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "資料編號"),
                    userId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    agencyName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "服務機關名稱"),
                    departmentName = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "服務部門名稱"),
                    jobTitle = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "職稱"),
                    duty = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "職務"),
                    jobGrade = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "薪級"),
                    hireDate = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "到職年月"),
                    terminationDate = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "卸職年月"),
                    supervisorName = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "主管姓名"),
                    certificateDate = table.Column<long>(type: "bigint", nullable: true, comment: "發證日期"),
                    certificateNumber = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "證書文號"),
                    remark = table.Column<string>(type: "nvarchar(MAX)", nullable: true, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pas_Undergo", x => x.uid);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AmortizationSources",
                columns: table => new
                {
                    AmortizationSourceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "攤提來源編號"),
                    DepartmentId = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "部門編號"),
                    SourceName = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "攤提來源名稱"),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "攤提來源描述"),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "攤提來源金額"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AmortizationSources", x => x.AmortizationSourceId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AssetAccounts",
                columns: table => new
                {
                    AssetAccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產科目流水號"),
                    AssetAccountNo = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, comment: "財產科目編號"),
                    AssetAccountName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "財產科目名稱"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AssetAccounts", x => x.AssetAccountId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AssetCategory",
                columns: table => new
                {
                    AssetCategoryId = table.Column<string>(type: "nvarchar(1)", nullable: false, comment: "資產類別編號"),
                    AssetCategoryName = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "資產類別名稱"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AssetCategory", x => x.AssetCategoryId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AssetLocationTransfer",
                columns: table => new
                {
                    TransferId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "位置變動單編號"),
                    TransferNo = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "變動單號"),
                    TransferDate = table.Column<long>(type: "bigint", nullable: false, comment: "變動日期"),
                    ApplicantId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "申請人員"),
                    ApplicantDepartmentId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "申請部門"),
                    TransferReason = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "變動原因"),
                    ApprovalStatus = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "審核狀態"),
                    ApproverId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "審核人員"),
                    ApprovalDate = table.Column<long>(type: "bigint", nullable: true, comment: "審核日期"),
                    ApprovalComments = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "審核意見"),
                    ExecutionStatus = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "執行狀態"),
                    ExecutorId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "執行人員"),
                    ExecutionDate = table.Column<long>(type: "bigint", nullable: true, comment: "執行日期"),
                    Notes = table.Column<string>(type: "nvarchar(1000)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AssetLocationTransfer", x => x.TransferId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_Assets",
                columns: table => new
                {
                    AssetId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產流水號"),
                    AssetNo = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "財產編號"),
                    AssetName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "財產名稱"),
                    AssetShortName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "財產簡稱"),
                    AcquisitionDate = table.Column<long>(type: "bigint", nullable: false, comment: "取得日期"),
                    Quantity = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "數量"),
                    UnitId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "單位編號"),
                    PurchaseAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "購入金額"),
                    DepreciationAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "折舊金額"),
                    AccumulatedDepreciationAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "累計折舊金額"),
                    SubsidyAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "輔助金額"),
                    EstimatedResidualValue = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "預估殘值"),
                    DepartmentId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "部門"),
                    DivisionId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "股別"),
                    CustodianId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "保管人"),
                    UserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用人"),
                    AssetStatusId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "財產狀態"),
                    StatusChangeDate = table.Column<long>(type: "bigint", nullable: false, comment: "狀態異動日期"),
                    Usage = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "使用狀態"),
                    Notes = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "備註"),
                    StorageLocationId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "存放地點"),
                    ServiceLife = table.Column<int>(type: "int", nullable: false, comment: "耐用年限"),
                    InsurancePeriod = table.Column<int>(type: "int", nullable: false, comment: "保固年限"),
                    ManufacturerId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "廠牌型號"),
                    Specification = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "規格"),
                    BuildingAddress = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "建物地址"),
                    BuildingStructure = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "建物構造"),
                    ConstructionDate = table.Column<long>(type: "bigint", nullable: true, comment: "興建日期"),
                    FloorArea = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "面積(m²)"),
                    PublicArea = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "公設(m²)"),
                    UsageExpiryDate = table.Column<long>(type: "bigint", nullable: true, comment: "使用執照日期"),
                    UsageLicenseNo = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用執照號碼"),
                    BuildingTaxItem = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "適用房屋稅目"),
                    PublicValue = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "公告現值"),
                    LandSection = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "地目"),
                    LandLocation = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "地段"),
                    LandNumber = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "地號"),
                    LandArea = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "面積(m²)"),
                    CertificateNo = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "權狀號碼"),
                    AssetAccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產科目"),
                    AssetSubAccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產子目"),
                    AssetCategoryId = table.Column<string>(type: "nvarchar(1)", nullable: false, comment: "資產類別編號"),
                    CustomAssetNo1 = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "自訂財產編號一"),
                    CustomAssetNo2 = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "自訂財產編號二"),
                    ScrapReason = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "報廢原因"),
                    ScrapDate = table.Column<long>(type: "bigint", nullable: true, comment: "報廢日期"),
                    EstimatedScrapYear = table.Column<long>(type: "bigint", nullable: true, comment: "預計報廢年度"),
                    UsableAfterScrap = table.Column<string>(type: "nvarchar(1)", nullable: false, comment: "報廢後堪用"),
                    EquipmentTypeId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "設備類型"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_Assets", x => x.AssetId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AssetSources",
                columns: table => new
                {
                    AssetSourceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "資產來源編號"),
                    AssetSourceName = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "資產來源名稱"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AssetSources", x => x.AssetSourceId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AssetStatus",
                columns: table => new
                {
                    AssetStatusId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產狀態流水號"),
                    AssetStatusNo = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, comment: "財產狀態編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "財產狀態名稱"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AssetStatus", x => x.AssetStatusId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AssetSubAccounts",
                columns: table => new
                {
                    AssetSubAccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產子目流水號"),
                    AssetSubAccountNo = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, comment: "財產科目編號"),
                    AssetAccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產科目編號"),
                    AssetSubAccountName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "財產子目名稱"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AssetSubAccounts", x => x.AssetSubAccountId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_DepreciationFormDetail",
                columns: table => new
                {
                    DepreciationId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "折舊紀錄編號"),
                    AssetId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產編號"),
                    DepreciationYear = table.Column<int>(type: "int", nullable: false, comment: "折舊年度"),
                    DepreciationMonth = table.Column<int>(type: "int", nullable: false, comment: "折舊月份"),
                    OriginalAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "原始金額"),
                    BeginningBookValue = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "期初帳面價值"),
                    EndingBookValue = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "期末帳面價值"),
                    AccumulatedDepreciation = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "累計折舊金額"),
                    CurrentDepreciation = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "本期折舊金額"),
                    DepreciationRate = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "折舊率"),
                    DepreciationMethod = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "折舊方法"),
                    ServiceLifeRemaining = table.Column<int>(type: "int", nullable: false, comment: "剩餘耐用年限"),
                    IsAdjustment = table.Column<bool>(type: "bit", nullable: false, comment: "是否為調整紀錄"),
                    AdjustmentReason = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "調整原因"),
                    Notes = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "備註"),
                    DepreciationDate = table.Column<long>(type: "bigint", nullable: false, comment: "折舊日期"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_DepreciationFormDetail", x => x.DepreciationId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_DepreciationForms",
                columns: table => new
                {
                    DepreciationFormId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "固定資產折舊單編號"),
                    DepreciationId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "折舊紀錄編號"),
                    DepreciationYear = table.Column<int>(type: "int", nullable: false, comment: "折舊年度"),
                    DepreciationMonth = table.Column<int>(type: "int", nullable: false, comment: "折舊月份"),
                    DepreciationDate = table.Column<long>(type: "bigint", nullable: false, comment: "折舊日期"),
                    Notes = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_DepreciationForms", x => x.DepreciationFormId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_EquipmentType",
                columns: table => new
                {
                    EquipmentTypeId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "設備類型流水號"),
                    EquipmentTypeNo = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, comment: "設備類型編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "設備類型名稱"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_EquipmentType", x => x.EquipmentTypeId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_InsuranceUnits",
                columns: table => new
                {
                    InsuranceUnitId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "承保單位編號"),
                    Name = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "承保單位名稱"),
                    InsuranceAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "投保金額"),
                    InsuranceStartDate = table.Column<long>(type: "bigint", nullable: true, comment: "投保起日"),
                    InsuranceExpiryDate = table.Column<long>(type: "bigint", nullable: true, comment: "投保迄日"),
                    CompanyNo = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "公司統一編號"),
                    ContactPerson = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "聯絡人"),
                    ContactPhone = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "聯絡電話"),
                    ContactEmail = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "聯絡信箱"),
                    Address = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "公司地址"),
                    Website = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "公司網站"),
                    Description = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "說明描述"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_InsuranceUnits", x => x.InsuranceUnitId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_Manufacturers",
                columns: table => new
                {
                    ManufacturerId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "廠牌型號編號"),
                    Name = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "廠牌名稱"),
                    Model = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "型號"),
                    ManufacturerName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "製造商名稱"),
                    Supplier = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "供應商名稱"),
                    ContactPerson = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "聯絡人"),
                    ContactPhone = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "聯絡電話"),
                    ContactEmail = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "聯絡信箱"),
                    Description = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "說明描述"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_Manufacturers", x => x.ManufacturerId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_StorageLocations",
                columns: table => new
                {
                    StorageLocationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "存放地點編號"),
                    Name = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "存放地點名稱"),
                    Address = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "詳細地址"),
                    Description = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "地點描述"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序號碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_StorageLocations", x => x.StorageLocationId);
                });

            migrationBuilder.CreateTable(
                name: "Pms_SystemParameters",
                columns: table => new
                {
                    ParameterId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "系統參數編號"),
                    ParameterName = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "參數名稱"),
                    ParameterValue = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "參數值"),
                    ParameterDescription = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "參數描述"),
                    ParameterType = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "參數類型"),
                    IsEnabled = table.Column<bool>(type: "bit", nullable: false, comment: "是否啟用"),
                    SortOrder = table.Column<int>(type: "int", nullable: false, comment: "排序順序"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_SystemParameters", x => x.ParameterId);
                });

            migrationBuilder.CreateTable(
                name: "PmsUserRoles",
                columns: table => new
                {
                    PmsUserRoleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產系統使用者身分編號"),
                    RoleName = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "身分名稱"),
                    Description = table.Column<string>(type: "nvarchar(MAX)", nullable: false, comment: "說明"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序編碼"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PmsUserRoles", x => x.PmsUserRoleId);
                });

            migrationBuilder.CreateTable(
                name: "Common_FileUploads",
                columns: table => new
                {
                    FileUploadId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "檔案編號"),
                    EnterpriseGroupsId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "企業群組編號"),
                    OriginalFileName = table.Column<string>(type: "nvarchar(255)", nullable: false, comment: "原始檔案名稱"),
                    StoredFileName = table.Column<string>(type: "nvarchar(255)", nullable: false, comment: "儲存檔案名稱"),
                    FilePath = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "檔案路徑"),
                    FileSize = table.Column<long>(type: "bigint", nullable: false, comment: "檔案大小"),
                    ContentType = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "檔案類型"),
                    FileExtension = table.Column<string>(type: "nvarchar(10)", nullable: false, comment: "檔案副檔名"),
                    FileCategory = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "檔案分類"),
                    SourceModule = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "來源模組"),
                    SourceTable = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "來源資料表"),
                    SourceRecordId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "來源記錄編號"),
                    Description = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "檔案描述"),
                    SortOrder = table.Column<int>(type: "int", nullable: false, comment: "排序順序"),
                    IsPrimary = table.Column<bool>(type: "bit", nullable: false, comment: "是否為主要檔案"),
                    AccessLevel = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "存取權限"),
                    FileStatus = table.Column<string>(type: "nvarchar(20)", nullable: false, comment: "檔案狀態"),
                    FileHash = table.Column<string>(type: "nvarchar(64)", nullable: false, comment: "檔案雜湊值"),
                    DownloadCount = table.Column<int>(type: "int", nullable: false, comment: "下載次數"),
                    LastAccessTime = table.Column<long>(type: "bigint", nullable: true, comment: "最後存取時間"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_FileUploads", x => x.FileUploadId);
                    table.ForeignKey(
                        name: "FK_Common_FileUploads_Common_EnterpriseGroups_EnterpriseGroupsId",
                        column: x => x.EnterpriseGroupsId,
                        principalTable: "Common_EnterpriseGroups",
                        principalColumn: "EnterpriseGroupsId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Common_RolesPermissions",
                columns: table => new
                {
                    RolesPermissionsId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "權限編號"),
                    RolesId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "角色編號"),
                    SystemMenuId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "系統選單編號"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Common_RolesPermissions", x => x.RolesPermissionsId);
                    table.ForeignKey(
                        name: "FK_Common_RolesPermissions_Common_Roles_RolesId",
                        column: x => x.RolesId,
                        principalTable: "Common_Roles",
                        principalColumn: "RolesId");
                    table.ForeignKey(
                        name: "FK_Common_RolesPermissions_Common_SystemMenu_SystemMenuId",
                        column: x => x.SystemMenuId,
                        principalTable: "Common_SystemMenu",
                        principalColumn: "SystemMenuId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Ims_Item",
                columns: table => new
                {
                    ItemID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "庫存品編號"),
                    CustomNO = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "自定義編號"),
                    InternationalBarCode = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "國際條碼"),
                    Name = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "名稱"),
                    Unit = table.Column<string>(type: "nvarchar(50)", nullable: false, comment: "單位"),
                    ItemCategoryID = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "庫存品分類編號"),
                    Description = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "描述"),
                    SortCode = table.Column<int>(type: "int", nullable: false, comment: "排序"),
                    IsStop = table.Column<bool>(type: "bit", nullable: false, comment: "停售"),
                    TaxType = table.Column<int>(type: "int", nullable: false, comment: "庫存品稅別"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_Item", x => x.ItemID);
                    table.ForeignKey(
                        name: "FK_Ims_Item_Ims_ItemCategory_ItemCategoryID",
                        column: x => x.ItemCategoryID,
                        principalTable: "Ims_ItemCategory",
                        principalColumn: "ItemCategoryID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Ims_Partner",
                columns: table => new
                {
                    PartnerID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "商業夥伴編號"),
                    IsStop = table.Column<bool>(type: "bit", nullable: false, comment: "停用"),
                    SupplierCategoryID = table.Column<string>(type: "nvarchar(100)", nullable: true),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_Partner", x => x.PartnerID);
                    table.ForeignKey(
                        name: "FK_Ims_Partner_Ims_SupplierCategory_SupplierCategoryID",
                        column: x => x.SupplierCategoryID,
                        principalTable: "Ims_SupplierCategory",
                        principalColumn: "SupplierCategoryID");
                });

            migrationBuilder.CreateTable(
                name: "Pms_AccessoryEquipments",
                columns: table => new
                {
                    AccessoryEquipmentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "附屬設備編號"),
                    EquipmentNo = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "設備編號"),
                    EquipmentName = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "設備名稱"),
                    EquipmentType = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "設備類型"),
                    Specification = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "規格/型號"),
                    PurchaseDate = table.Column<long>(type: "bigint", nullable: true, comment: "購入日期"),
                    PurchasePrice = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "購入價格"),
                    AssetId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產編號"),
                    UsageStatus = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用狀態"),
                    Remarks = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AccessoryEquipments", x => x.AccessoryEquipmentId);
                    table.ForeignKey(
                        name: "FK_Pms_AccessoryEquipments_Pms_Assets_AssetId",
                        column: x => x.AssetId,
                        principalTable: "Pms_Assets",
                        principalColumn: "AssetId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AssetAmortizationSourceMapping",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AssetId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產流水號"),
                    AmortizationSourceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "攤提來源編號"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AssetAmortizationSourceMapping", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Pms_AssetAmortizationSourceMapping_Pms_AmortizationSources_AmortizationSourceId",
                        column: x => x.AmortizationSourceId,
                        principalTable: "Pms_AmortizationSources",
                        principalColumn: "AmortizationSourceId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Pms_AssetAmortizationSourceMapping_Pms_Assets_AssetId",
                        column: x => x.AssetId,
                        principalTable: "Pms_Assets",
                        principalColumn: "AssetId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AssetLocationTransferDetail",
                columns: table => new
                {
                    DetailId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "明細編號"),
                    TransferId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "位置變動單編號"),
                    AssetId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產編號"),
                    OriginalLocationId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "原存放地點"),
                    NewLocationId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "新存放地點"),
                    OriginalCustodianId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "原保管人"),
                    NewCustodianId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "新保管人"),
                    OriginalUserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "原使用人"),
                    NewUserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "新使用人"),
                    OriginalDepartmentId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "原部門"),
                    NewDepartmentId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "新部門"),
                    OriginalDivisionId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "原股別"),
                    NewDivisionId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "新股別"),
                    ChangeItems = table.Column<string>(type: "nvarchar(200)", nullable: false, comment: "變動項目"),
                    DetailNotes = table.Column<string>(type: "nvarchar(500)", nullable: false, comment: "明細備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AssetLocationTransferDetail", x => x.DetailId);
                    table.ForeignKey(
                        name: "FK_Pms_AssetLocationTransferDetail_Pms_AssetLocationTransfer_TransferId",
                        column: x => x.TransferId,
                        principalTable: "Pms_AssetLocationTransfer",
                        principalColumn: "TransferId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Pms_AssetLocationTransferDetail_Pms_Assets_AssetId",
                        column: x => x.AssetId,
                        principalTable: "Pms_Assets",
                        principalColumn: "AssetId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PmsAssetCarryOut",
                columns: table => new
                {
                    CarryOutId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CarryOutNo = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "攜出申請單號"),
                    AssetId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ApplicantId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "攜出申請人"),
                    ApplicationDate = table.Column<long>(type: "bigint", nullable: false, comment: "攜出申請日期"),
                    PlannedCarryOutDate = table.Column<long>(type: "bigint", nullable: false, comment: "預計攜出日期"),
                    PlannedReturnDate = table.Column<long>(type: "bigint", nullable: false, comment: "預計歸還日期"),
                    ActualCarryOutDate = table.Column<long>(type: "bigint", nullable: true, comment: "實際攜出日期"),
                    ActualReturnDate = table.Column<long>(type: "bigint", nullable: true, comment: "實際歸還日期"),
                    Purpose = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false, comment: "攜出目的"),
                    Destination = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "攜出地點"),
                    Status = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "攜出狀態"),
                    ApproverId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true, comment: "審核人員"),
                    ApprovalDate = table.Column<long>(type: "bigint", nullable: true, comment: "審核日期"),
                    ApprovalComment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "審核意見"),
                    Notes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "備註"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PmsAssetCarryOut", x => x.CarryOutId);
                    table.ForeignKey(
                        name: "FK_PmsAssetCarryOut_Pms_Assets_AssetId",
                        column: x => x.AssetId,
                        principalTable: "Pms_Assets",
                        principalColumn: "AssetId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VendorMaintenance",
                columns: table => new
                {
                    MaintenanceNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "修繕單號"),
                    AssetId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產流水號"),
                    ApplicantId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "申請人編號"),
                    DepartmentId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "申請部門編號"),
                    ApplicationDate = table.Column<long>(type: "bigint", nullable: false, comment: "申請日期"),
                    FaultDescription = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false, comment: "故障描述"),
                    MaintenanceType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "修繕類型"),
                    UrgencyLevel = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "緊急程度"),
                    EstimatedCost = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "預估修繕費用"),
                    VendorName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "廠商名稱"),
                    VendorContact = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true, comment: "廠商聯絡人"),
                    VendorPhone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "廠商聯絡電話"),
                    ScheduledStartDate = table.Column<long>(type: "bigint", nullable: false),
                    ScheduledEndDate = table.Column<long>(type: "bigint", nullable: false),
                    ActualStartDate = table.Column<long>(type: "bigint", nullable: false),
                    ActualEndDate = table.Column<long>(type: "bigint", nullable: false),
                    ActualCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    MaintenanceResult = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    InspectorId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "驗收人編號"),
                    InspectionDate = table.Column<long>(type: "bigint", nullable: false),
                    InspectionResult = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    InspectionNotes = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Status = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ApproverId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "審核人編號"),
                    ApprovalDate = table.Column<long>(type: "bigint", nullable: false),
                    ApprovalComment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VendorMaintenance", x => x.MaintenanceNumber);
                    table.ForeignKey(
                        name: "FK_VendorMaintenance_Pms_Assets_AssetId",
                        column: x => x.AssetId,
                        principalTable: "Pms_Assets",
                        principalColumn: "AssetId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AssetAssetSourceMapping",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AssetId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產流水號"),
                    AssetSourceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產來源編號"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AssetAssetSourceMapping", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Pms_AssetAssetSourceMapping_Pms_AssetSources_AssetSourceId",
                        column: x => x.AssetSourceId,
                        principalTable: "Pms_AssetSources",
                        principalColumn: "AssetSourceId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Pms_AssetAssetSourceMapping_Pms_Assets_AssetId",
                        column: x => x.AssetId,
                        principalTable: "Pms_Assets",
                        principalColumn: "AssetId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Pms_AssetInsuranceUnitMapping",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AssetId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產流水號"),
                    InsuranceUnitId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "承保單位編號"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pms_AssetInsuranceUnitMapping", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Pms_AssetInsuranceUnitMapping_Pms_Assets_AssetId",
                        column: x => x.AssetId,
                        principalTable: "Pms_Assets",
                        principalColumn: "AssetId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Pms_AssetInsuranceUnitMapping_Pms_InsuranceUnits_InsuranceUnitId",
                        column: x => x.InsuranceUnitId,
                        principalTable: "Pms_InsuranceUnits",
                        principalColumn: "InsuranceUnitId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PmsUserRoleMappings",
                columns: table => new
                {
                    PmsUserRoleMappingId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "對應編號"),
                    UserId = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "使用者編號"),
                    PmsUserRoleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "財產系統使用者身分編號"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PmsUserRoleMappings", x => x.PmsUserRoleMappingId);
                    table.ForeignKey(
                        name: "FK_PmsUserRoleMappings_Common_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Common_Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PmsUserRoleMappings_PmsUserRoles_PmsUserRoleId",
                        column: x => x.PmsUserRoleId,
                        principalTable: "PmsUserRoles",
                        principalColumn: "PmsUserRoleId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Ims_ItemPrice",
                columns: table => new
                {
                    ItemPriceID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "售價編號"),
                    ItemID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "庫存品編號"),
                    PriceTypeID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "價格類別"),
                    Price = table.Column<decimal>(type: "decimal(38,8)", nullable: false, comment: "售價"),
                    CreateTime = table.Column<long>(type: "bigint", nullable: true, comment: "新增時間"),
                    CreateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "新增者編號"),
                    UpdateTime = table.Column<long>(type: "bigint", nullable: true, comment: "更新時間"),
                    UpdateUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "更新者編號"),
                    DeleteTime = table.Column<long>(type: "bigint", nullable: true, comment: "刪除時間"),
                    DeleteUserId = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "刪除者編號"),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, comment: "刪除狀態")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_ItemPrice", x => x.ItemPriceID);
                    table.ForeignKey(
                        name: "FK_Ims_ItemPrice_Ims_Item_ItemID",
                        column: x => x.ItemID,
                        principalTable: "Ims_Item",
                        principalColumn: "ItemID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Ims_ItemPrice_Ims_PriceType_PriceTypeID",
                        column: x => x.PriceTypeID,
                        principalTable: "Ims_PriceType",
                        principalColumn: "PriceTypeID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Ims_CustomerDetail",
                columns: table => new
                {
                    PartnerID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "商業夥伴編號"),
                    CustomerCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "客戶代碼"),
                    CustomerCategoryID = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "客戶分類編號"),
                    SettlementDay = table.Column<int>(type: "int", nullable: true, comment: "應收結帳日")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_CustomerDetail", x => x.PartnerID);
                    table.ForeignKey(
                        name: "FK_Ims_CustomerDetail_Ims_CustomerCategory_CustomerCategoryID",
                        column: x => x.CustomerCategoryID,
                        principalTable: "Ims_CustomerCategory",
                        principalColumn: "CustomerCategoryID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Ims_CustomerDetail_Ims_Partner_PartnerID",
                        column: x => x.PartnerID,
                        principalTable: "Ims_Partner",
                        principalColumn: "PartnerID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Ims_EnterpriseDetail",
                columns: table => new
                {
                    PartnerID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "商業夥伴編號"),
                    CompanyName = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "公司名稱"),
                    BussinessID = table.Column<string>(type: "nvarchar(20)", nullable: true, comment: "統一編號"),
                    TaxID = table.Column<string>(type: "nvarchar(20)", nullable: true, comment: "稅籍編號"),
                    ResponsiblePerson = table.Column<string>(type: "nvarchar(20)", nullable: true, comment: "負責人")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_EnterpriseDetail", x => x.PartnerID);
                    table.ForeignKey(
                        name: "FK_Ims_EnterpriseDetail_Ims_Partner_PartnerID",
                        column: x => x.PartnerID,
                        principalTable: "Ims_Partner",
                        principalColumn: "PartnerID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Ims_IndividualDetail",
                columns: table => new
                {
                    PartnerID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "商業夥伴編號"),
                    LastName = table.Column<string>(type: "nvarchar(50)", nullable: true, comment: "姓氏"),
                    FirstName = table.Column<string>(type: "nvarchar(50)", nullable: true, comment: "名字"),
                    IdentificationNumber = table.Column<string>(type: "nvarchar(20)", nullable: true, comment: "身分證字號"),
                    BirthDate = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "出生日期")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_IndividualDetail", x => x.PartnerID);
                    table.ForeignKey(
                        name: "FK_Ims_IndividualDetail_Ims_Partner_PartnerID",
                        column: x => x.PartnerID,
                        principalTable: "Ims_Partner",
                        principalColumn: "PartnerID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Ims_PartnerAddress",
                columns: table => new
                {
                    PartnerAddressID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "地址編號"),
                    PartnerID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "商業夥伴編號"),
                    Address = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false, comment: "詳細地址"),
                    City = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true, comment: "縣市"),
                    District = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true, comment: "鄉鎮市區"),
                    PostalCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "郵遞區號"),
                    Country = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true, comment: "國家地區"),
                    IsPrimary = table.Column<bool>(type: "bit", nullable: false, comment: "是否為主要地址")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_PartnerAddress", x => x.PartnerAddressID);
                    table.ForeignKey(
                        name: "FK_Ims_PartnerAddress_Ims_Partner_PartnerID",
                        column: x => x.PartnerID,
                        principalTable: "Ims_Partner",
                        principalColumn: "PartnerID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Ims_PartnerContact",
                columns: table => new
                {
                    PartnerID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "商業夥伴編號"),
                    ContactID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "聯絡人編號"),
                    ContactRoleID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "聯絡人角色編號"),
                    IsPrimary = table.Column<bool>(type: "bit", nullable: false, comment: "是否為主要聯絡人 (針對此夥伴)")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_PartnerContact", x => new { x.PartnerID, x.ContactID, x.ContactRoleID });
                    table.ForeignKey(
                        name: "FK_Ims_PartnerContact_Ims_ContactRole_ContactRoleID",
                        column: x => x.ContactRoleID,
                        principalTable: "Ims_ContactRole",
                        principalColumn: "ContactRoleID",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Ims_PartnerContact_Ims_Contact_ContactID",
                        column: x => x.ContactID,
                        principalTable: "Ims_Contact",
                        principalColumn: "ContactID",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Ims_PartnerContact_Ims_Partner_PartnerID",
                        column: x => x.PartnerID,
                        principalTable: "Ims_Partner",
                        principalColumn: "PartnerID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Ims_SupplierDetail",
                columns: table => new
                {
                    PartnerID = table.Column<string>(type: "nvarchar(100)", nullable: false, comment: "商業夥伴編號"),
                    SupplierCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "供應商代碼"),
                    SupplierCategoryID = table.Column<string>(type: "nvarchar(100)", nullable: true, comment: "供應商分類編號"),
                    SettlementDay = table.Column<int>(type: "int", nullable: true, comment: "應付結帳日")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ims_SupplierDetail", x => x.PartnerID);
                    table.ForeignKey(
                        name: "FK_Ims_SupplierDetail_Ims_Partner_PartnerID",
                        column: x => x.PartnerID,
                        principalTable: "Ims_Partner",
                        principalColumn: "PartnerID",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Ims_SupplierDetail_Ims_SupplierCategory_SupplierCategoryID",
                        column: x => x.SupplierCategoryID,
                        principalTable: "Ims_SupplierCategory",
                        principalColumn: "SupplierCategoryID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Common_Cities",
                columns: new[] { "CityId", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "Description", "EnglishName", "IsDeleted", "Name", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "2ccecf5d-4f8e-4cd1-8fd3-8238b8968250", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Taipei City", false, "台北市", 100, null, null },
                    { "2f441941-4343-4695-beb5-d72a04798a6f", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Keelung City", false, "基隆市", 200, null, null },
                    { "322ebadc-902c-4b53-aa55-5ba620ce14c7", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Lienchiang County", false, "連江縣", 209, null, null },
                    { "333aad34-e809-4e00-8fa1-ffa32ccdbdf2", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Yilan County", false, "宜蘭縣", 260, null, null },
                    { "4b02d17e-3619-4bf8-8946-5598f5a27dd5", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Diaoyutai", false, "釣魚台", 290, null, null },
                    { "4e3d0307-51cd-421a-8690-fbe10543a824", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Hsinchu City", false, "新竹市", 300, null, null },
                    { "531ddd16-2f17-4afb-8fe7-507c0120f7ea", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Hsinchu County", false, "新竹縣", 302, null, null },
                    { "6b25ee4a-dddb-4bb5-a173-8a3b1930f21d", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Taoyuan County", false, "桃園縣", 320, null, null },
                    { "7d65a2ca-9bed-4446-97e2-26a8b0e19b85", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Miaoli County", false, "苗栗縣", 350, null, null },
                    { "7e5dedeb-279b-4bec-8440-fe1918a23845", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Taichung City", false, "台中市", 400, null, null },
                    { "800ad98a-c75f-400e-9068-7f8903b585a5", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Changhua County", false, "彰化縣", 500, null, null },
                    { "8052bb63-1c98-4699-a760-ed77c8d75f01", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Nantou County", false, "南投縣", 540, null, null },
                    { "82427f35-9c7a-40ad-aa15-361435cb68af", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Chiayi City", false, "嘉義市", 600, null, null },
                    { "8cae2fd4-4089-40aa-9e74-d73deadab984", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Chiayi County", false, "嘉義縣", 602, null, null },
                    { "8f17e952-9ace-4835-b0e7-ec1f70c5bd5c", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Yunlin County", false, "雲林縣", 630, null, null },
                    { "9159bec5-b912-465e-a98e-72b6e5396ea2", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Tainan City", false, "台南市", 700, null, null },
                    { "9bde99df-d8f3-43ee-8595-4203ae7f3aa3", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Kaohsiung City", false, "高雄市", 800, null, null },
                    { "b8550f03-8d8f-40dd-b0ca-40104dd955c2", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Nanhai Islands", false, "南海島", 817, null, null },
                    { "bad085a9-72ae-4aab-99b6-40e396faa25c", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Penghu County", false, "澎湖縣", 880, null, null },
                    { "bfa05fb5-005e-47bf-8126-d3a58e6fee64", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Kinmen County", false, "金門縣", 890, null, null },
                    { "c7af2fb6-999f-45ee-a52a-2676072de25c", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Pingtung County", false, "屏東縣", 900, null, null },
                    { "c7eb7fa7-896c-4d52-b378-2f410db9a319", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Taitung County", false, "台東縣", 950, null, null },
                    { "d2a37ceb-07d3-482c-a7d0-854104fb6bac", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "", "Hualien County", false, "花蓮縣", 970, null, null }
                });

            migrationBuilder.InsertData(
                table: "Common_Departments",
                columns: new[] { "DepartmentId", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "EnterpriseGroupId", "IsDeleted", "Name", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "244fe635-16d2-490e-9a08-c0ae2a4f260e", 1742454109L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "信用部", 0, null, "" },
                    { "3e1c6d5f-2b4a-47f8-91ae-c7d8f0b1e2a3", 1742454091L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "老爺辦事處", 0, null, "" },
                    { "a1b2c3d4-e5f6-7890-1234-567890abcdef", 1742454091L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "保險部", 0, null, "" },
                    { "a7d9c4f1-5e8b-4c3d-a2f0-9b6c1d7e4f5a", 1742454091L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "茂林辦事處", 0, null, "" },
                    { "bf4dadda-59b1-41c6-8eaa-bac176124017", 1742454097L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "供銷部", 0, null, "" },
                    { "c404fc72-c566-43ed-8080-dcf943b4a0b2", 1742454103L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "推廣部", 0, null, "" },
                    { "d4f7a9b2-1c3e-4d6f-8a0b-9e2c5f1a7d3b", 1742454091L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "鎮北辦事處", 0, null, "" },
                    { "e0f1d2c3-b4a5-6789-9876-543210fedcba", 1742454091L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "過埤辦事處", 0, null, "" },
                    { "e1ac337b-d154-4e23-b603-cc51af2b995a", 1742454114L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "五甲辦事處", 0, null, "" },
                    { "e2ea86da-2172-493d-89cc-35f6a6bc6731", 1742454091L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "文山辦事處", 0, null, "" },
                    { "f2a8e7c3-9b4d-4a6f-b1d2-3c5e7f8a9b0c", 1742454091L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "東區辦事處", 0, null, "" },
                    { "f7a3b9c2-d6e5-4f1a-8c7b-0e9d1f2a3b4c", 1742454091L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", false, "市場辦事處", 0, null, "" }
                });

            migrationBuilder.InsertData(
                table: "Common_EnterpriseGroups",
                columns: new[] { "EnterpriseGroupsId", "AccountingPeriod", "Address1", "Address2", "CompanyPhone", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "Email", "EnglishAddress", "EnglishName", "EstablishDate", "Fax", "IsDeleted", "MobilePhone", "Name", "Phone", "Representative", "SortCode", "UnifiedNumber", "UpdateTime", "UpdateUserId", "Website" },
                values: new object[] { "8b4d81a9-7f22-49c3-bbda-3886cf28a80d", "12", "830高雄市鳳山區維新路124號", "", "*********", 1742454076L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "<EMAIL>", "", "", 1742454019L, "", false, "", "鳳山區農會", "", "潘建仲", 0, "********", null, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", "" });

            migrationBuilder.InsertData(
                table: "Common_Roles",
                columns: new[] { "RolesId", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "IsDeleted", "Name", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "1558c61a-e196-4811-bc59-3020dadcf91e", 1741852778L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "G3", null, null },
                    { "4fc92853-5cce-48d2-83d5-09ea3bf88087", 1741852767L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "G1", null, null },
                    { "c7cc94e7-51f3-4769-9afc-b4bf0c3b9433", 1741852773L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "G2", null, null }
                });

            migrationBuilder.InsertData(
                table: "Common_SystemGroups",
                columns: new[] { "SystemGroupId", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "IsDeleted", "Name", "Option", "Remark", "SystemCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "09734e61-a7ad-40d6-a0e0-5ef9fc212b25", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "其他系統", "VPwAdIR9iSg2X9irBAXA0sQ4ZL051q8Y0W6zrttzAOrWH71BcNeujK7M17boL6bPyVlSvGSaGMA1FAL3o0nj87b0qu4B1deRzfHRSl6pwtU=", "測試用的", "Other", null, null },
                    { "43c04c10-69cf-48dc-8c08-89def33e480c", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "進銷存系統", "VPwAdIR9iSg2X9irBAXA0sQ4ZL051q8Y0W6zrttzAOrWH71BcNeujK7M17boL6bPyVlSvGSaGMA1FAL3o0nj87b0qu4B1deRzfHRSl6pwtU=", "系統預設資料到1970/01/01 00:00:00", "Ims", null, null },
                    { "55c48a9f-5451-4bdc-8139-67ac55105ac0", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "財產系統", "VPwAdIR9iSg2X9irBAXA0sQ4ZL051q8Y0W6zrttzAOrWH71BcNeujK7M17boL6bPyVlSvGSaGMA1FAL3o0nj87b0qu4B1deRzfHRSl6pwtU=", "系統預設資料到1970/01/01 00:00:00", "Pms", null, null },
                    { "5f933c38-f759-4427-8fcd-713213fdf5ab", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "共用系統", "V0sXS0bkPeTWOeXY7st/SIGVxKPIbzznqd6f3OGjp+CXEM+u2QBtM6P+liq0d8eFFgx8G7guYOl8RMgj2G0jXHtY5GoCF31ny3kzAE5FtLkl+Mz6Cy+jEVbN1MN29F1p", "系統預設資料到2099/12/31 23:59:59", "Common", null, null },
                    { "F1732C06-A239-4211-ABFD-E3C319DF071B", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "人事系統", "VPwAdIR9iSg2X9irBAXA0sQ4ZL051q8Y0W6zrttzAOrWH71BcNeujK7M17boL6bPyVlSvGSaGMA1FAL3o0nj87b0qu4B1deRzfHRSl6pwtU=", "系統預設資料到1970/01/01 00:00:00", "Pas", null, null }
                });

            migrationBuilder.InsertData(
                table: "Common_SystemMenu",
                columns: new[] { "SystemMenuId", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "Icon", "IsDeleted", "IsMenu", "Key", "Label", "ParentId", "SystemGroupId", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "072661fa-1740-46f1-b91e-0aa11f371abc", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "security", "權限管理", "6443a2b6-40a1-41b2-98a4-029d47d4720e", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "0cbac110-60b7-497d-93ae-27229ddd5158", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_source", "財產來源", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "0ceb6fed-7704-41bf-a0be-02e094ace908", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_location_change_form", "財產位置變動單", "12743d6f-9736-4f1e-82b3-4b450c9f074b", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "12743d6f-9736-4f1e-82b3-4b450c9f074b", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_document_maintenance", "財產管理", "a44ee353-cdfd-4466-807a-496a8e0748c2", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "13211f5b-94b4-48a8-a049-f7299e393971", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_fixed_asset_scrap_form", "固定資產報廢單", "12743d6f-9736-4f1e-82b3-4b450c9f074b", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "14b79fbd-3e2a-4507-9293-8f44362606bd", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_scrap", "財產報廢清冊", "b40b286a-f47c-4bc9-a3b2-9be04a24afc4", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "19d3fd8a-a412-4a3f-9b03-c3f498e8e543", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_inventory_record", "財產盤點紀錄表", "b40b286a-f47c-4bc9-a3b2-9be04a24afc4", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "1fc16a81-af0f-44cb-b4cf-e61842e8d761", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "TagsOutlined", false, false, "ims_basic_item", "庫存品管理", "c09e6c21-c490-4d4d-b08d-f95605117b72", "43c04c10-69cf-48dc-8c08-89def33e480c", null, null },
                    { "24bb839d-fce2-4291-8331-01f8c3cbe39d", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "other", "其他管理", "6443a2b6-40a1-41b2-98a4-029d47d4720e", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "26434602-d9a0-4abb-bfc4-59da0d542433", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "AliwangwangFilled", false, false, "pas_overview", "功能總覽", "76075bff-f54f-4f32-83d8-0c5a91367779", "F1732C06-A239-4211-ABFD-E3C319DF071B", null, null },
                    { "26434602-d9a0-4abb-bfc4-59da0d54243e", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "BugFilled", false, false, "pas_employee_main", "員工資料主頁", "76075bff-f54f-4f32-83d8-0c5a91367779", "F1732C06-A239-4211-ABFD-E3C319DF071B", null, null },
                    { "27bfec4e-748b-43df-a0ce-07613dccec9e", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_system_parameter_setting", "系統參數", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "2b03d682-68a5-4dae-be43-d83063cbb798", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, true, "companyInfo", "公司資訊", "d07b835d-aae9-4a41-93c1-88396ff9e713", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "2e6eb4d2-2f75-4f66-a9c0-3c080eb4c20c", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_account", "財產科目", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "2f8e9b3a-c7d5-4e6f-a8b2-1d9c0e5f3a7b", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_sub_account", "財產子目", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "31e4aea3-feb7-48bd-8616-e1e45d158652", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_change_improvement", "資產異動改良單", "12743d6f-9736-4f1e-82b3-4b450c9f074b", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "3c4d5dc4-aafe-4634-badb-39bed49f46ce", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "TeamOutlined", false, false, "ims_basic_partner", "商業夥伴管理", "c09e6c21-c490-4d4d-b08d-f95605117b72", "43c04c10-69cf-48dc-8c08-89def33e480c", null, null },
                    { "3d82acc5-51ab-4994-b329-5a5f51b3ffbd", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_fixed_asset_sale_form", "固定資產出售單", "12743d6f-9736-4f1e-82b3-4b450c9f074b", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "3dbcbc5b-ab15-40e4-90b6-54e0420083a8", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "ClockCircleOutlined", false, true, "building", "大樓管理系統", null, "09734e61-a7ad-40d6-a0e0-5ef9fc212b25", null, null },
                    { "6443a2b6-40a1-41b2-98a4-029d47d4720e", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "SettingOutlined", false, true, "system", "系統設定", null, "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "682f1334-cd3b-4259-a807-7e3a06811245", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "reportEmail", "經營報告書", "2b03d682-68a5-4dae-be43-d83063cbb798", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "683fc782-54a1-4832-b6a2-204446d4cb83", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "datainit", "資料初始化", "b9d7b94e-89af-4f25-9fbe-7b802df1aaf3", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "6f4f1511-0ca4-4eb6-b53f-2640637018ef", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "systemMenu", "選單管理", "6443a2b6-40a1-41b2-98a4-029d47d4720e", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "6f5fef7e-5824-4678-99c3-17a69cd3c629", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "users", "用户管理", "6443a2b6-40a1-41b2-98a4-029d47d4720e", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "70e3e8ce-9031-4c27-93f4-cca02a68399b", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_carryout_form", "資產攜出單", "12743d6f-9736-4f1e-82b3-4b450c9f074b", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "719ce213-a84f-4563-b26d-7d14c7481a9d", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_vendor_maintenance_form", "廠商修護單", "12743d6f-9736-4f1e-82b3-4b450c9f074b", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "739cef61-283b-4b6e-81ad-c3676d8b89fd", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "unit", "單位管理", "24bb839d-fce2-4291-8331-01f8c3cbe39d", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "76075bff-f54f-4f32-83d8-0c5a91367779", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "TeamOutlined", false, true, "hr", "人事薪資系統", null, "09734e61-a7ad-40d6-a0e0-5ef9fc212b25", null, null },
                    { "953c7b41-ac61-437a-8243-9a8fefb9da78", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_category", "財產類別", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "9f604141-9db6-4892-a4bd-bf18b67b142f", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_label", "財產標籤", "b40b286a-f47c-4bc9-a3b2-9be04a24afc4", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "a44ee353-cdfd-4466-807a-496a8e0748c2", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "BankOutlined", false, true, "pms_asset", "財產管理系統", null, "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "a4b40480-1058-4e2b-a169-468951156e51", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_inventory_list", "財產盤點清冊", "b40b286a-f47c-4bc9-a3b2-9be04a24afc4", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "ac2b11a7-3a25-4b19-a61b-1c149fed3919", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_parameter_settings", "基本資料管理", "a44ee353-cdfd-4466-807a-496a8e0748c2", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "ad33284b-4e7c-4898-a955-0001a83c160b", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_increase_decrease", "財產增減表", "b40b286a-f47c-4bc9-a3b2-9be04a24afc4", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "ae454e3e-6605-4ce5-b68a-d7a576def0bd", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_fixed_asset_maintenance_form", "固定資產維護單", "12743d6f-9736-4f1e-82b3-4b450c9f074b", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "b40b286a-f47c-4bc9-a3b2-9be04a24afc4", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_report", "報表列印", "a44ee353-cdfd-4466-807a-496a8e0748c2", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "b7d828d7-ee73-4a55-af7c-80fb2099ea69", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_storage_location", "存放位置", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "b8c09d28-abf6-438b-a1ac-0b4bae0352a1", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "enterpriseGroups", "公司基本資訊", "2b03d682-68a5-4dae-be43-d83063cbb798", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "b9d7b94e-89af-4f25-9fbe-7b802df1aaf3", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, true, "dataManagement", "資料管理", "d07b835d-aae9-4a41-93c1-88396ff9e713", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "b9e5f2a3-d1c7-4b8e-9f6a-2d4c8e7b3a5d", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_manufacturer", "廠牌型號", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "bbb6bd69-d440-4199-ba90-becae506cab3", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "ShopOutlined", false, true, "ims", "進銷存管理系統", null, "43c04c10-69cf-48dc-8c08-89def33e480c", null, null },
                    { "bd752f16-c6a6-48aa-836e-49cc32d090e0", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_fixed_asset_depreciation_form", "固定資產折舊單", "12743d6f-9736-4f1e-82b3-4b450c9f074b", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "c09e6c21-c490-4d4d-b08d-f95605117b72", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "FolderOpenOutlined", false, true, "ims_basic", "基本資料", "bbb6bd69-d440-4199-ba90-becae506cab3", "43c04c10-69cf-48dc-8c08-89def33e480c", null, null },
                    { "c0f6a3b4-d5e7-4c8e-9f0a-2d3c4e5f6a7b", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_user_role", "保管人&使用人", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "c429f7cc-a732-4fba-81f0-a6683dd5bb11", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_inventory", "財產盤點明細表", "b40b286a-f47c-4bc9-a3b2-9be04a24afc4", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "c4ec0a7e-694d-4e83-9af6-c028dad6fae0", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_depreciation", "財產折舊表", "b40b286a-f47c-4bc9-a3b2-9be04a24afc4", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "c7eaaa00-505b-48a7-9435-7c0c99757a75", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_accessory_equipment", "附屬設備", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "c85e62d2-5eeb-4966-b240-cf84354d9504", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "enterpriseImage", "公司商標&印章", "2b03d682-68a5-4dae-be43-d83063cbb798", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "c8b9f51d-41a1-4e4e-8672-91a4dc9e3c5d", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_batch_import", "財產整批轉檔", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "cb38dffd-0960-4d62-8eef-e90cf91130a1", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_card", "財產卡", "b40b286a-f47c-4bc9-a3b2-9be04a24afc4", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "d07b835d-aae9-4a41-93c1-88396ff9e713", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, true, "info", "資訊管理", "6443a2b6-40a1-41b2-98a4-029d47d4720e", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "ec103869-1245-4eb9-b865-0042d3564069", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_amortization_source", "攤提來源", "ac2b11a7-3a25-4b19-a61b-1c149fed3919", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "f1b9432f-627b-43e7-a937-8e5da1216a02", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "databaseBackup", "資料庫備份", "b9d7b94e-89af-4f25-9fbe-7b802df1aaf3", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "f537f85c-1798-4afd-8374-b9e57639cc7a", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "apiKey", "簽發API認證金鑰", "b9d7b94e-89af-4f25-9fbe-7b802df1aaf3", "5f933c38-f759-4427-8fcd-713213fdf5ab", null, null },
                    { "f5391e4e-c8f4-4d46-84dd-caeef53b30eb", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, null, false, false, "pms_asset_list", "財產清冊", "b40b286a-f47c-4bc9-a3b2-9be04a24afc4", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null },
                    { "f93d2b8e-8e4f-4df3-9c1e-61b9e2f8cb9a", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "AliwangwangFilled", false, false, "pms", "功能總覽", "a44ee353-cdfd-4466-807a-496a8e0748c2", "55c48a9f-5451-4bdc-8139-67ac55105ac0", null, null }
                });

            migrationBuilder.InsertData(
                table: "Common_Units",
                columns: new[] { "UnitId", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "IsDeleted", "Name", "SortCode", "UnitNo", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { new Guid("a1b2c3d4-e5f6-4a5b-8c9d-1e2f3a4b5c6d"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "個", 0, "PCS", null, null },
                    { new Guid("a7b8c9d0-e1f2-0a1b-4c5d-7e8f9a0b1c2d"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "捲", 6, "ROLL", null, null },
                    { new Guid("a7b8c9d0-e1f2-3456-7890-fedcba987654"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "輛", 2, "LIANG", null, null },
                    { new Guid("b1e4c7a9-3d2f-46a0-8f9b-7c6d1e2a3f4b"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "坪", 10, "METER", null, null },
                    { new Guid("b2c3d4e5-f6a7-5b6c-9d0e-2f3a4b5c6d7e"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "組", 1, "SET", null, null },
                    { new Guid("b8c9d0e1-f2a3-1b2c-5d6e-8f9a0b1c2d3e"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "瓶", 7, "BOTTLE", null, null },
                    { new Guid("c3d4e5f6-a7b8-6c7d-0e1f-3a4b5c6d7e8f"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "箱", 2, "BOX", null, null },
                    { new Guid("c9d0e1f2-a3b4-2c3d-6e7f-9a0b1c2d3e4f"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "雙", 8, "PAIR", null, null },
                    { new Guid("d0e1f2a3-b4c5-3d4e-7f8a-0b1c2d3e4f5a"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "張", 9, "SHEET", null, null },
                    { new Guid("d4e5f6a7-b8c9-7d8e-1f2a-4b5c6d7e8f9a"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "公斤", 3, "KG", null, null },
                    { new Guid("e5f6a7b8-c9d0-8e9f-2a3b-5c6d7e8f9a0b"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "公尺", 4, "M", null, null },
                    { new Guid("e9f0d1c2-b3a4-5678-9012-34567890abcd"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "台", 1, "TAI", null, null },
                    { new Guid("f6a7b8c9-d0e1-9f0a-3b4c-6d7e8f9a0b1c"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "包", 5, "PACK", null, null }
                });

            migrationBuilder.InsertData(
                table: "Common_Users",
                columns: new[] { "UserId", "Account", "AltPhone", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "EMail", "EnterpriseGroupId", "IsDeleted", "MailingAddress", "Name", "Password", "PermanentAddress", "Phone", "PositionId", "RolesId", "SortCode", "TelNo", "UnlockTime", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", "FastAdmin", "", null, null, null, null, "", "", false, "", "南農中心管理者", "+SWMLywijCZyA8svqFs/NA==", "", "", "", "4fc92853-5cce-48d2-83d5-09ea3bf88087", 0, "", null, null, null },
                    { "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d66", "PmsAdmin", "", null, null, null, null, "", "", false, "", "財產系統主辦", "+SWMLywijCZyA8svqFs/NA==", "", "", "", "4fc92853-5cce-48d2-83d5-09ea3bf88087", 0, "", null, null, null }
                });

            migrationBuilder.InsertData(
                table: "Ims_ItemCategory",
                columns: new[] { "ItemCategoryID", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "Description", "IsDeleted", "Name", "ParentID", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[] { "b9937eff-4952-4ebf-af90-982fd25b2803", 1750140789L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "", false, "大", null, 0, null, "" });

            migrationBuilder.InsertData(
                table: "Ims_PriceType",
                columns: new[] { "PriceTypeID", "AllowStop", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "Description", "IsDeleted", "IsStop", "Name", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "972a6670-af33-4e38-8e4b-62e0da5d539b", false, 1749814696L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "系統預設", false, false, "會員", 1, null, null },
                    { "c14a69b6-f866-4461-8f63-147296e2abe4", false, 1749814668L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "系統預設", false, false, "一般", 0, null, null },
                    { "ee48d0f6-a227-4489-b822-58303d4aee9d", false, 1750058255L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "系統預設", false, false, "大批", 2, null, null }
                });

            migrationBuilder.InsertData(
                table: "PmsUserRoles",
                columns: new[] { "PmsUserRoleId", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "Description", "IsDeleted", "RoleName", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { new Guid("1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "負責保管及管理財產資產", false, "保管人", 1, null, null },
                    { new Guid("2b3c4d5e-6f7a-8b9c-0d1e-2f3a4b5c6d7e"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "實際使用財產資產的人員", false, "使用人", 2, null, null }
                });

            migrationBuilder.InsertData(
                table: "Pms_AmortizationSources",
                columns: new[] { "AmortizationSourceId", "Amount", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "DepartmentId", "Description", "IsDeleted", "SourceName", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { new Guid("e1a1f5b6-4c3b-4d5a-9f1e-2b5a1f5b6c3b"), 100000.00m, 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", 0L, "", "bf4dadda-59b1-41c6-8eaa-bac176124017", "由政府提供的資金補助", false, "政府補助", 0L, "" },
                    { new Guid("f2b2f6b7-5d4c-5e6b-0f2e-3c6b2f6b7d4c"), 50000.00m, 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", 0L, "", "e1ac337b-d154-4e23-b603-cc51af2b995a", "由部門自行採購的資金來源", false, "自行採購", 0L, "" }
                });

            migrationBuilder.InsertData(
                table: "Pms_AssetAccounts",
                columns: new[] { "AssetAccountId", "AssetAccountName", "AssetAccountNo", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "IsDeleted", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { new Guid("1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"), "土地", "1", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("2a3b4c5d-6e7f-8a9b-0c1d-2e3f4a5b6c7d"), "房屋及建築", "2", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d"), "機器及設備", "3", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("4a5b6c7d-8e9f-0a1b-2c3d-4e5f6a7b8c9d"), "電腦設備", "4", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("5a6b7c8d-9e0f-1a2b-3c4d-5e6f7a8b9c0d"), "農林設備", "5", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("6a7b8c9d-0e1f-2a3b-4c5d-6e7f8a9b0c1d"), "畜產設備", "6", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d"), "交通運輸設備", "7", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("8a9b0c1d-2e3f-4a5b-6c7d-8e9f0a1b2c3d"), "雜項設備", "8", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("9a0b1c2d-3e4f-5a6b-7c8d-9e0f1a2b3c4d"), "未完工程", "9", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null }
                });

            migrationBuilder.InsertData(
                table: "Pms_AssetCategory",
                columns: new[] { "AssetCategoryId", "AssetCategoryName", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "IsDeleted", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "A", "固定資產", 0L, "", 0L, "", false, 0, 0L, "" },
                    { "N", "非固定資產", 0L, "", 0L, "", false, 0, 0L, "" }
                });

            migrationBuilder.InsertData(
                table: "Pms_AssetSources",
                columns: new[] { "AssetSourceId", "AssetSourceName", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "IsDeleted", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { new Guid("308dffc2-0b7b-48d0-9fe1-155e08d36011"), "其他來源", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("6a4d84aa-9a4e-4d65-aad2-5cffeddca95a"), "自行採購", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("7e8b99bf-f254-4d6f-be39-20bd1892d690"), "調撥轉入", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("8ea785e3-5743-4f61-9cd7-cee8dae3b3d9"), "租賃", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("dd50750d-fd8b-4b75-901a-c6ae6a58892b"), "捐贈", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("f5eacef8-aebb-4416-9757-44c64e3bf373"), "政府補助", 0L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null }
                });

            migrationBuilder.InsertData(
                table: "Pms_AssetStatus",
                columns: new[] { "AssetStatusId", "AssetStatusNo", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "IsDeleted", "Name", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { new Guid("a1b2c3d4-e5f6-4a5b-8c9d-1e2f3a4b5c6d"), "N", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "正常使用", 0, null, null },
                    { new Guid("a7b8c9d0-e1f2-0a1b-4c5d-7e8f9a0b1c2d"), "WA", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "待驗收", 6, null, null },
                    { new Guid("b2c3d4e5-f6a7-5b6c-9d0e-2f3a4b5c6d7e"), "I", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "閒置中", 1, null, null },
                    { new Guid("b8c9d0e1-f2a3-1b2c-5d6e-8f9a0b1c2d3e"), "ST", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "庫存中", 7, null, null },
                    { new Guid("c3d4e5f6-a7b8-6c7d-0e1f-3a4b5c6d7e8f"), "M", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "維修中", 2, null, null },
                    { new Guid("d4e5f6a7-b8c9-7d8e-1f2a-4b5c6d7e8f9a"), "R", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "租借中", 3, null, null },
                    { new Guid("e5f6a7b8-c9d0-8e9f-2a3b-5c6d7e8f9a0b"), "WS", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "待報廢", 4, null, null },
                    { new Guid("f6a7b8c9-d0e1-9f0a-3b4c-6d7e8f9a0b1c"), "S", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "已報廢", 5, null, null }
                });

            migrationBuilder.InsertData(
                table: "Pms_AssetSubAccounts",
                columns: new[] { "AssetSubAccountId", "AssetAccountId", "AssetSubAccountName", "AssetSubAccountNo", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "IsDeleted", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { new Guid("1e9d2e7d-1c3b-4a5f-8e0c-9b0a1c2d3e4f"), new Guid("3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d"), "其他", "03", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 6, null, null },
                    { new Guid("3f1a2b4c-5d6e-7f8a-9b0c-1d2e3f4a5b6c"), new Guid("7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d"), "堆高機", "03", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 14, null, null },
                    { new Guid("a1c4e3b2-8d7f-4e6a-9b5c-3f2d1e0a4b8c"), new Guid("1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"), "農用地", "02", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 1, null, null },
                    { new Guid("a4f6c8e0-3b1f-5e7a-8f9b-1c2d3e4f5a6b"), new Guid("7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d"), "小客車", "02", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 13, null, null },
                    { new Guid("a8c0e2f4-7b5d-9e1a-2f3b-5c6d7e8f9a0b"), new Guid("4a5b6c7d-8e9f-0a1b-2c3d-4e5f6a7b8c9d"), "伺服器", "02", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 7, null, null },
                    { new Guid("b3d5f7e9-2a1c-4b8d-6e3f-5a4c2b1d0e9f"), new Guid("2a3b4c5d-6e7f-8a9b-0c1d-2e3f4a5b6c7d"), "辦公大樓", "01", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 2, null, null },
                    { new Guid("b5a7d9f1-4c2a-6f8b-9a0c-2d3e4f5a6b7c"), new Guid("8a9b0c1d-2e3f-4a5b-6c7d-8e9f0a1b2c3d"), "辦公設備", "01", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 14, null, null },
                    { new Guid("b9d1f3e5-8c6a-0f2b-3a4c-6d7e8f9a0b1c"), new Guid("5a6b7c8d-9e0f-1a2b-3c4d-5e6f7a8b9c0d"), "灌溉設備", "01", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 8, null, null },
                    { new Guid("c0e2f4a6-9d7b-1a3c-4b5d-7e8f9a0b1c2d"), new Guid("5a6b7c8d-9e0f-1a2b-3c4d-5e6f7a8b9c0d"), "溫室設備", "02", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 9, null, null },
                    { new Guid("c4e6a8b0-3f2d-5c7e-8b9a-1d4f2e3a5c7b"), new Guid("2a3b4c5d-6e7f-8a9b-0c1d-2e3f4a5b6c7d"), "倉庫", "02", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 3, null, null },
                    { new Guid("c6b8e0a2-5d3b-7a9c-0b1d-3e4f5a6b7c8d"), new Guid("8a9b0c1d-2e3f-4a5b-6c7d-8e9f0a1b2c3d"), "空調設備", "02", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 15, null, null },
                    { new Guid("d1f3e5b7-0e8c-2b4d-5c6e-8f9a0b1c2d3e"), new Guid("6a7b8c9d-0e1f-2a3b-4c5d-6e7f8a9b0c1d"), "飼養設備", "01", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 10, null, null },
                    { new Guid("d5f7b9c1-4e2a-6d8b-9c0e-2f3a4b5d6e7f"), new Guid("3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d"), "農業機械", "01", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 4, null, null },
                    { new Guid("e2f4a6c8-1f9d-3c5e-6d7f-9a0b1c2d3e4f"), new Guid("6a7b8c9d-0e1f-2a3b-4c5d-6e7f8a9b0c1d"), "孵化設備", "02", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 11, null, null },
                    { new Guid("e6a8c0d2-5f3b-7e9c-0d1f-3a4b5c6d7e8f"), new Guid("3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d"), "生產設備", "02", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 5, null, null },
                    { new Guid("f2b6e1a4-5c3d-4e8f-9a7b-2d1c0e3f4a5b"), new Guid("1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"), "一般用地", "01", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 0, null, null },
                    { new Guid("f3e5b7d9-2a0e-4d6f-7e8a-0b1c2d3e4f5a"), new Guid("7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d"), "貨車", "01", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 12, null, null },
                    { new Guid("f5d9a0c1-b2e3-4f5a-8d7c-9b0e1f2a3c4d"), new Guid("4a5b6c7d-8e9f-0a1b-2c3d-4e5f6a7b8c9d"), "其他", "03", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 8, null, null },
                    { new Guid("f7b9d1e3-6a4c-8f0d-1e2a-4b5c6d7e8f9a"), new Guid("4a5b6c7d-8e9f-0a1b-2c3d-4e5f6a7b8c9d"), "個人電腦", "01", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, 6, null, null }
                });

            migrationBuilder.InsertData(
                table: "Pms_EquipmentType",
                columns: new[] { "EquipmentTypeId", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "EquipmentTypeNo", "IsDeleted", "Name", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { new Guid("a1b2c3d4-e5f6-4a5b-8c9d-1e2f3a4b5c6d"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "C", false, "電腦設備", 0, null, null },
                    { new Guid("a7b8c9d0-e1f2-0a1b-4c5d-7e8f9a0b1c2d"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "L", false, "照明設備", 6, null, null },
                    { new Guid("b2c3d4e5-f6a7-5b6c-9d0e-2f3a4b5c6d7e"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "O", false, "辦公設備", 1, null, null },
                    { new Guid("b8c9d0e1-f2a3-1b2c-5d6e-8f9a0b1c2d3e"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "CM", false, "會議設備", 7, null, null },
                    { new Guid("c3d4e5f6-a7b8-6c7d-0e1f-3a4b5c6d7e8f"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "T", false, "通訊設備", 2, null, null },
                    { new Guid("c9d0e1f2-a3b4-2c3d-6e7f-9a0b1c2d3e4f"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "S", false, "安全設備", 8, null, null },
                    { new Guid("d0e1f2a3-b4c5-3d4e-7f8a-0b1c2d3e4f5a"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "OTH", false, "其他", 9, null, null },
                    { new Guid("d4e5f6a7-b8c9-7d8e-1f2a-4b5c6d7e8f9a"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "N", false, "網路設備", 3, null, null },
                    { new Guid("e3f2c7a1-4d8e-4f7b-935e-5b0d2f9c7e3a"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "T", false, "交通設備", 9, null, null },
                    { new Guid("e5f6a7b8-c9d0-8e9f-2a3b-5c6d7e8f9a0b"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "M", false, "監控設備", 4, null, null },
                    { new Guid("f6a7b8c9-d0e1-9f0a-3b4c-6d7e8f9a0b1c"), 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "A", false, "空調設備", 5, null, null }
                });

            migrationBuilder.InsertData(
                table: "Pms_Manufacturers",
                columns: new[] { "ManufacturerId", "ContactEmail", "ContactPerson", "ContactPhone", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "Description", "IsDeleted", "ManufacturerName", "Model", "Name", "SortCode", "Supplier", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d", "<EMAIL>", "周工程師", "02-27327988", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "法人級Wi-Fi 6路由器，高速穩定連線", false, "ASUSTeK Computer Inc.", "RT-AX86U", "ASUS", 8, "華碩電腦股份有限公司", null, "" },
                    { "a2e9f6b3-8c71-4d5e-b26f-9f5a8d7c1e3b", "<EMAIL>", "王經理", "02-87939168", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "迷你電腦，節省桌面空間", false, "Lenovo Group Ltd.", "ThinkCentre M70q", "Lenovo", 2, "聯想科技股份有限公司", null, "" },
                    { "a7b8c9d0-e1f2-3a4b-5c6d-7e8f9a0b1c2d", "<EMAIL>", "張經理", "02-45678901", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "LED燈管，可直接替換傳統螢光燈管", false, "OSRAM GmbH", "SubstiTUBE T8 Universal", "Osram", 14, "台灣歐司朗照明股份有限公司", null, "" },
                    { "b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e", "<EMAIL>", "劉工程師", "02-27887799", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "4MP紅外線半球網路攝影機，適合室內監控", false, "Hangzhou Hikvision Digital Technology", "DS-2CD2143G0-I", "HIKVISION", 9, "海康威視數位科技股份有限公司", null, "" },
                    { "b7d4c8e5-9f2a-4d6b-8c3e-1a5f9d0e7b4c", "<EMAIL>", "陳經理", "02-26586868", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "彩色多功能事務機，可列印、掃描、影印、傳真", false, "Xerox Corporation", "WorkCentre 6515", "Xerox", 3, "富士全錄股份有限公司", null, "" },
                    { "b8c9d0e1-f2a3-4b5c-6d7e-8f9a0b1c2d3e", "<EMAIL>", "劉經理", "02-56789012", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "無線藍牙喇叭，適合中小型會議室", false, "Sony Corporation", "SRS-XB43", "SONY", 15, "台灣索尼股份有限公司", null, "" },
                    { "c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f", "<EMAIL>", "張工程師", "02-23145678", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "6MP紅外線變焦半球網路攝影機，適合室外監控", false, "Zhejiang Dahua Technology", "IPC-HDBW4631R-ZS", "Dahua", 10, "大華系統科技股份有限公司", null, "" },
                    { "c5f771fb-a645-4ba1-8c25-ef42ad58ac13", "<EMAIL>", "張維修", "02-27287888", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "法人級桌上型電腦，適合辦公室使用", false, "Dell Inc.", "OptiPlex 7090", "Dell", 0, "台灣戴爾股份有限公司", null, "" },
                    { "c8e5a2f1-7b3d-4c9a-8e6f-2d1b5c4a3f7d", "<EMAIL>", "林小姐", "02-27319090", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "辦公傢俱與文具，提供完整辦公解決方案", false, "KOKUYO Co., Ltd.", "Campus系列", "KOKUYO", 4, "國譽文具股份有限公司", null, "" },
                    { "c9d0e1f2-a3b4-5c6d-7e8f-9a0b1c2d3e4f", "<EMAIL>", "周經理", "02-67890123", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "高階視訊會議系統，適合大型會議室", false, "Logitech International S.A.", "Rally Plus", "Logitech", 16, "台灣羅技電子股份有限公司", null, "" },
                    { "d0e1f2a3-b4c5-6d7e-8f9a-0b1c2d3e4f5a", "<EMAIL>", "許工程師", "02-78901234", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "門禁管理系統，高安全性", false, "dormakaba Group", "Exos 9300", "Kaba", 17, "台灣多瑪科巴股份有限公司", null, "" },
                    { "d4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a", "<EMAIL>", "許經理", "02-27861234", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "變頻冷暖空調，適合辦公室使用", false, "Daikin Industries, Ltd.", "FTXM50UVMA", "Daikin", 11, "大金空調股份有限公司", null, "" },
                    { "d9f4b3e2-6c5a-4b8d-9f7e-1a2c3d4e5f6a", "<EMAIL>", "黃工程師", "02-27196000", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "法人IP電話，高清晰度語音通話", false, "Cisco Systems, Inc.", "IP Phone 8841", "Cisco", 5, "台灣思科系統股份有限公司", null, "" },
                    { "e1f2a3b4-c5d6-7e8f-9a0b-1c2d3e4f5a6b", "<EMAIL>", "王工程師", "02-89012345", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "防盜警報系統，適合辦公室安全防護", false, "Honeywell International Inc.", "Vista-21iP", "Honeywell", 18, "台灣霍尼威爾股份有限公司", null, "" },
                    { "e5f6a7b8-c9d0-1e2f-3a4b-5c6d7e8f9a0b", "<EMAIL>", "楊經理", "02-28756789", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "變頻冷專空調，節能省電", false, "Mitsubishi Electric Corporation", "MSY-GR42VF", "Mitsubishi Electric", 12, "台灣三菱電機股份有限公司", null, "" },
                    { "e8d7c6b5-4a3f-2e1d-9c8b-7a6f5d4e3c2b", "<EMAIL>", "吳專員", "02-27583588", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "法人通訊手機，支援多人視訊會議", false, "Samsung Electronics", "Galaxy A52", "Samsung", 6, "台灣三星電子股份有限公司", null, "" },
                    { "f1d7e39a-0c83-4b4a-9ec5-712b85d27311", "<EMAIL>", "李業務", "02-37897890", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "商務筆記型電腦，適合移動辦公", false, "Hewlett-Packard", "ProBook 450 G8", "HP", 1, "台灣惠普資訊科技有限公司", null, "" },
                    { "f6a7b8c9-d0e1-2f3a-4b5c-6d7e8f9a0b1c", "<EMAIL>", "林經理", "02-34567890", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "LED平板燈，適合辦公室照明", false, "Philips Lighting", "LED Panel RC048B", "Philips", 13, "飛利浦照明股份有限公司", null, "" },
                    { "f7e6d5c4-3b2a-1f9e-8d7c-6b5a4d3e2f1c", "<EMAIL>", "黃工程師", "02-27196000", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "法人級網路交換器，高性能、高安全性", false, "Cisco Systems, Inc.", "Catalyst 9200", "Cisco", 7, "台灣思科系統股份有限公司", null, "" }
                });

            migrationBuilder.InsertData(
                table: "Pms_StorageLocations",
                columns: new[] { "StorageLocationId", "Address", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "Description", "IsDeleted", "Name", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { new Guid("08291a5c-7906-4a7d-9b4c-57d6fd119642"), "高雄市鳳山區中山西路316號1樓", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "行政大樓一樓辦公室及接待區", false, "行政大樓一樓", 0, null, null },
                    { new Guid("18c64fe1-62f8-4e7c-9b8a-0e28e3f4b7c9"), "高雄市鳳山區中山西路316號2樓", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "行政大樓二樓辦公室及會議室", false, "行政大樓二樓", 1, null, null },
                    { new Guid("2a7b9d32-5e4c-4f1d-8c7a-6b3e2a1f8d9c"), "高雄市鳳山區中山西路316號3樓", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "行政大樓三樓行政管理處", false, "行政大樓三樓", 2, null, null },
                    { new Guid("3b8c0e43-6f5d-4a2e-9d8b-7c4f3b2e1d0a"), "高雄市鳳山區中山西路316號4樓", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "行政大樓四樓資訊中心辦公室", false, "行政大樓四樓", 3, null, null },
                    { new Guid("4c9d1f54-7a6e-5b3f-0e9c-8d5a4c3f2e1b"), "高雄市鳳山區中山西路316號B1", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "資訊機房及伺服器區域", false, "資訊機房", 4, null, null },
                    { new Guid("5d0e2a65-8b7f-6c4a-1f0d-9e6b5d4a3f2c"), "高雄市鳳山區中山西路318號", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "一般物品及設備儲存倉庫", false, "一號倉庫", 5, null, null },
                    { new Guid("6e1f3b76-9c8a-7d5b-2a1e-0f7c6e5b4a3d"), "高雄市大寮區光明路1號", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "農業示範及實驗區域", false, "農業示範區", 6, null, null },
                    { new Guid("7f2a4c87-0d9b-8e6c-3b2f-1a8d7f6c5b4e"), "高雄市鳳山區中山西路320號", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "農業設備及物資儲存倉庫", false, "二號倉庫", 7, null, null },
                    { new Guid("8a3b5d98-1e0c-9f7d-4c3a-2b9e8a7d6c5f"), "高雄市鳳山區中山西路322號", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "農業技術研發及實驗中心", false, "研發中心", 8, null, null },
                    { new Guid("9b4c6e09-2f1d-0a8e-5d4b-3c0f9b8e7d6a"), "高雄市鳳山區中山西路324號", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, "教育訓練及會議中心", false, "教育訓練中心", 9, null, null }
                });

            migrationBuilder.InsertData(
                table: "Pms_SystemParameters",
                columns: new[] { "ParameterId", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "IsDeleted", "IsEnabled", "ParameterDescription", "ParameterName", "ParameterType", "ParameterValue", "SortOrder", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "直線法是將資產的成本減去預計殘值後，除以預計使用年限，每年提列相同金額的折舊費用", "直線法", "depreciation_method", "{\"key\":\"straight_line\",\"description\":\"按照資產成本減去殘值後，依耐用年限平均計算每年折舊額\",\"formula\":\"(原始成本-殘值)/耐用年限\",\"isDefault\":true}", 1, null, "" },
                    { "a3b4c5d6-7e8f-9a0b-1c2d-3e4f5a6b7c8d", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "尚未完工，不能提列折舊", "餘額遞減法折舊率-未完工程", "declining_balance_rate", "{\"assetAccountId\":\"9A0B1C2D-3E4F-5A6B-7C8D-9E0F1A2B3C4D\",\"rate\":0}", 18, null, "" },
                    { "a7b8c9d0-1e2f-3a4b-5c6d-7e8f9a0b1c2d", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "中長期設備(參考使用年限:10年)", "餘額遞減法折舊率-機器及設備", "declining_balance_rate", "{\"assetAccountId\":\"3A4B5C6D-7E8F-9A0B-1C2D-3E4F5A6B7C8D\",\"rate\":0.2}", 12, null, "" },
                    { "b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "餘額遞減法是以資產的賬面價值乘以一個固定的折舊率來計算折舊費用，折舊率通常為直線法折舊率的2倍", "餘額遞減法", "depreciation_method", "{\"key\":\"declining_balance\",\"description\":\"按照資產賬面價值乘以一個固定折舊率計算每年折舊額\",\"formula\":\"賬面價值*折舊率\",\"rate\": 0.4,\"isDefault\":false}", 2, null, "" },
                    { "b8c9d0e1-2f3a-4b5c-6d7e-8f9a0b1c2d3e", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "技術更新快，壽命短(參考使用年限:5年)", "餘額遞減法折舊率-電腦設備", "declining_balance_rate", "{\"assetAccountId\":\"4A5B6C7D-8E9F-0A1B-2C3D-4E5F6A7B8C9D\",\"rate\":0.4}", 13, null, "" },
                    { "c9d0e1f2-3a4b-5c6d-7e8f-9a0b1c2d3e4f", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "農用機械或設施(參考使用年限:8年)", "餘額遞減法折舊率-農林設備", "declining_balance_rate", "{\"assetAccountId\":\"5A6B7C8D-9E0F-1A2B-3C4D-5E6F7A8B9C0D\",\"rate\":0.25}", 14, null, "" },
                    { "d0e1f2a3-4b5c-6d7e-8f9a-0b1c2d3e4f5a", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "用於養殖的設施(參考使用年限:6年)", "餘額遞減法折舊率-畜產設備", "declining_balance_rate", "{\"assetAccountId\":\"6A7B8C9D-0E1F-2A3B-4C5D-6E7F8A9B0C1D\",\"rate\":0.33}", 15, null, "" },
                    { "e1f2a3b4-5c6d-7e8f-9a0b-1c2d3e4f5a6b", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "車輛、機車等(參考使用年限:5年)", "餘額遞減法折舊率-交通運輸設備", "declining_balance_rate", "{\"assetAccountId\":\"7A8B9C0D-1E2F-3A4B-5C6D-7E8F9A0B1C2D\",\"rate\":0.4}", 16, null, "" },
                    { "e5f6a7b8-9c0d-1e2f-3a4b-5c6d7e8f9a0b", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "土地不計提折舊", "餘額遞減法折舊率-土地", "declining_balance_rate", "{\"assetAccountId\":\"1A2B3C4D-5E6F-7A8B-9C0D-1E2F3A4B5C6D\",\"rate\":0}", 10, null, "" },
                    { "f2a3b4c5-6d7e-8f9a-0b1c-2d3e4f5a6b7c", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "各類小型設備(參考使用年限:5年)", "餘額遞減法折舊率-雜項設備", "declining_balance_rate", "{\"assetAccountId\":\"8A9B0C1D-2E3F-4A5B-6C7D-8E9F0A1B2C3D\",\"rate\":0.4}", 17, null, "" },
                    { "f6a7b8c9-0d1e-2f3a-4b5c-6d7e8f9a0b1c", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "估計使用壽命長，折舊率低(參考使用年限:50年)", "餘額遞減法折舊率-房屋及建築", "declining_balance_rate", "{\"assetAccountId\":\"2A3B4C5D-6E7F-8A9B-0C1D-2E3F4A5B6C7D\",\"rate\":0.04}", 11, null, "" },
                    { "f7e6d5c4-b3a2-1098-7654-3210fedcba98", 1742266132L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", false, true, "標示系統是否已完成初始化設定", "系統初始化狀態", "initialization", "{\"isInitialized\": false, \"initializationDate\": null}", 0, null, "" }
                });

            migrationBuilder.InsertData(
                table: "Common_RolesPermissions",
                columns: new[] { "RolesPermissionsId", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "IsDeleted", "RolesId", "SystemMenuId", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "04e9189f-c00d-40d6-9176-235ffa63ca41", 1741853872L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "4fc92853-5cce-48d2-83d5-09ea3bf88087", "072661fa-1740-46f1-b91e-0aa11f371abc", null, null },
                    { "0c45247a-ae2c-4462-96bf-d10e5eb281b3", 1741852891L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "4fc92853-5cce-48d2-83d5-09ea3bf88087", "6443a2b6-40a1-41b2-98a4-029d47d4720e", null, null },
                    { "1dec7214-3b90-4318-8784-e4bd41d1330e", 1741914881L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "4fc92853-5cce-48d2-83d5-09ea3bf88087", "2b03d682-68a5-4dae-be43-d83063cbb798", null, null },
                    { "4329d73c-ab18-41e3-b764-5ec1b99446cb", 1741914901L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "4fc92853-5cce-48d2-83d5-09ea3bf88087", "b8c09d28-abf6-438b-a1ac-0b4bae0352a1", null, null },
                    { "6f6432c7-af19-4156-87db-bc94694b0c29", 1741914955L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "4fc92853-5cce-48d2-83d5-09ea3bf88087", "bbb6bd69-d440-4199-ba90-becae506cab3", null, null },
                    { "79b32fdb-0c77-42ac-99f7-85443a03ae64", 1741914567L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "4fc92853-5cce-48d2-83d5-09ea3bf88087", "d07b835d-aae9-4a41-93c1-88396ff9e713", null, null },
                    { "eba8fb41-b67a-4918-b894-872d348b22d2", 1741914891L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, null, false, "4fc92853-5cce-48d2-83d5-09ea3bf88087", "682f1334-cd3b-4259-a807-7e3a06811245", null, null }
                });

            migrationBuilder.InsertData(
                table: "Ims_ItemCategory",
                columns: new[] { "ItemCategoryID", "CreateTime", "CreateUserId", "DeleteTime", "DeleteUserId", "Description", "IsDeleted", "Name", "ParentID", "SortCode", "UpdateTime", "UpdateUserId" },
                values: new object[,]
                {
                    { "ad7c4192-6c7d-4f24-b238-02faed56a507", 1750140800L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "", false, "中", "b9937eff-4952-4ebf-af90-982fd25b2803", 0, null, "" },
                    { "ae790d4b-ef98-43ba-b0b6-a46f115e1f32", 1750140805L, "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65", null, "", "", false, "小", "ad7c4192-6c7d-4f24-b238-02faed56a507", 0, null, "" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Common_FileUploads_EnterpriseGroupsId",
                table: "Common_FileUploads",
                column: "EnterpriseGroupsId");

            migrationBuilder.CreateIndex(
                name: "IX_Common_RolesPermissions_RolesId",
                table: "Common_RolesPermissions",
                column: "RolesId");

            migrationBuilder.CreateIndex(
                name: "IX_Common_RolesPermissions_SystemMenuId",
                table: "Common_RolesPermissions",
                column: "SystemMenuId");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_CustomerCategory_ParentID",
                table: "Ims_CustomerCategory",
                column: "ParentID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_CustomerDetail_CustomerCategoryID",
                table: "Ims_CustomerDetail",
                column: "CustomerCategoryID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_Item_ItemCategoryID",
                table: "Ims_Item",
                column: "ItemCategoryID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_ItemCategory_ParentID",
                table: "Ims_ItemCategory",
                column: "ParentID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_ItemPrice_ItemID",
                table: "Ims_ItemPrice",
                column: "ItemID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_ItemPrice_PriceTypeID",
                table: "Ims_ItemPrice",
                column: "PriceTypeID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_Partner_SupplierCategoryID",
                table: "Ims_Partner",
                column: "SupplierCategoryID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_PartnerAddress_PartnerID",
                table: "Ims_PartnerAddress",
                column: "PartnerID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_PartnerContact_ContactID",
                table: "Ims_PartnerContact",
                column: "ContactID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_PartnerContact_ContactRoleID",
                table: "Ims_PartnerContact",
                column: "ContactRoleID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_SupplierCategory_ParentID",
                table: "Ims_SupplierCategory",
                column: "ParentID");

            migrationBuilder.CreateIndex(
                name: "IX_Ims_SupplierDetail_SupplierCategoryID",
                table: "Ims_SupplierDetail",
                column: "SupplierCategoryID");

            migrationBuilder.CreateIndex(
                name: "IX_Pms_AccessoryEquipments_AssetId",
                table: "Pms_AccessoryEquipments",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_Pms_AssetAmortizationSourceMapping_AmortizationSourceId",
                schema: "dbo",
                table: "Pms_AssetAmortizationSourceMapping",
                column: "AmortizationSourceId");

            migrationBuilder.CreateIndex(
                name: "IX_Pms_AssetAmortizationSourceMapping_AssetId",
                schema: "dbo",
                table: "Pms_AssetAmortizationSourceMapping",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_Pms_AssetAssetSourceMapping_AssetId",
                schema: "dbo",
                table: "Pms_AssetAssetSourceMapping",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_Pms_AssetAssetSourceMapping_AssetSourceId",
                schema: "dbo",
                table: "Pms_AssetAssetSourceMapping",
                column: "AssetSourceId");

            migrationBuilder.CreateIndex(
                name: "IX_Pms_AssetInsuranceUnitMapping_AssetId",
                schema: "dbo",
                table: "Pms_AssetInsuranceUnitMapping",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_Pms_AssetInsuranceUnitMapping_InsuranceUnitId",
                schema: "dbo",
                table: "Pms_AssetInsuranceUnitMapping",
                column: "InsuranceUnitId");

            migrationBuilder.CreateIndex(
                name: "IX_Pms_AssetLocationTransferDetail_AssetId",
                table: "Pms_AssetLocationTransferDetail",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_Pms_AssetLocationTransferDetail_TransferId",
                table: "Pms_AssetLocationTransferDetail",
                column: "TransferId");

            migrationBuilder.CreateIndex(
                name: "IX_PmsAssetCarryOut_AssetId",
                table: "PmsAssetCarryOut",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_PmsUserRoleMappings_PmsUserRoleId",
                table: "PmsUserRoleMappings",
                column: "PmsUserRoleId");

            migrationBuilder.CreateIndex(
                name: "IX_PmsUserRoleMappings_UserId",
                table: "PmsUserRoleMappings",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VendorMaintenance_AssetId",
                table: "VendorMaintenance",
                column: "AssetId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Common_AuditLogs");

            migrationBuilder.DropTable(
                name: "Common_Cities");

            migrationBuilder.DropTable(
                name: "Common_Departments");

            migrationBuilder.DropTable(
                name: "Common_Districts");

            migrationBuilder.DropTable(
                name: "Common_Divisions");

            migrationBuilder.DropTable(
                name: "Common_EnterpriseImage");

            migrationBuilder.DropTable(
                name: "Common_FileList");

            migrationBuilder.DropTable(
                name: "Common_FileUploads");

            migrationBuilder.DropTable(
                name: "Common_Positions");

            migrationBuilder.DropTable(
                name: "Common_RolesPermissions");

            migrationBuilder.DropTable(
                name: "Common_SystemGroups");

            migrationBuilder.DropTable(
                name: "Common_SystemParameters");

            migrationBuilder.DropTable(
                name: "Common_SystemParametersItem");

            migrationBuilder.DropTable(
                name: "Common_Units");

            migrationBuilder.DropTable(
                name: "Ims_CustomerDetail");

            migrationBuilder.DropTable(
                name: "Ims_EnterpriseDetail");

            migrationBuilder.DropTable(
                name: "Ims_IndividualDetail");

            migrationBuilder.DropTable(
                name: "Ims_ItemPrice");

            migrationBuilder.DropTable(
                name: "Ims_PartnerAddress");

            migrationBuilder.DropTable(
                name: "Ims_PartnerContact");

            migrationBuilder.DropTable(
                name: "Ims_SupplierDetail");

            migrationBuilder.DropTable(
                name: "Pas_Certification");

            migrationBuilder.DropTable(
                name: "Pas_Dependent");

            migrationBuilder.DropTable(
                name: "Pas_Education");

            migrationBuilder.DropTable(
                name: "Pas_Employee");

            migrationBuilder.DropTable(
                name: "Pas_EmployeeRegularSalary");

            migrationBuilder.DropTable(
                name: "Pas_Ensure");

            migrationBuilder.DropTable(
                name: "Pas_Examination");

            migrationBuilder.DropTable(
                name: "Pas_ExpenseDepartmentChange");

            migrationBuilder.DropTable(
                name: "Pas_Hensure");

            migrationBuilder.DropTable(
                name: "Pas_InsuranceGrade");

            migrationBuilder.DropTable(
                name: "Pas_InsuranceHistory");

            migrationBuilder.DropTable(
                name: "Pas_PerformancePointGroup");

            migrationBuilder.DropTable(
                name: "Pas_PerformancePointRecord");

            migrationBuilder.DropTable(
                name: "Pas_PerformancePointType");

            migrationBuilder.DropTable(
                name: "Pas_Promotion");

            migrationBuilder.DropTable(
                name: "Pas_RegularSalaryItem");

            migrationBuilder.DropTable(
                name: "Pas_Salary");

            migrationBuilder.DropTable(
                name: "Pas_SalaryPoint");

            migrationBuilder.DropTable(
                name: "Pas_ServiceDepartmentChange");

            migrationBuilder.DropTable(
                name: "Pas_Suspend");

            migrationBuilder.DropTable(
                name: "Pas_Train");

            migrationBuilder.DropTable(
                name: "Pas_Undergo");

            migrationBuilder.DropTable(
                name: "Pms_AccessoryEquipments");

            migrationBuilder.DropTable(
                name: "Pms_AssetAccounts");

            migrationBuilder.DropTable(
                name: "Pms_AssetAmortizationSourceMapping",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "Pms_AssetAssetSourceMapping",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "Pms_AssetCategory");

            migrationBuilder.DropTable(
                name: "Pms_AssetInsuranceUnitMapping",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "Pms_AssetLocationTransferDetail");

            migrationBuilder.DropTable(
                name: "Pms_AssetStatus");

            migrationBuilder.DropTable(
                name: "Pms_AssetSubAccounts");

            migrationBuilder.DropTable(
                name: "Pms_DepreciationFormDetail");

            migrationBuilder.DropTable(
                name: "Pms_DepreciationForms");

            migrationBuilder.DropTable(
                name: "Pms_EquipmentType");

            migrationBuilder.DropTable(
                name: "Pms_Manufacturers");

            migrationBuilder.DropTable(
                name: "Pms_StorageLocations");

            migrationBuilder.DropTable(
                name: "Pms_SystemParameters");

            migrationBuilder.DropTable(
                name: "PmsAssetCarryOut");

            migrationBuilder.DropTable(
                name: "PmsUserRoleMappings");

            migrationBuilder.DropTable(
                name: "VendorMaintenance");

            migrationBuilder.DropTable(
                name: "Common_EnterpriseGroups");

            migrationBuilder.DropTable(
                name: "Common_Roles");

            migrationBuilder.DropTable(
                name: "Common_SystemMenu");

            migrationBuilder.DropTable(
                name: "Ims_CustomerCategory");

            migrationBuilder.DropTable(
                name: "Ims_Item");

            migrationBuilder.DropTable(
                name: "Ims_PriceType");

            migrationBuilder.DropTable(
                name: "Ims_ContactRole");

            migrationBuilder.DropTable(
                name: "Ims_Contact");

            migrationBuilder.DropTable(
                name: "Ims_Partner");

            migrationBuilder.DropTable(
                name: "Pms_AmortizationSources");

            migrationBuilder.DropTable(
                name: "Pms_AssetSources");

            migrationBuilder.DropTable(
                name: "Pms_InsuranceUnits");

            migrationBuilder.DropTable(
                name: "Pms_AssetLocationTransfer");

            migrationBuilder.DropTable(
                name: "Common_Users");

            migrationBuilder.DropTable(
                name: "PmsUserRoles");

            migrationBuilder.DropTable(
                name: "Pms_Assets");

            migrationBuilder.DropTable(
                name: "Ims_ItemCategory");

            migrationBuilder.DropTable(
                name: "Ims_SupplierCategory");
        }
    }
}
