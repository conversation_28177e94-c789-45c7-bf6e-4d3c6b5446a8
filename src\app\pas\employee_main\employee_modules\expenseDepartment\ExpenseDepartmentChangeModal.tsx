import React, { useEffect } from 'react';
import { Modal, Form, DatePicker, Select, Input, Row, Col, message } from 'antd';
import dayjs from 'dayjs';
import { ExpenseDepartmentChange } from '@/services/pas/PromotionService';

const { Option } = Select;

export interface ExpenseDepartmentChangeModalProps {
    /** Modal是否顯示 */
    open: boolean;
    /** 當前用戶ID */
    userId: string;
    /** 部門選項數據 */
    departmentOptions: Array<{ departmentId: string; name: string; }>;
    /** 當前的開支部門異動數據（編輯時傳入） */
    expenseDepartmentChange?: ExpenseDepartmentChange | null;
    /** 確認回調函數 */
    onOk: (data: ExpenseDepartmentChange) => void;
    /** 取消回調函數 */
    onCancel: () => void;
    /** 載入狀態 */
    loading?: boolean;
}

const ExpenseDepartmentChangeModal: React.FC<ExpenseDepartmentChangeModalProps> = ({
    open,
    userId,
    departmentOptions,
    expenseDepartmentChange,
    onOk,
    onCancel,
    loading = false
}) => {
    const [form] = Form.useForm();

    // 當Modal打開時，設置表單值或重置表單
    useEffect(() => {
        if (open) {
            if (expenseDepartmentChange) {
                // 編輯模式：設置表單值
                form.setFieldsValue({
                    expenseDepartmentId: expenseDepartmentChange.expenseDepartmentId,
                    changeDate: expenseDepartmentChange.changeDate ? dayjs(expenseDepartmentChange.changeDate) : null,
                    effectiveDate: expenseDepartmentChange.effectiveDate ? dayjs(expenseDepartmentChange.effectiveDate) : null,
                    changeReason: expenseDepartmentChange.changeReason,
                    remark: expenseDepartmentChange.remark,
                });
            } else {
                // 新增模式：重置表單
                form.resetFields();
            }
        } else {
            // Modal關閉時重置表單
            form.resetFields();
        }
    }, [open, expenseDepartmentChange, form]);

    const handleOk = async () => {
        try {
            // 檢查必要的 props
            if (!departmentOptions || departmentOptions.length === 0) {
                message.error('部門選項未載入，請稍後再試');
                return;
            }

            const values = await form.validateFields();
            const departmentName = departmentOptions.find(dept => dept.departmentId === values.expenseDepartmentId)?.name || '';

            if (!departmentName) {
                message.error('無法找到所選部門，請重新選擇');
                return;
            }

            const expenseData: ExpenseDepartmentChange = {
                uid: expenseDepartmentChange?.uid || '',
                userId: userId,
                expenseDepartmentId: values.expenseDepartmentId,
                expenseDepartmentName: departmentName,
                changeDate: values.changeDate ? values.changeDate.format('YYYY-MM-DD') : '',
                effectiveDate: values.effectiveDate ? values.effectiveDate.format('YYYY-MM-DD') : '',
                changeReason: values.changeReason || '',
                remark: values.remark || ''
            };

            onOk(expenseData);
        } catch (error) {
            console.error('設定開支部門異動失敗:', error);
            if (error instanceof Error) {
                message.error(error.message || '設定開支部門異動失敗，請檢查輸入資料');
            } else {
                message.error('設定開支部門異動失敗，請稍後再試');
            }
        }
    };

    const handleCancel = () => {
        onCancel();
    };

    return (
        <Modal
            title="設定開支部門異動"
            open={open}
            onOk={handleOk}
            onCancel={handleCancel}
            confirmLoading={loading}
            width={600}
            maskClosable={false}
        >
            <Form
                form={form}
                layout="vertical"
                style={{ marginTop: 16 }}
            >
                <Row gutter={16}>
                    <Col span={24}>
                        <Form.Item
                            label="開支部門"
                            name="expenseDepartmentId"
                            rules={[{ required: true, message: '請選擇開支部門' }]}
                        >
                            <Select
                                placeholder="請選擇開支部門"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.children?.toString() || '')?.toLowerCase()?.includes(input.toLowerCase())
                                }
                            >
                                {departmentOptions.map(dept => (
                                    <Option key={dept.departmentId} value={dept.departmentId}>
                                        {dept.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="異動日期"
                            name="changeDate"
                            rules={[{ required: true, message: '請選擇異動日期' }]}
                        >
                            <DatePicker placeholder="請選擇異動日期" style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="生效日期"
                            name="effectiveDate"
                            rules={[{ required: true, message: '請選擇生效日期' }]}
                        >
                            <DatePicker placeholder="請選擇生效日期" style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item
                            label="異動原因"
                            name="changeReason"
                            rules={[
                                { max: 200, message: '異動原因不能超過200個字元' }
                            ]}
                        >
                            <Input
                                placeholder="請輸入異動原因"
                                maxLength={200}
                                showCount
                            />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item
                            label="備註"
                            name="remark"
                            rules={[
                                { max: 500, message: '備註不能超過500個字元' }
                            ]}
                        >
                            <Input.TextArea
                                placeholder="請輸入備註"
                                rows={3}
                                maxLength={500}
                                showCount
                            />
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    );
};

export default ExpenseDepartmentChangeModal; 