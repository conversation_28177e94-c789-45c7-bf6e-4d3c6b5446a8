"use client";

import React from 'react';
import { ContactsOutlined } from '@ant-design/icons';
import GenericCategoryManagement, { GenericCategory, CategoryService, CategoryConfig } from './GenericCategoryManagement';
import { CustomerCategory } from '@/services/ims/partner';
import { addCustomerCategory, editCustomerCategory, deleteCustomerCategory, buildCustomerCategoryTree } from '@/services/ims/CustomerCategoryService';

interface CustomerCategoryAdapterProps {
  visible: boolean;
  onClose: () => void;
  categories: CustomerCategory[];
  onDataChange: () => void;
}

// 客戶分類服務適配器
const customerCategoryService: CategoryService<CustomerCategory> = {
  add: async (category: Partial<CustomerCategory>) => {
    return await addCustomerCategory({
      name: category.name || '',
      description: category.description || '',
      parentID: category.parentID || null,
      sortCode: category.sortCode || 0,
    });
  },
  
  edit: async (category: Partial<CustomerCategory>) => {
    return await editCustomerCategory({
      customerCategoryID: category.customerCategoryID || '',
      name: category.name || '',
      description: category.description || '',
      parentID: category.parentID || null,
      sortCode: category.sortCode || 0,
    });
  },
  
  delete: async (id: string) => {
    return await deleteCustomerCategory(id);
  },
  
  buildTree: (categories: CustomerCategory[]) => {
    return buildCustomerCategoryTree(categories);
  }
};

// 客戶分類配置
const customerCategoryConfig: CategoryConfig = {
  title: '客戶分類管理',
  icon: <ContactsOutlined style={{ color: '#1890ff' }} />,
  emptyMessage: '尚無客戶分類',
  emptyDescription: '點擊右側「新增分類」按鈕開始建立客戶分類結構。',
  entityName: '客戶分類'
};

// 映射函數
const mapToGeneric = (category: CustomerCategory): GenericCategory => ({
  id: category.customerCategoryID,
  name: category.name,
  description: category.description,
  parentID: category.parentID,
  sortCode: category.sortCode,
  children: category.children?.map(mapToGeneric)
});

const mapFromGeneric = (generic: GenericCategory, original?: CustomerCategory): Partial<CustomerCategory> => ({
  customerCategoryID: generic.id || original?.customerCategoryID,
  name: generic.name,
  description: generic.description,
  parentID: generic.parentID,
  sortCode: generic.sortCode
});

const getIdField = (category: CustomerCategory): string => category.customerCategoryID;

const CustomerCategoryAdapter: React.FC<CustomerCategoryAdapterProps> = (props) => {
  return (
    <GenericCategoryManagement
      {...props}
      config={customerCategoryConfig}
      service={customerCategoryService}
      getIdField={getIdField}
      mapToGeneric={mapToGeneric}
      mapFromGeneric={mapFromGeneric}
    />
  );
};

export default CustomerCategoryAdapter;
