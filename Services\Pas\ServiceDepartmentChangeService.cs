using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Server.Tools;

namespace FAST_ERP_Backend.Services.Pas
{
    public class ServiceDepartmentChangeService : IServiceDepartmentChangeService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public ServiceDepartmentChangeService(
            ERPDbContext context,
            Baseform baseform,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<ServiceDepartmentChangeDTO>> GetServiceDepartmentChangeListAsync(string userId)
        {
            var serviceChanges = await _context.Pas_ServiceDepartmentChange
                .Where(s => s.UserId == userId && !s.IsDeleted)
                .OrderByDescending(s => s.CreateTime)
                .ToListAsync();

            var result = new List<ServiceDepartmentChangeDTO>();

            foreach (var change in serviceChanges)
            {
                var dto = new ServiceDepartmentChangeDTO
                {
                    Uid = change.Uid,
                    UserId = change.UserId,
                    ServiceDepartmentId = change.ServiceDepartmentId,
                    ServiceDepartmentName = GetDepartmentName(change.ServiceDepartmentId),
                    ServiceDivisionId = change.ServiceDivisionId,
                    ServiceDivisionName = GetDivisionName(change.ServiceDivisionId),
                    ChangeDate = _baseform.TimestampToDateStr(change.ChangeDate),
                    EffectiveDate = _baseform.TimestampToDateStr(change.EffectiveDate),
                    ChangeReason = change.ChangeReason,
                    Remark = change.Remark,
                    UpdateTime = change.UpdateTime
                };

                result.Add(dto);
            }

            return result;
        }

        public async Task<ServiceDepartmentChangeDTO> GetServiceDepartmentChangeDetailAsync(string uid)
        {
            var serviceChange = await _context.Pas_ServiceDepartmentChange
                .Where(s => s.Uid == uid && !s.IsDeleted)
                .FirstOrDefaultAsync();

            if (serviceChange == null)
                return null;

            return new ServiceDepartmentChangeDTO
            {
                Uid = serviceChange.Uid,
                UserId = serviceChange.UserId,
                ServiceDepartmentId = serviceChange.ServiceDepartmentId,
                ServiceDepartmentName = GetDepartmentName(serviceChange.ServiceDepartmentId),
                ServiceDivisionId = serviceChange.ServiceDivisionId,
                ServiceDivisionName = GetDivisionName(serviceChange.ServiceDivisionId),
                ChangeDate = _baseform.TimestampToDateStr(serviceChange.ChangeDate),
                EffectiveDate = _baseform.TimestampToDateStr(serviceChange.EffectiveDate),
                ChangeReason = serviceChange.ChangeReason,
                Remark = serviceChange.Remark,
                UpdateTime = serviceChange.UpdateTime
            };
        }

        public async Task<string> AddServiceDepartmentChangeAsync(ServiceDepartmentChangeDTO data, string userId)
        {
            var uid = Guid.NewGuid().ToString().Trim();

            var newServiceChange = new ServiceDepartmentChange
            {
                Uid = uid,
                UserId = userId,
                ServiceDepartmentId = data.ServiceDepartmentId ?? "",
                ServiceDivisionId = data.ServiceDivisionId ?? "",
                ChangeDate = _baseform.DateStrToTimestamp(data.ChangeDate ?? ""),
                EffectiveDate = _baseform.DateStrToTimestamp(data.EffectiveDate ?? ""),
                ChangeReason = data.ChangeReason ?? "",
                Remark = data.Remark ?? "",
                CreateTime = _baseform.GetCurrentLocalTimestamp(),
                CreateUserId = _currentUserService.UserId,
            };

            await _context.Pas_ServiceDepartmentChange.AddAsync(newServiceChange);
            await _context.SaveChangesAsync();

            return uid;
        }

        public async Task UpdateServiceDepartmentChangeAsync(ServiceDepartmentChangeDTO data, string uid)
        {
            var existing = await _context.Pas_ServiceDepartmentChange
                .FirstOrDefaultAsync(s => s.Uid == uid && !s.IsDeleted);

            if (existing != null)
            {
                existing.ServiceDepartmentId = data.ServiceDepartmentId ?? "";
                existing.ServiceDivisionId = data.ServiceDivisionId ?? "";
                existing.ChangeDate = _baseform.DateStrToTimestamp(data.ChangeDate ?? "");
                existing.EffectiveDate = _baseform.DateStrToTimestamp(data.EffectiveDate ?? "");
                existing.ChangeReason = data.ChangeReason ?? "";
                existing.Remark = data.Remark ?? "";
                existing.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                existing.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
            }
        }

        public async Task DeleteServiceDepartmentChangeAsync(string uid)
        {
            var existing = await _context.Pas_ServiceDepartmentChange
                .FirstOrDefaultAsync(s => s.Uid == uid && !s.IsDeleted);

            if (existing != null)
            {
                existing.IsDeleted = true;
                existing.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                existing.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
            }
        }

        private string GetDepartmentName(string departmentId)
        {
            if (string.IsNullOrEmpty(departmentId))
                return "";

            var department = _context.Common_Departments
                .FirstOrDefault(d => d.DepartmentId == departmentId && !d.IsDeleted);

            return department?.Name ?? departmentId;
        }

        private string GetDivisionName(string divisionId)
        {
            if (string.IsNullOrEmpty(divisionId))
                return "";

            var division = _context.Common_Divisions
                .FirstOrDefault(d => d.DivisionId == divisionId && !d.IsDeleted);

            return division?.Name ?? divisionId;
        }
    }
}