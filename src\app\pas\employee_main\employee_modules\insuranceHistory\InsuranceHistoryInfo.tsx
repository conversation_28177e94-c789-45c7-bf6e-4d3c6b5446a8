import React, { useEffect, useState } from "react";
import {
    Table,
    Button,
    Modal,
    Form,
    Input,
    DatePicker,
    Card,
    message,
    Row,
    Col,
    Typography,
    Space,
    Select,
    Tabs,
    Tag,
    Tooltip,
    Alert,
    Popconfirm
} from "antd";
import dayjs from "dayjs";

import {
    getInsuranceHistoryDetail,
    getInsuranceHistoryByUserAndType,
    addInsuranceHistory,
    editInsuranceHistory,
    deleteInsuranceHistory,
    getAllEffectiveInsuranceGrades,
    getAllEffectiveInsuranceGradesFromAPI,
    InsuranceHistory,
    createEmptyInsuranceHistory,
} from "@/services/pas/Insurance/InsuranceHistoryService";
import {
    getInsuranceGradeList,
    InsuranceGrade as BaseInsuranceGrade
} from '@/services/pas/Insurance/InsuranceGradeService';
import {
    INSURANCE_TYPES,
    getInsuranceTypeName,
} from "@/services/pas/Insurance/constants/insuranceConstants";
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    SafetyOutlined,
    MedicineBoxOutlined,
    ToolOutlined,
    EditOutlined,
    DeleteOutlined,
    PlusOutlined,
    CalendarOutlined,
    InfoCircleOutlined,
    ExclamationCircleOutlined
} from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

// 保險類型配置
const INSURANCE_TYPE_CONFIG = [
    {
        type: INSURANCE_TYPES.LABOR,
        name: '勞保',
        icon: <SafetyOutlined />,
        color: '#1890ff',
        tabIcon: <SafetyOutlined />
    },
    {
        type: INSURANCE_TYPES.HEALTH,
        name: '健保',
        icon: <MedicineBoxOutlined />,
        color: '#52c41a',
        tabIcon: <MedicineBoxOutlined />
    },
    {
        type: INSURANCE_TYPES.ACCIDENT,
        name: '職災',
        icon: <ToolOutlined />,
        color: '#fa8c16',
        tabIcon: <ToolOutlined />
    }
];

// 檢查記錄是否在指定日期生效
const isRecordActiveOnDate = (record: InsuranceHistory, targetDate = dayjs().startOf('day')) => {
    const start = dayjs(record.startDate).startOf('day');
    const end = record.endDate ? dayjs(record.endDate).startOf('day') : null;

    // 開始日期 <= 目標日期 且 (沒有結束日期 或 結束日期 >= 目標日期)
    return (start.isBefore(targetDate) || start.isSame(targetDate)) &&
        (!end || end.isAfter(targetDate) || end.isSame(targetDate));
};

// 獲取記錄狀態
const getRecordStatus = (record: InsuranceHistory) => {
    const today = dayjs().startOf('day');
    const start = dayjs(record.startDate).startOf('day');
    const end = record.endDate ? dayjs(record.endDate).startOf('day') : null;

    if (start.isAfter(today)) {
        return { status: 'future', label: '未生效', color: 'blue' };
    } else if (isRecordActiveOnDate(record, today)) {
        return { status: 'active', label: '生效中', color: 'green' };
    } else {
        return { status: 'ended', label: '已結束', color: 'default' };
    }
};

// 格式化日期顯示
const formatDateDisplay = (dateStr: string | null, emptyText = '持續中') => {
    return dateStr ? dayjs(dateStr).format('YYYY-MM-DD') : emptyText;
};

type InsuranceHistoryInfoProps = {
    userId: string;
    active: boolean;
    tabUpdateidx?: number;
};

interface ExtendedInsuranceGrade extends BaseInsuranceGrade {
    gradeName: string;
    amount: number;
}

const InsuranceHistoryInfo: React.FC<InsuranceHistoryInfoProps> = ({ userId, active, tabUpdateidx }) => {
    // States
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [activeTab, setActiveTab] = useState(INSURANCE_TYPES.LABOR.toString());

    // Data states
    const [historyData, setHistoryData] = useState<Record<number, InsuranceHistory[]>>({
        [INSURANCE_TYPES.LABOR]: [],
        [INSURANCE_TYPES.HEALTH]: [],
        [INSURANCE_TYPES.ACCIDENT]: []
    });
    const [historyDetail, setHistoryDetail] = useState<InsuranceHistory | null>(null);
    const [insuranceGrades, setInsuranceGrades] = useState<ExtendedInsuranceGrade[]>([]);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);

    // 新增：當前生效保險級距的 state
    const [currentActiveInsurance, setCurrentActiveInsurance] = useState<Record<number, InsuranceHistory | null>>({
        [INSURANCE_TYPES.LABOR]: null,
        [INSURANCE_TYPES.HEALTH]: null,
        [INSURANCE_TYPES.ACCIDENT]: null
    });
    const [activeInsuranceLoading, setActiveInsuranceLoading] = useState(false);

    const [form] = Form.useForm();

    // 新的獲取當前生效保險級距的方法 - 使用後端API
    const fetchCurrentActiveInsurance = async (targetDate?: dayjs.Dayjs) => {
        setActiveInsuranceLoading(true);
        try {
            // 將日期轉換為 YYYY-MM-DD 格式的字串
            const targetDateStr = targetDate ? targetDate.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD');

            // 使用後端的一次性查詢方法，提高效能
            const result = await getAllEffectiveInsuranceGradesFromAPI(userId, targetDateStr);

            if (result.success && result.data) {
                setCurrentActiveInsurance(result.data);
            } else {
                // 如果後端API失敗，使用前端便利方法作為後備
                const backupResult = await getAllEffectiveInsuranceGrades(userId, targetDateStr);

                if (backupResult.success && backupResult.data) {
                    setCurrentActiveInsurance(backupResult.data);
                } else {
                    message.error(backupResult.message || '獲取當前生效保險級距失敗');
                }
            }
        } catch (error: any) {
            message.error('獲取當前生效保險級距失敗');
        } finally {
            setActiveInsuranceLoading(false);
        }
    };

    useEffect(() => {
        if (active) {
            fetchInsuranceGrades();
            fetchAllHistoryData();
            fetchCurrentActiveInsurance(); // 獲取當前生效的保險級距
        }
    }, [active, userId, tabUpdateidx]);

    // 獲取保險級距選項
    const fetchInsuranceGrades = async () => {
        try {
            // 獲取所有類型的保險級距
            const allGrades: ExtendedInsuranceGrade[] = [];
            for (const type of [INSURANCE_TYPES.LABOR, INSURANCE_TYPES.HEALTH, INSURANCE_TYPES.ACCIDENT]) {
                const res = await getInsuranceGradeList(type);
                if (res.success && res.data) {
                    allGrades.push(...res.data.map(grade => ({
                        ...grade,
                        gradeName: `${getInsuranceTypeName(type)} - NT$ ${grade.monthlySalary?.toLocaleString() || 0}`,
                        amount: grade.monthlySalary || 0
                    })));
                }
            }
            setInsuranceGrades(allGrades);
        } catch (error: any) {
            // 錯誤處理
        }
    };

    // 獲取所有保險歷程資料
    const fetchAllHistoryData = async () => {
        setLoading(true);
        setErrorMsg('');
        try {
            const newHistoryData: Record<number, InsuranceHistory[]> = {
                [INSURANCE_TYPES.LABOR]: [],
                [INSURANCE_TYPES.HEALTH]: [],
                [INSURANCE_TYPES.ACCIDENT]: []
            };

            // 並行獲取三種保險類型的歷程資料
            const promises = [
                getInsuranceHistoryByUserAndType(userId, INSURANCE_TYPES.LABOR),
                getInsuranceHistoryByUserAndType(userId, INSURANCE_TYPES.HEALTH),
                getInsuranceHistoryByUserAndType(userId, INSURANCE_TYPES.ACCIDENT)
            ];

            const results = await Promise.all(promises);

            results.forEach((result, index) => {
                const insuranceType = [INSURANCE_TYPES.LABOR, INSURANCE_TYPES.HEALTH, INSURANCE_TYPES.ACCIDENT][index];
                if (result.success && result.data) {
                    newHistoryData[insuranceType] = result.data;
                }
            });

            setHistoryData(newHistoryData);
        } catch (error: any) {
            setErrorMsg(error.message || '載入失敗');
            message.error(error.message || '載入失敗');
        } finally {
            setLoading(false);
        }
    };

    // 檢查日期區間是否重疊 - 修正邏輯確保日期不重複
    const validateDateRange = (
        startDate: dayjs.Dayjs,
        endDate: dayjs.Dayjs | null,
        insuranceType: number,
        excludeUid?: string
    ): boolean => {
        const currentList = historyData[insuranceType] || [];
        const filteredList = excludeUid
            ? currentList.filter(item => item.uid !== excludeUid)
            : currentList;

        for (const item of filteredList) {
            const itemStart = dayjs(item.startDate);
            const itemEnd = item.endDate ? dayjs(item.endDate) : null;

            // 檢查是否有重疊或相鄰（不允許相同日期）
            if (endDate) {
                // 新記錄有結束日期
                if (itemEnd) {
                    // 舊記錄也有結束日期 - 檢查區間是否重疊（包含相同日期）
                    if ((startDate.isBefore(itemEnd) || startDate.isSame(itemEnd)) &&
                        (endDate.isAfter(itemStart) || endDate.isSame(itemStart))) {
                        return false; // 有重疊或相同日期
                    }
                } else {
                    // 舊記錄沒有結束日期（持續中） - 新記錄結束日期不能晚於或等於舊記錄開始日期
                    if (endDate.isAfter(itemStart) || endDate.isSame(itemStart)) {
                        return false; // 有重疊
                    }
                }
            } else {
                // 新記錄沒有結束日期（持續中）
                if (itemEnd) {
                    // 舊記錄有結束日期 - 新記錄開始日期必須晚於舊記錄結束日期
                    if (startDate.isBefore(itemEnd) || startDate.isSame(itemEnd)) {
                        return false; // 有重疊或相同日期
                    }
                } else {
                    // 兩個都沒有結束日期 - 不能有兩個持續中的記錄
                    return false;
                }
            }
        }
        return true;
    };

    const handleAddNew = () => {
        setHistoryDetail(null);
        setIsModalOpen(true);
        // 在 Modal 打開後設置表單值
        setTimeout(() => {
            form.resetFields();
            form.setFieldsValue({
                ...createEmptyInsuranceHistory(),
                insuranceType: parseInt(activeTab)
            });
        }, 0);
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        try {
            const { success, data, message: msg } = await getInsuranceHistoryDetail(uid);
            if (success && data) {
                setHistoryDetail(data);
                // 切換到對應的 tab
                setActiveTab(data.insuranceType.toString());
                setIsModalOpen(true);
                // 在 Modal 打開後設置表單值
                setTimeout(() => {
                    form.resetFields();
                    form.setFieldsValue({
                        ...data,
                        startDate: data.startDate ? dayjs(data.startDate) : null,
                        endDate: data.endDate ? dayjs(data.endDate) : null,
                    });
                }, 0);
            } else {
                message.error(msg || '載入資料失敗');
            }
        } catch (error: any) {
            message.error(error.message || '載入資料時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();

            // 使用當前 tab 的保險類型，而非表單中的值
            const insuranceType = parseInt(activeTab);

            // 日期區間驗證
            const startDate = values.startDate;
            const endDate = values.endDate;

            if (endDate && endDate.isBefore(startDate)) {
                message.error('結束日期不能早於開始日期');
                return;
            }

            const isValidRange = validateDateRange(
                startDate,
                endDate,
                insuranceType,
                historyDetail?.uid
            );

            if (!isValidRange) {
                message.error('日期區間與現有記錄重疊或相鄰，請調整日期（結束日期的隔天才能作為下一筆記錄的開始日期）');
                return;
            }

            const payload: Partial<InsuranceHistory> = {
                ...values,
                userId: userId,
                insuranceType: insuranceType, // 使用當前 tab 的保險類型
                startDate: startDate.format("YYYY-MM-DD"),
                endDate: endDate ? endDate.format("YYYY-MM-DD") : null,
            };

            setModalLoading(true);
            let res;
            if (historyDetail) {
                res = await editInsuranceHistory({ ...payload, uid: historyDetail.uid });
            } else {
                res = await addInsuranceHistory(payload);
            }

            if (res.success && res.data?.result) {
                message.success(historyDetail ? "更新成功" : "新增成功");
                setIsModalOpen(false);
                fetchAllHistoryData();
                fetchCurrentActiveInsurance(); // 重新獲取當前生效保險級距
            } else {
                message.error(res.data?.msg || "操作失敗");
            }
        } catch (err) {
            if (!(err as any)?.errorFields) {
                message.error("儲存發生錯誤");
            }
        } finally {
            setModalLoading(false);
        }
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteInsuranceHistory(uid);
            if (res.success && res.data?.result) {
                message.success("刪除成功");
                fetchAllHistoryData();
                fetchCurrentActiveInsurance(); // 重新獲取當前生效保險級距
            } else {
                message.error(res.data?.msg || "刪除失敗");
            }
        } catch (error: any) {
            message.error(error.message || "刪除發生錯誤");
        }
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    // 獲取保險類型配置
    const getInsuranceTypeConfig = (type: number) => {
        return INSURANCE_TYPE_CONFIG.find(config => config.type === type) || {
            type,
            name: '未知',
            icon: <InfoCircleOutlined />,
            color: '#999',
            tabIcon: <InfoCircleOutlined />
        };
    };

    // 表格欄位定義
    const columns = [
        {
            title: '級距名稱',
            dataIndex: 'insuranceGradeUid',
            render: (gradeUid: string) => {
                const grade = insuranceGrades.find(g => g.uid === gradeUid);
                const config = getInsuranceTypeConfig(parseInt(activeTab));
                return (
                    <Space>
                        <span style={{ color: config.color }}>{config.icon}</span>
                        <Text strong>{grade?.gradeName || '未知級距'}</Text>
                    </Space>
                );
            }
        },
        {
            title: '生效日期',
            dataIndex: 'startDate',
            render: (text: string) => (
                <Space>
                    <CalendarOutlined style={{ color: '#52c41a' }} />
                    <Text>{formatDateDisplay(text, '-')}</Text>
                </Space>
            )
        },
        {
            title: '結束日期',
            dataIndex: 'endDate',
            render: (text: string | null) => (
                <Space>
                    <CalendarOutlined style={{ color: '#ff4d4f' }} />
                    <Text>{formatDateDisplay(text, '持續中')}</Text>
                </Space>
            )
        },
        {
            title: '狀態',
            render: (record: InsuranceHistory) => {
                const status = getRecordStatus(record);
                return <Tag color={status.color}>{status.label}</Tag>;
            }
        },
        {
            title: '操作',
            render: (record: InsuranceHistory) => (
                <Space onClick={(e) => e.stopPropagation()}>
                    <Button
                        type="link"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleRowClick(record.uid)}
                    >
                        編輯
                    </Button>
                    <Popconfirm
                        title={
                            <div>
                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                <Text>確定要刪除此筆保險歷程資料嗎？</Text>
                            </div>
                        }
                        onConfirm={() => setDeleteUid(record.uid)}
                        okText="確認"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                    >
                        <Button
                            type="link"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                        >
                            刪除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    if (!active) return null;
    if (errorMsg) return <div style={{ color: 'red', textAlign: 'center', padding: 20 }}>錯誤：{errorMsg}</div>;

    return (
        <>
            {/* 當前生效保險級距概覽 */}
            <Card
                title={
                    <Space>
                        <InfoCircleOutlined />
                        <Title level={4} style={{ margin: 0 }}>當前生效保險級距</Title>
                    </Space>
                }
                loading={loading || activeInsuranceLoading}
                className="shadow-sm"
                style={{ marginBottom: 16, borderRadius: '8px' }}
            >
                <Row gutter={[16, 16]}>
                    {INSURANCE_TYPE_CONFIG.map((config) => {
                        const activeRecord = currentActiveInsurance[config.type];
                        const grade = activeRecord ? insuranceGrades.find(g => g.uid === activeRecord.insuranceGradeUid) : null;

                        return (
                            <Col xs={24} sm={12} lg={8} key={config.type}>
                                <Card
                                    size="small"
                                    style={{
                                        borderLeft: `4px solid ${config.color}`,
                                        backgroundColor: activeRecord ? '#f6ffed' : '#f5f5f5'
                                    }}
                                >
                                    <Space direction="vertical" style={{ width: '100%' }}>
                                        <Space>
                                            <span style={{ color: config.color }}>{config.icon}</span>
                                            <Text strong style={{ color: config.color }}>{config.name}</Text>
                                        </Space>

                                        {activeRecord ? (
                                            <>
                                                <div>
                                                    <Text type="secondary">級距：</Text>
                                                    <Text strong>
                                                        NT$ {grade?.monthlySalary?.toLocaleString() || '未知'}
                                                    </Text>
                                                </div>
                                                <div>
                                                    <Text type="secondary">生效日期：</Text>
                                                    <Text>{formatDateDisplay(activeRecord.startDate, '-')}</Text>
                                                </div>
                                                <div>
                                                    <Text type="secondary">結束日期：</Text>
                                                    <Text style={{ color: activeRecord.endDate ? '#fa8c16' : '#52c41a' }}>
                                                        {formatDateDisplay(activeRecord.endDate || null, '持續生效中')}
                                                    </Text>
                                                </div>
                                                <Tag color="green">
                                                    <CalendarOutlined style={{ marginRight: 4 }} />
                                                    生效中
                                                </Tag>
                                            </>
                                        ) : (
                                            <>
                                                <Text type="secondary">目前無生效記錄</Text>
                                                <Tag color="default">
                                                    未投保
                                                </Tag>
                                            </>
                                        )}
                                    </Space>
                                </Card>
                            </Col>
                        );
                    })}
                </Row>
            </Card>

            <Card
                title={
                    <Space>
                        <SafetyOutlined />
                        <Title level={4} style={{ margin: 0 }}>保險級距歷程</Title>
                    </Space>
                }
                loading={loading}
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <Alert
                    message="注意事項"
                    description="每種保險類型的生效期間不可重疊，同時只能有一個生效中的級距。若要新增重疊期間的記錄，請先結束現有記錄。"
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                />

                <Tabs
                    activeKey={activeTab}
                    onChange={setActiveTab}
                    tabBarExtraContent={
                        <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={handleAddNew}
                            style={{ borderRadius: '6px' }}
                        >
                            新增{getInsuranceTypeName(parseInt(activeTab))}記錄
                        </Button>
                    }
                    items={INSURANCE_TYPE_CONFIG.map((config) => ({
                        key: config.type.toString(),
                        label: (
                            <Space>
                                {config.tabIcon}
                                {config.name}
                            </Space>
                        ),
                        children: (
                            <Table
                                rowKey="uid"
                                dataSource={historyData[config.type]}
                                columns={columns}
                                pagination={{ pageSize: 10 }}
                                size="middle"
                                onRow={(record) => ({
                                    onClick: () => handleRowClick(record.uid),
                                })}
                                rowClassName={(record) =>
                                    record.uid === deleteUid ? 'row-deleting-pulse' : ''
                                }
                            />
                        )
                    }))}
                />
            </Card>

            {/* 編輯/新增 Modal */}
            <Modal
                title={
                    <Space>
                        {(() => {
                            const config = getInsuranceTypeConfig(parseInt(activeTab));
                            return <span style={{ color: config.color }}>{config.icon}</span>;
                        })()}
                        <Text>{historyDetail ? '編輯' : '新增'}{getInsuranceTypeName(parseInt(activeTab))}記錄</Text>
                    </Space>
                }
                open={isModalOpen}
                onOk={handleSubmit}
                onCancel={handleCancel}
                confirmLoading={modalLoading}
                width={600}
                okText="儲存"
                cancelText="取消"
            >
                <Alert
                    message="注意事項"
                    description={`當前正在編輯 ${getInsuranceTypeName(parseInt(activeTab))} 記錄。請確保生效期間與現有記錄不重疊，結束日期的隔天才能作為下一筆記錄的開始日期。`}
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                />
                <Form
                    form={form}
                    layout="vertical"
                    className="form-section"
                >
                    <Row gutter={16}>
                        <Col span={24}>
                            <Form.Item
                                label="保險級距"
                                name="insuranceGradeUid"
                                rules={[{ required: true, message: '請選擇保險級距' }]}
                            >
                                <Select
                                    placeholder="請選擇保險級距"
                                    options={insuranceGrades
                                        .filter(grade => grade.insuranceType === parseInt(activeTab))
                                        .map(grade => ({
                                            value: grade.uid,
                                            label: `${grade.gradeName}`
                                        }))
                                    }
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label="生效日期"
                                name="startDate"
                                rules={[{ required: true, message: '請選擇生效日期' }]}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    format="YYYY-MM-DD"
                                    placeholder="請選擇生效日期"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="結束日期"
                                name="endDate"
                                extra="留空表示持續生效中"
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    format="YYYY-MM-DD"
                                    placeholder="請選擇結束日期（可選）"
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>

            {/* 刪除確認 Modal */}
            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </>
    );
};

export default InsuranceHistoryInfo; 