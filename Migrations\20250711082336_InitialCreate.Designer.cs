﻿// <auto-generated />
using System;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace FAST_ERP_Backend.Migrations
{
    [DbContext(typeof(ERPDbContext))]
    [Migration("20250711082336_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("AssetAmortizationSourceMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AmortizationSourceId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("攤提來源編號");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產流水號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("Id");

                    b.HasIndex("AmortizationSourceId");

                    b.HasIndex("AssetId");

                    b.ToTable("Pms_AssetAmortizationSourceMapping", "dbo");
                });

            modelBuilder.Entity("AssetAssetSourceMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產流水號");

                    b.Property<Guid>("AssetSourceId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產來源編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("AssetSourceId");

                    b.ToTable("Pms_AssetAssetSourceMapping", "dbo");
                });

            modelBuilder.Entity("AssetInsuranceUnitMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產流水號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<Guid>("InsuranceUnitId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("承保單位編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("InsuranceUnitId");

                    b.ToTable("Pms_AssetInsuranceUnitMapping", "dbo");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.AuditLogs", b =>
                {
                    b.Property<string>("AuditLogsId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("日誌編號");

                    b.Property<string>("Agent")
                        .HasColumnType("nvarchar(50)")
                        .HasComment("操作資訊");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<string>("IP")
                        .HasColumnType("nvarchar(50)")
                        .HasComment("操作者IP");

                    b.Property<string>("LogContent")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("操作內容");

                    b.Property<string>("RequestUrl")
                        .HasColumnType("nvarchar(50)")
                        .HasComment("連線路徑");

                    b.HasKey("AuditLogsId");

                    b.ToTable("Common_AuditLogs");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.City", b =>
                {
                    b.Property<string>("CityId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("縣市編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("說明描述");

                    b.Property<string>("EnglishName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("英文名稱");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("縣市名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("CityId");

                    b.ToTable("Common_Cities");

                    b.HasData(
                        new
                        {
                            CityId = "2ccecf5d-4f8e-4cd1-8fd3-8238b8968250",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Taipei City",
                            IsDeleted = false,
                            Name = "台北市",
                            SortCode = 100
                        },
                        new
                        {
                            CityId = "2f441941-4343-4695-beb5-d72a04798a6f",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Keelung City",
                            IsDeleted = false,
                            Name = "基隆市",
                            SortCode = 200
                        },
                        new
                        {
                            CityId = "322ebadc-902c-4b53-aa55-5ba620ce14c7",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Lienchiang County",
                            IsDeleted = false,
                            Name = "連江縣",
                            SortCode = 209
                        },
                        new
                        {
                            CityId = "333aad34-e809-4e00-8fa1-ffa32ccdbdf2",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Yilan County",
                            IsDeleted = false,
                            Name = "宜蘭縣",
                            SortCode = 260
                        },
                        new
                        {
                            CityId = "4b02d17e-3619-4bf8-8946-5598f5a27dd5",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Diaoyutai",
                            IsDeleted = false,
                            Name = "釣魚台",
                            SortCode = 290
                        },
                        new
                        {
                            CityId = "4e3d0307-51cd-421a-8690-fbe10543a824",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Hsinchu City",
                            IsDeleted = false,
                            Name = "新竹市",
                            SortCode = 300
                        },
                        new
                        {
                            CityId = "531ddd16-2f17-4afb-8fe7-507c0120f7ea",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Hsinchu County",
                            IsDeleted = false,
                            Name = "新竹縣",
                            SortCode = 302
                        },
                        new
                        {
                            CityId = "6b25ee4a-dddb-4bb5-a173-8a3b1930f21d",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Taoyuan County",
                            IsDeleted = false,
                            Name = "桃園縣",
                            SortCode = 320
                        },
                        new
                        {
                            CityId = "7d65a2ca-9bed-4446-97e2-26a8b0e19b85",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Miaoli County",
                            IsDeleted = false,
                            Name = "苗栗縣",
                            SortCode = 350
                        },
                        new
                        {
                            CityId = "7e5dedeb-279b-4bec-8440-fe1918a23845",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Taichung City",
                            IsDeleted = false,
                            Name = "台中市",
                            SortCode = 400
                        },
                        new
                        {
                            CityId = "800ad98a-c75f-400e-9068-7f8903b585a5",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Changhua County",
                            IsDeleted = false,
                            Name = "彰化縣",
                            SortCode = 500
                        },
                        new
                        {
                            CityId = "8052bb63-1c98-4699-a760-ed77c8d75f01",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Nantou County",
                            IsDeleted = false,
                            Name = "南投縣",
                            SortCode = 540
                        },
                        new
                        {
                            CityId = "82427f35-9c7a-40ad-aa15-361435cb68af",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Chiayi City",
                            IsDeleted = false,
                            Name = "嘉義市",
                            SortCode = 600
                        },
                        new
                        {
                            CityId = "8cae2fd4-4089-40aa-9e74-d73deadab984",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Chiayi County",
                            IsDeleted = false,
                            Name = "嘉義縣",
                            SortCode = 602
                        },
                        new
                        {
                            CityId = "8f17e952-9ace-4835-b0e7-ec1f70c5bd5c",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Yunlin County",
                            IsDeleted = false,
                            Name = "雲林縣",
                            SortCode = 630
                        },
                        new
                        {
                            CityId = "9159bec5-b912-465e-a98e-72b6e5396ea2",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Tainan City",
                            IsDeleted = false,
                            Name = "台南市",
                            SortCode = 700
                        },
                        new
                        {
                            CityId = "9bde99df-d8f3-43ee-8595-4203ae7f3aa3",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Kaohsiung City",
                            IsDeleted = false,
                            Name = "高雄市",
                            SortCode = 800
                        },
                        new
                        {
                            CityId = "b8550f03-8d8f-40dd-b0ca-40104dd955c2",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Nanhai Islands",
                            IsDeleted = false,
                            Name = "南海島",
                            SortCode = 817
                        },
                        new
                        {
                            CityId = "bad085a9-72ae-4aab-99b6-40e396faa25c",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Penghu County",
                            IsDeleted = false,
                            Name = "澎湖縣",
                            SortCode = 880
                        },
                        new
                        {
                            CityId = "bfa05fb5-005e-47bf-8126-d3a58e6fee64",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Kinmen County",
                            IsDeleted = false,
                            Name = "金門縣",
                            SortCode = 890
                        },
                        new
                        {
                            CityId = "c7af2fb6-999f-45ee-a52a-2676072de25c",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Pingtung County",
                            IsDeleted = false,
                            Name = "屏東縣",
                            SortCode = 900
                        },
                        new
                        {
                            CityId = "c7eb7fa7-896c-4d52-b378-2f410db9a319",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Taitung County",
                            IsDeleted = false,
                            Name = "台東縣",
                            SortCode = 950
                        },
                        new
                        {
                            CityId = "d2a37ceb-07d3-482c-a7d0-854104fb6bac",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "",
                            EnglishName = "Hualien County",
                            IsDeleted = false,
                            Name = "花蓮縣",
                            SortCode = 970
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.Department", b =>
                {
                    b.Property<string>("DepartmentId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("部門編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("EnterpriseGroupId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("公司編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("部門名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("DepartmentId");

                    b.ToTable("Common_Departments");

                    b.HasData(
                        new
                        {
                            DepartmentId = "244fe635-16d2-490e-9a08-c0ae2a4f260e",
                            CreateTime = 1742454109L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "信用部",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "bf4dadda-59b1-41c6-8eaa-bac176124017",
                            CreateTime = 1742454097L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "供銷部",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "c404fc72-c566-43ed-8080-dcf943b4a0b2",
                            CreateTime = 1742454103L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "推廣部",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "e1ac337b-d154-4e23-b603-cc51af2b995a",
                            CreateTime = 1742454114L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "五甲辦事處",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "e2ea86da-2172-493d-89cc-35f6a6bc6731",
                            CreateTime = 1742454091L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "文山辦事處",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "f2a8e7c3-9b4d-4a6f-b1d2-3c5e7f8a9b0c",
                            CreateTime = 1742454091L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "東區辦事處",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "3e1c6d5f-2b4a-47f8-91ae-c7d8f0b1e2a3",
                            CreateTime = 1742454091L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "老爺辦事處",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "a7d9c4f1-5e8b-4c3d-a2f0-9b6c1d7e4f5a",
                            CreateTime = 1742454091L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "茂林辦事處",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "d4f7a9b2-1c3e-4d6f-8a0b-9e2c5f1a7d3b",
                            CreateTime = 1742454091L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "鎮北辦事處",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "f7a3b9c2-d6e5-4f1a-8c7b-0e9d1f2a3b4c",
                            CreateTime = 1742454091L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "市場辦事處",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                            CreateTime = 1742454091L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "保險部",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            DepartmentId = "e0f1d2c3-b4a5-6789-9876-543210fedcba",
                            CreateTime = 1742454091L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            EnterpriseGroupId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            IsDeleted = false,
                            Name = "過埤辦事處",
                            SortCode = 0,
                            UpdateUserId = ""
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.District", b =>
                {
                    b.Property<string>("DistrictId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("鄉鎮市區編號");

                    b.Property<string>("CityId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("縣市編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("鄉鎮市區名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("ZipCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(5)")
                        .HasComment("郵遞區號");

                    b.HasKey("DistrictId");

                    b.ToTable("Common_Districts");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.Division", b =>
                {
                    b.Property<string>("DivisionId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("組別編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("DepartmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("部門編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("組別名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("DivisionId");

                    b.ToTable("Common_Divisions");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.EnterpriseGroups", b =>
                {
                    b.Property<string>("EnterpriseGroupsId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("公司編號");

                    b.Property<string>("AccountingPeriod")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("結算週期");

                    b.Property<string>("Address1")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("地址1");

                    b.Property<string>("Address2")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("地址2");

                    b.Property<string>("CompanyPhone")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("公司電話");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("電子郵件");

                    b.Property<string>("EnglishAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("英文地址");

                    b.Property<string>("EnglishName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("公司英文名稱");

                    b.Property<long>("EstablishDate")
                        .HasColumnType("bigint")
                        .HasComment("成立日期");

                    b.Property<string>("Fax")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("傳真");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("MobilePhone")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("手機號碼");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("公司名稱");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("電話");

                    b.Property<string>("Representative")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("公司負責人");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<string>("UnifiedNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("統一編號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("Website")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("網頁");

                    b.HasKey("EnterpriseGroupsId");

                    b.ToTable("Common_EnterpriseGroups");

                    b.HasData(
                        new
                        {
                            EnterpriseGroupsId = "8b4d81a9-7f22-49c3-bbda-3886cf28a80d",
                            AccountingPeriod = "12",
                            Address1 = "830高雄市鳳山區維新路124號",
                            Address2 = "",
                            CompanyPhone = "*********",
                            CreateTime = 1742454076L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Email = "<EMAIL>",
                            EnglishAddress = "",
                            EnglishName = "",
                            EstablishDate = 1742454019L,
                            Fax = "",
                            IsDeleted = false,
                            MobilePhone = "",
                            Name = "鳳山區農會",
                            Phone = "",
                            Representative = "潘建仲",
                            SortCode = 0,
                            UnifiedNumber = "********",
                            UpdateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Website = ""
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.EnterpriseImage", b =>
                {
                    b.Property<string>("ImageId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("圖片編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("ImageName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("圖片名稱");

                    b.Property<string>("ImagePath")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("圖片路徑");

                    b.Property<string>("ImageType")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("圖片類型");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("ImageId");

                    b.ToTable("Common_EnterpriseImage");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.FileList", b =>
                {
                    b.Property<string>("FileId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("檔案列表編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("檔案說明");

                    b.Property<string>("FileListId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("檔案來源編號");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("檔案名稱");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("檔案路徑");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("檔案類型");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasComment("排序編號");

                    b.Property<string>("SourceTable")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("來源資料表");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("FileId");

                    b.ToTable("Common_FileList");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.FileUpload", b =>
                {
                    b.Property<string>("FileUploadId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("檔案編號");

                    b.Property<string>("AccessLevel")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("存取權限");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("檔案類型");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("檔案描述");

                    b.Property<int>("DownloadCount")
                        .HasColumnType("int")
                        .HasComment("下載次數");

                    b.Property<string>("EnterpriseGroupsId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("企業群組編號");

                    b.Property<string>("FileCategory")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("檔案分類");

                    b.Property<string>("FileExtension")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("檔案副檔名");

                    b.Property<string>("FileHash")
                        .IsRequired()
                        .HasColumnType("nvarchar(64)")
                        .HasComment("檔案雜湊值");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("檔案路徑");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasComment("檔案大小");

                    b.Property<string>("FileStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("檔案狀態");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("bit")
                        .HasComment("是否為主要檔案");

                    b.Property<long?>("LastAccessTime")
                        .HasColumnType("bigint")
                        .HasComment("最後存取時間");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasComment("原始檔案名稱");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasComment("排序順序");

                    b.Property<string>("SourceModule")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("來源模組");

                    b.Property<string>("SourceRecordId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("來源記錄編號");

                    b.Property<string>("SourceTable")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("來源資料表");

                    b.Property<string>("StoredFileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasComment("儲存檔案名稱");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("FileUploadId");

                    b.HasIndex("EnterpriseGroupsId");

                    b.ToTable("Common_FileUploads");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.Position", b =>
                {
                    b.Property<string>("PositionId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("職稱編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("職稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序編碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("PositionId");

                    b.ToTable("Common_Positions");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.Roles", b =>
                {
                    b.Property<string>("RolesId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("角色編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("角色名稱");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("RolesId");

                    b.ToTable("Common_Roles");

                    b.HasData(
                        new
                        {
                            RolesId = "1558c61a-e196-4811-bc59-3020dadcf91e",
                            CreateTime = 1741852778L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "G3"
                        },
                        new
                        {
                            RolesId = "4fc92853-5cce-48d2-83d5-09ea3bf88087",
                            CreateTime = 1741852767L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "G1"
                        },
                        new
                        {
                            RolesId = "c7cc94e7-51f3-4769-9afc-b4bf0c3b9433",
                            CreateTime = 1741852773L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "G2"
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.RolesPermissions", b =>
                {
                    b.Property<string>("RolesPermissionsId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("權限編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("RolesId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("角色編號");

                    b.Property<string>("SystemMenuId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("系統選單編號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("RolesPermissionsId");

                    b.HasIndex("RolesId");

                    b.HasIndex("SystemMenuId");

                    b.ToTable("Common_RolesPermissions");

                    b.HasData(
                        new
                        {
                            RolesPermissionsId = "04e9189f-c00d-40d6-9176-235ffa63ca41",
                            CreateTime = 1741853872L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            RolesId = "4fc92853-5cce-48d2-83d5-09ea3bf88087",
                            SystemMenuId = "072661fa-1740-46f1-b91e-0aa11f371abc"
                        },
                        new
                        {
                            RolesPermissionsId = "0c45247a-ae2c-4462-96bf-d10e5eb281b3",
                            CreateTime = 1741852891L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            RolesId = "4fc92853-5cce-48d2-83d5-09ea3bf88087",
                            SystemMenuId = "6443a2b6-40a1-41b2-98a4-029d47d4720e"
                        },
                        new
                        {
                            RolesPermissionsId = "1dec7214-3b90-4318-8784-e4bd41d1330e",
                            CreateTime = 1741914881L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            RolesId = "4fc92853-5cce-48d2-83d5-09ea3bf88087",
                            SystemMenuId = "2b03d682-68a5-4dae-be43-d83063cbb798"
                        },
                        new
                        {
                            RolesPermissionsId = "4329d73c-ab18-41e3-b764-5ec1b99446cb",
                            CreateTime = 1741914901L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            RolesId = "4fc92853-5cce-48d2-83d5-09ea3bf88087",
                            SystemMenuId = "b8c09d28-abf6-438b-a1ac-0b4bae0352a1"
                        },
                        new
                        {
                            RolesPermissionsId = "6f6432c7-af19-4156-87db-bc94694b0c29",
                            CreateTime = 1741914955L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            RolesId = "4fc92853-5cce-48d2-83d5-09ea3bf88087",
                            SystemMenuId = "bbb6bd69-d440-4199-ba90-becae506cab3"
                        },
                        new
                        {
                            RolesPermissionsId = "79b32fdb-0c77-42ac-99f7-85443a03ae64",
                            CreateTime = 1741914567L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            RolesId = "4fc92853-5cce-48d2-83d5-09ea3bf88087",
                            SystemMenuId = "d07b835d-aae9-4a41-93c1-88396ff9e713"
                        },
                        new
                        {
                            RolesPermissionsId = "eba8fb41-b67a-4918-b894-872d348b22d2",
                            CreateTime = 1741914891L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            RolesId = "4fc92853-5cce-48d2-83d5-09ea3bf88087",
                            SystemMenuId = "682f1334-cd3b-4259-a807-7e3a06811245"
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.SystemGroups", b =>
                {
                    b.Property<string>("SystemGroupId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("系統群組編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("系統名稱");

                    b.Property<string>("Option")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("系統設定");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("系統備註");

                    b.Property<string>("SystemCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("系統代碼(如Common、Pms等)");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("SystemGroupId");

                    b.ToTable("Common_SystemGroups");

                    b.HasData(
                        new
                        {
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "共用系統",
                            Option = "V0sXS0bkPeTWOeXY7st/SIGVxKPIbzznqd6f3OGjp+CXEM+u2QBtM6P+liq0d8eFFgx8G7guYOl8RMgj2G0jXHtY5GoCF31ny3kzAE5FtLkl+Mz6Cy+jEVbN1MN29F1p",
                            Remark = "系統預設資料到2099/12/31 23:59:59",
                            SystemCode = "Common"
                        },
                        new
                        {
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "財產系統",
                            Option = "VPwAdIR9iSg2X9irBAXA0sQ4ZL051q8Y0W6zrttzAOrWH71BcNeujK7M17boL6bPyVlSvGSaGMA1FAL3o0nj87b0qu4B1deRzfHRSl6pwtU=",
                            Remark = "系統預設資料到1970/01/01 00:00:00",
                            SystemCode = "Pms"
                        },
                        new
                        {
                            SystemGroupId = "F1732C06-A239-4211-ABFD-E3C319DF071B",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "人事系統",
                            Option = "VPwAdIR9iSg2X9irBAXA0sQ4ZL051q8Y0W6zrttzAOrWH71BcNeujK7M17boL6bPyVlSvGSaGMA1FAL3o0nj87b0qu4B1deRzfHRSl6pwtU=",
                            Remark = "系統預設資料到1970/01/01 00:00:00",
                            SystemCode = "Pas"
                        },
                        new
                        {
                            SystemGroupId = "43c04c10-69cf-48dc-8c08-89def33e480c",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "進銷存系統",
                            Option = "VPwAdIR9iSg2X9irBAXA0sQ4ZL051q8Y0W6zrttzAOrWH71BcNeujK7M17boL6bPyVlSvGSaGMA1FAL3o0nj87b0qu4B1deRzfHRSl6pwtU=",
                            Remark = "系統預設資料到1970/01/01 00:00:00",
                            SystemCode = "Ims"
                        },
                        new
                        {
                            SystemGroupId = "09734e61-a7ad-40d6-a0e0-5ef9fc212b25",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "其他系統",
                            Option = "VPwAdIR9iSg2X9irBAXA0sQ4ZL051q8Y0W6zrttzAOrWH71BcNeujK7M17boL6bPyVlSvGSaGMA1FAL3o0nj87b0qu4B1deRzfHRSl6pwtU=",
                            Remark = "測試用的",
                            SystemCode = "Other"
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.SystemMenu", b =>
                {
                    b.Property<string>("SystemMenuId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("選單編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Icon")
                        .HasColumnType("nvarchar(50)")
                        .HasComment("選單圖示");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<bool>("IsMenu")
                        .HasColumnType("bit")
                        .HasComment("為選單");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("選單鍵");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("選單標籤");

                    b.Property<string>("ParentId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("父層選單編號");

                    b.Property<string>("SystemGroupId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("系統群組編號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("SystemMenuId");

                    b.ToTable("Common_SystemMenu");

                    b.HasData(
                        new
                        {
                            SystemMenuId = "6443a2b6-40a1-41b2-98a4-029d47d4720e",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "SettingOutlined",
                            IsDeleted = false,
                            IsMenu = true,
                            Key = "system",
                            Label = "系統設定",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "d07b835d-aae9-4a41-93c1-88396ff9e713",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = true,
                            Key = "info",
                            Label = "資訊管理",
                            ParentId = "6443a2b6-40a1-41b2-98a4-029d47d4720e",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "2b03d682-68a5-4dae-be43-d83063cbb798",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = true,
                            Key = "companyInfo",
                            Label = "公司資訊",
                            ParentId = "d07b835d-aae9-4a41-93c1-88396ff9e713",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "b8c09d28-abf6-438b-a1ac-0b4bae0352a1",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "enterpriseGroups",
                            Label = "公司基本資訊",
                            ParentId = "2b03d682-68a5-4dae-be43-d83063cbb798",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "682f1334-cd3b-4259-a807-7e3a06811245",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "reportEmail",
                            Label = "經營報告書",
                            ParentId = "2b03d682-68a5-4dae-be43-d83063cbb798",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "c85e62d2-5eeb-4966-b240-cf84354d9504",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "enterpriseImage",
                            Label = "公司商標&印章",
                            ParentId = "2b03d682-68a5-4dae-be43-d83063cbb798",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "b9d7b94e-89af-4f25-9fbe-7b802df1aaf3",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = true,
                            Key = "dataManagement",
                            Label = "資料管理",
                            ParentId = "d07b835d-aae9-4a41-93c1-88396ff9e713",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "f1b9432f-627b-43e7-a937-8e5da1216a02",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "databaseBackup",
                            Label = "資料庫備份",
                            ParentId = "b9d7b94e-89af-4f25-9fbe-7b802df1aaf3",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "683fc782-54a1-4832-b6a2-204446d4cb83",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "datainit",
                            Label = "資料初始化",
                            ParentId = "b9d7b94e-89af-4f25-9fbe-7b802df1aaf3",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "f537f85c-1798-4afd-8374-b9e57639cc7a",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "apiKey",
                            Label = "簽發API認證金鑰",
                            ParentId = "b9d7b94e-89af-4f25-9fbe-7b802df1aaf3",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "6f5fef7e-5824-4678-99c3-17a69cd3c629",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "users",
                            Label = "用户管理",
                            ParentId = "6443a2b6-40a1-41b2-98a4-029d47d4720e",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "24bb839d-fce2-4291-8331-01f8c3cbe39d",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "other",
                            Label = "其他管理",
                            ParentId = "6443a2b6-40a1-41b2-98a4-029d47d4720e",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "739cef61-283b-4b6e-81ad-c3676d8b89fd",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "unit",
                            Label = "單位管理",
                            ParentId = "24bb839d-fce2-4291-8331-01f8c3cbe39d",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "072661fa-1740-46f1-b91e-0aa11f371abc",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "security",
                            Label = "權限管理",
                            ParentId = "6443a2b6-40a1-41b2-98a4-029d47d4720e",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        },
                        new
                        {
                            SystemMenuId = "a44ee353-cdfd-4466-807a-496a8e0748c2",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "BankOutlined",
                            IsDeleted = false,
                            IsMenu = true,
                            Key = "pms_asset",
                            Label = "財產管理系統",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "0cbac110-60b7-497d-93ae-27229ddd5158",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_source",
                            Label = "財產來源",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "0ceb6fed-7704-41bf-a0be-02e094ace908",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_location_change_form",
                            Label = "財產位置變動單",
                            ParentId = "12743d6f-9736-4f1e-82b3-4b450c9f074b",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "12743d6f-9736-4f1e-82b3-4b450c9f074b",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_document_maintenance",
                            Label = "財產管理",
                            ParentId = "a44ee353-cdfd-4466-807a-496a8e0748c2",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "13211f5b-94b4-48a8-a049-f7299e393971",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_fixed_asset_scrap_form",
                            Label = "固定資產報廢單",
                            ParentId = "12743d6f-9736-4f1e-82b3-4b450c9f074b",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "14b79fbd-3e2a-4507-9293-8f44362606bd",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_scrap",
                            Label = "財產報廢清冊",
                            ParentId = "b40b286a-f47c-4bc9-a3b2-9be04a24afc4",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "19d3fd8a-a412-4a3f-9b03-c3f498e8e543",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_inventory_record",
                            Label = "財產盤點紀錄表",
                            ParentId = "b40b286a-f47c-4bc9-a3b2-9be04a24afc4",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "27bfec4e-748b-43df-a0ce-07613dccec9e",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_system_parameter_setting",
                            Label = "系統參數",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "2e6eb4d2-2f75-4f66-a9c0-3c080eb4c20c",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_account",
                            Label = "財產科目",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "2f8e9b3a-c7d5-4e6f-a8b2-1d9c0e5f3a7b",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_sub_account",
                            Label = "財產子目",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "31e4aea3-feb7-48bd-8616-e1e45d158652",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_change_improvement",
                            Label = "資產異動改良單",
                            ParentId = "12743d6f-9736-4f1e-82b3-4b450c9f074b",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "c8b9f51d-41a1-4e4e-8672-91a4dc9e3c5d",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_batch_import",
                            Label = "財產整批轉檔",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "3d82acc5-51ab-4994-b329-5a5f51b3ffbd",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_fixed_asset_sale_form",
                            Label = "固定資產出售單",
                            ParentId = "12743d6f-9736-4f1e-82b3-4b450c9f074b",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "70e3e8ce-9031-4c27-93f4-cca02a68399b",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_carryout_form",
                            Label = "資產攜出單",
                            ParentId = "12743d6f-9736-4f1e-82b3-4b450c9f074b",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "719ce213-a84f-4563-b26d-7d14c7481a9d",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_vendor_maintenance_form",
                            Label = "廠商修護單",
                            ParentId = "12743d6f-9736-4f1e-82b3-4b450c9f074b",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "953c7b41-ac61-437a-8243-9a8fefb9da78",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_category",
                            Label = "財產類別",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "9f604141-9db6-4892-a4bd-bf18b67b142f",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_label",
                            Label = "財產標籤",
                            ParentId = "b40b286a-f47c-4bc9-a3b2-9be04a24afc4",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "a4b40480-1058-4e2b-a169-468951156e51",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_inventory_list",
                            Label = "財產盤點清冊",
                            ParentId = "b40b286a-f47c-4bc9-a3b2-9be04a24afc4",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_parameter_settings",
                            Label = "基本資料管理",
                            ParentId = "a44ee353-cdfd-4466-807a-496a8e0748c2",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "ad33284b-4e7c-4898-a955-0001a83c160b",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_increase_decrease",
                            Label = "財產增減表",
                            ParentId = "b40b286a-f47c-4bc9-a3b2-9be04a24afc4",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "ae454e3e-6605-4ce5-b68a-d7a576def0bd",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_fixed_asset_maintenance_form",
                            Label = "固定資產維護單",
                            ParentId = "12743d6f-9736-4f1e-82b3-4b450c9f074b",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "b40b286a-f47c-4bc9-a3b2-9be04a24afc4",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_report",
                            Label = "報表列印",
                            ParentId = "a44ee353-cdfd-4466-807a-496a8e0748c2",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "b7d828d7-ee73-4a55-af7c-80fb2099ea69",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_storage_location",
                            Label = "存放位置",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "b9e5f2a3-d1c7-4b8e-9f6a-2d4c8e7b3a5d",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_manufacturer",
                            Label = "廠牌型號",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "c0f6a3b4-d5e7-4c8e-9f0a-2d3c4e5f6a7b",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_user_role",
                            Label = "保管人&使用人",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "bd752f16-c6a6-48aa-836e-49cc32d090e0",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_fixed_asset_depreciation_form",
                            Label = "固定資產折舊單",
                            ParentId = "12743d6f-9736-4f1e-82b3-4b450c9f074b",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "c429f7cc-a732-4fba-81f0-a6683dd5bb11",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_inventory",
                            Label = "財產盤點明細表",
                            ParentId = "b40b286a-f47c-4bc9-a3b2-9be04a24afc4",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "c4ec0a7e-694d-4e83-9af6-c028dad6fae0",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_depreciation",
                            Label = "財產折舊表",
                            ParentId = "b40b286a-f47c-4bc9-a3b2-9be04a24afc4",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "c7eaaa00-505b-48a7-9435-7c0c99757a75",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_accessory_equipment",
                            Label = "附屬設備",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "cb38dffd-0960-4d62-8eef-e90cf91130a1",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_card",
                            Label = "財產卡",
                            ParentId = "b40b286a-f47c-4bc9-a3b2-9be04a24afc4",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "ec103869-1245-4eb9-b865-0042d3564069",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_amortization_source",
                            Label = "攤提來源",
                            ParentId = "ac2b11a7-3a25-4b19-a61b-1c149fed3919",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "f5391e4e-c8f4-4d46-84dd-caeef53b30eb",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms_asset_list",
                            Label = "財產清冊",
                            ParentId = "b40b286a-f47c-4bc9-a3b2-9be04a24afc4",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "f93d2b8e-8e4f-4df3-9c1e-61b9e2f8cb9a",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "AliwangwangFilled",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pms",
                            Label = "功能總覽",
                            ParentId = "a44ee353-cdfd-4466-807a-496a8e0748c2",
                            SystemGroupId = "55c48a9f-5451-4bdc-8139-67ac55105ac0"
                        },
                        new
                        {
                            SystemMenuId = "76075bff-f54f-4f32-83d8-0c5a91367779",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "TeamOutlined",
                            IsDeleted = false,
                            IsMenu = true,
                            Key = "hr",
                            Label = "人事薪資系統",
                            SystemGroupId = "09734e61-a7ad-40d6-a0e0-5ef9fc212b25"
                        },
                        new
                        {
                            SystemMenuId = "26434602-d9a0-4abb-bfc4-59da0d54243e",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "BugFilled",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pas_employee_main",
                            Label = "員工資料主頁",
                            ParentId = "76075bff-f54f-4f32-83d8-0c5a91367779",
                            SystemGroupId = "F1732C06-A239-4211-ABFD-E3C319DF071B"
                        },
                        new
                        {
                            SystemMenuId = "26434602-d9a0-4abb-bfc4-59da0d542433",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "AliwangwangFilled",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "pas_overview",
                            Label = "功能總覽",
                            ParentId = "76075bff-f54f-4f32-83d8-0c5a91367779",
                            SystemGroupId = "F1732C06-A239-4211-ABFD-E3C319DF071B"
                        },
                        new
                        {
                            SystemMenuId = "3dbcbc5b-ab15-40e4-90b6-54e0420083a8",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "ClockCircleOutlined",
                            IsDeleted = false,
                            IsMenu = true,
                            Key = "building",
                            Label = "大樓管理系統",
                            SystemGroupId = "09734e61-a7ad-40d6-a0e0-5ef9fc212b25"
                        },
                        new
                        {
                            SystemMenuId = "bbb6bd69-d440-4199-ba90-becae506cab3",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "ShopOutlined",
                            IsDeleted = false,
                            IsMenu = true,
                            Key = "ims",
                            Label = "進銷存管理系統",
                            SystemGroupId = "43c04c10-69cf-48dc-8c08-89def33e480c"
                        },
                        new
                        {
                            SystemMenuId = "c09e6c21-c490-4d4d-b08d-f95605117b72",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "FolderOpenOutlined",
                            IsDeleted = false,
                            IsMenu = true,
                            Key = "ims_basic",
                            Label = "基本資料",
                            ParentId = "bbb6bd69-d440-4199-ba90-becae506cab3",
                            SystemGroupId = "43c04c10-69cf-48dc-8c08-89def33e480c"
                        },
                        new
                        {
                            SystemMenuId = "1fc16a81-af0f-44cb-b4cf-e61842e8d761",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "TagsOutlined",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "ims_basic_item",
                            Label = "庫存品管理",
                            ParentId = "c09e6c21-c490-4d4d-b08d-f95605117b72",
                            SystemGroupId = "43c04c10-69cf-48dc-8c08-89def33e480c"
                        },
                        new
                        {
                            SystemMenuId = "3c4d5dc4-aafe-4634-badb-39bed49f46ce",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Icon = "TeamOutlined",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "ims_basic_partner",
                            Label = "商業夥伴管理",
                            ParentId = "c09e6c21-c490-4d4d-b08d-f95605117b72",
                            SystemGroupId = "43c04c10-69cf-48dc-8c08-89def33e480c"
                        },
                        new
                        {
                            SystemMenuId = "6f4f1511-0ca4-4eb6-b53f-2640637018ef",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            IsMenu = false,
                            Key = "systemMenu",
                            Label = "選單管理",
                            ParentId = "6443a2b6-40a1-41b2-98a4-029d47d4720e",
                            SystemGroupId = "5f933c38-f759-4427-8fcd-713213fdf5ab"
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.SystemParameters", b =>
                {
                    b.Property<string>("SystemParametersId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("參數編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("參數說明");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("ParameterCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("參數代號");

                    b.Property<string>("SystemGroupId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("系統群組編號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("SystemParametersId");

                    b.ToTable("Common_SystemParameters");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.SystemParametersItem", b =>
                {
                    b.Property<string>("SystemParametersItemId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("參數項目編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("參數項目名稱");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("integer")
                        .HasComment("排序編號");

                    b.Property<string>("SystemParametersId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("參數編號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("參數項目值");

                    b.HasKey("SystemParametersItemId");

                    b.ToTable("Common_SystemParametersItem");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.Unit", b =>
                {
                    b.Property<Guid>("UnitId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("單位流水號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("單位名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<string>("UnitNo")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasComment("單位編號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("UnitId");

                    b.ToTable("Common_Units");

                    b.HasData(
                        new
                        {
                            UnitId = new Guid("a1b2c3d4-e5f6-4a5b-8c9d-1e2f3a4b5c6d"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "個",
                            SortCode = 0,
                            UnitNo = "PCS"
                        },
                        new
                        {
                            UnitId = new Guid("b2c3d4e5-f6a7-5b6c-9d0e-2f3a4b5c6d7e"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "組",
                            SortCode = 1,
                            UnitNo = "SET"
                        },
                        new
                        {
                            UnitId = new Guid("c3d4e5f6-a7b8-6c7d-0e1f-3a4b5c6d7e8f"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "箱",
                            SortCode = 2,
                            UnitNo = "BOX"
                        },
                        new
                        {
                            UnitId = new Guid("d4e5f6a7-b8c9-7d8e-1f2a-4b5c6d7e8f9a"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "公斤",
                            SortCode = 3,
                            UnitNo = "KG"
                        },
                        new
                        {
                            UnitId = new Guid("e5f6a7b8-c9d0-8e9f-2a3b-5c6d7e8f9a0b"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "公尺",
                            SortCode = 4,
                            UnitNo = "M"
                        },
                        new
                        {
                            UnitId = new Guid("f6a7b8c9-d0e1-9f0a-3b4c-6d7e8f9a0b1c"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "包",
                            SortCode = 5,
                            UnitNo = "PACK"
                        },
                        new
                        {
                            UnitId = new Guid("a7b8c9d0-e1f2-0a1b-4c5d-7e8f9a0b1c2d"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "捲",
                            SortCode = 6,
                            UnitNo = "ROLL"
                        },
                        new
                        {
                            UnitId = new Guid("b8c9d0e1-f2a3-1b2c-5d6e-8f9a0b1c2d3e"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "瓶",
                            SortCode = 7,
                            UnitNo = "BOTTLE"
                        },
                        new
                        {
                            UnitId = new Guid("c9d0e1f2-a3b4-2c3d-6e7f-9a0b1c2d3e4f"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "雙",
                            SortCode = 8,
                            UnitNo = "PAIR"
                        },
                        new
                        {
                            UnitId = new Guid("d0e1f2a3-b4c5-3d4e-7f8a-0b1c2d3e4f5a"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "張",
                            SortCode = 9,
                            UnitNo = "SHEET"
                        },
                        new
                        {
                            UnitId = new Guid("b1e4c7a9-3d2f-46a0-8f9b-7c6d1e2a3f4b"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "坪",
                            SortCode = 10,
                            UnitNo = "METER"
                        },
                        new
                        {
                            UnitId = new Guid("e9f0d1c2-b3a4-5678-9012-34567890abcd"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "台",
                            SortCode = 1,
                            UnitNo = "TAI"
                        },
                        new
                        {
                            UnitId = new Guid("a7b8c9d0-e1f2-3456-7890-fedcba987654"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "輛",
                            SortCode = 2,
                            UnitNo = "LIANG"
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.Users", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.Property<string>("Account")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("帳號");

                    b.Property<string>("AltPhone")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("備用電話");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("EMail")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("電子信箱");

                    b.Property<string>("EnterpriseGroupId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("公司群組編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("MailingAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("通訊地址");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("使用者名稱");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("密碼");

                    b.Property<string>("PermanentAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("戶籍地址");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("手機");

                    b.Property<string>("PositionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("職務編號");

                    b.Property<string>("RolesId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("角色編號");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序編碼");

                    b.Property<string>("TelNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(15)")
                        .HasComment("電話");

                    b.Property<long?>("UnlockTime")
                        .HasColumnType("bigint")
                        .HasComment("解鎖時間");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("UserId");

                    b.ToTable("Common_Users");

                    b.HasData(
                        new
                        {
                            UserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Account = "FastAdmin",
                            AltPhone = "",
                            EMail = "",
                            EnterpriseGroupId = "",
                            IsDeleted = false,
                            MailingAddress = "",
                            Name = "南農中心管理者",
                            Password = "+SWMLywijCZyA8svqFs/NA==",
                            PermanentAddress = "",
                            Phone = "",
                            PositionId = "",
                            RolesId = "4fc92853-5cce-48d2-83d5-09ea3bf88087",
                            SortCode = 0,
                            TelNo = ""
                        },
                        new
                        {
                            UserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d66",
                            Account = "PmsAdmin",
                            AltPhone = "",
                            EMail = "",
                            EnterpriseGroupId = "",
                            IsDeleted = false,
                            MailingAddress = "",
                            Name = "財產系統主辦",
                            Password = "+SWMLywijCZyA8svqFs/NA==",
                            PermanentAddress = "",
                            Phone = "",
                            PositionId = "",
                            RolesId = "4fc92853-5cce-48d2-83d5-09ea3bf88087",
                            SortCode = 0,
                            TelNo = ""
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.Contact", b =>
                {
                    b.Property<string>("ContactID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("聯絡人編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("電子郵件");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("bit")
                        .HasComment("是否為主要聯絡人");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("聯絡人姓名");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("電話");

                    b.Property<string>("Position")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("職位");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("ContactID");

                    b.ToTable("Ims_Contact");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.ContactRole", b =>
                {
                    b.Property<string>("ContactRoleID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("角色編號");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("角色名稱");

                    b.HasKey("ContactRoleID");

                    b.ToTable("Ims_ContactRole");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.CustomerCategory", b =>
                {
                    b.Property<string>("CustomerCategoryID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("客戶分類編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("描述");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("名稱");

                    b.Property<string>("ParentID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("父分類ID");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("CustomerCategoryID");

                    b.HasIndex("ParentID");

                    b.ToTable("Ims_CustomerCategory");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.CustomerDetail", b =>
                {
                    b.Property<string>("PartnerID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("商業夥伴編號");

                    b.Property<string>("CustomerCategoryID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("客戶分類編號");

                    b.Property<string>("CustomerCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("客戶代碼");

                    b.Property<int?>("SettlementDay")
                        .HasColumnType("int")
                        .HasComment("應收結帳日");

                    b.HasKey("PartnerID");

                    b.HasIndex("CustomerCategoryID");

                    b.ToTable("Ims_CustomerDetail");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.EnterpriseDetail", b =>
                {
                    b.Property<string>("PartnerID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("商業夥伴編號");

                    b.Property<string>("BussinessID")
                        .HasColumnType("nvarchar(20)")
                        .HasComment("統一編號");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("公司名稱");

                    b.Property<string>("ResponsiblePerson")
                        .HasColumnType("nvarchar(20)")
                        .HasComment("負責人");

                    b.Property<string>("TaxID")
                        .HasColumnType("nvarchar(20)")
                        .HasComment("稅籍編號");

                    b.HasKey("PartnerID");

                    b.ToTable("Ims_EnterpriseDetail");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.IndividualDetail", b =>
                {
                    b.Property<string>("PartnerID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("商業夥伴編號");

                    b.Property<DateTime?>("BirthDate")
                        .HasColumnType("datetime2")
                        .HasComment("出生日期");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(50)")
                        .HasComment("名字");

                    b.Property<string>("IdentificationNumber")
                        .HasColumnType("nvarchar(20)")
                        .HasComment("身分證字號");

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(50)")
                        .HasComment("姓氏");

                    b.HasKey("PartnerID");

                    b.ToTable("Ims_IndividualDetail");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.Item", b =>
                {
                    b.Property<string>("ItemID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("庫存品編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<string>("CustomNO")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("自定義編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("描述");

                    b.Property<string>("InternationalBarCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("國際條碼");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<bool>("IsStop")
                        .HasColumnType("bit")
                        .HasComment("停售");

                    b.Property<string>("ItemCategoryID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("庫存品分類編號");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序");

                    b.Property<int>("TaxType")
                        .HasColumnType("int")
                        .HasComment("庫存品稅別");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("單位");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("ItemID");

                    b.HasIndex("ItemCategoryID");

                    b.ToTable("Ims_Item");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.ItemCategory", b =>
                {
                    b.Property<string>("ItemCategoryID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("庫存品分類編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("描述");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("名稱");

                    b.Property<string>("ParentID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("父分類ID");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("ItemCategoryID");

                    b.HasIndex("ParentID");

                    b.ToTable("Ims_ItemCategory");

                    b.HasData(
                        new
                        {
                            ItemCategoryID = "b9937eff-4952-4ebf-af90-982fd25b2803",
                            CreateTime = 1750140789L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "",
                            IsDeleted = false,
                            Name = "大",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ItemCategoryID = "ad7c4192-6c7d-4f24-b238-02faed56a507",
                            CreateTime = 1750140800L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "",
                            IsDeleted = false,
                            Name = "中",
                            ParentID = "b9937eff-4952-4ebf-af90-982fd25b2803",
                            SortCode = 0,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ItemCategoryID = "ae790d4b-ef98-43ba-b0b6-a46f115e1f32",
                            CreateTime = 1750140805L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "",
                            IsDeleted = false,
                            Name = "小",
                            ParentID = "ad7c4192-6c7d-4f24-b238-02faed56a507",
                            SortCode = 0,
                            UpdateUserId = ""
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.ItemPrice", b =>
                {
                    b.Property<string>("ItemPriceID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("售價編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("ItemID")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("庫存品編號");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(38, 8)")
                        .HasComment("售價");

                    b.Property<string>("PriceTypeID")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("價格類別");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("ItemPriceID");

                    b.HasIndex("ItemID");

                    b.HasIndex("PriceTypeID");

                    b.ToTable("Ims_ItemPrice");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.Partner", b =>
                {
                    b.Property<string>("PartnerID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("商業夥伴編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<bool>("IsStop")
                        .HasColumnType("bit")
                        .HasComment("停用");

                    b.Property<string>("SupplierCategoryID")
                        .HasColumnType("nvarchar(100)");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("PartnerID");

                    b.HasIndex("SupplierCategoryID");

                    b.ToTable("Ims_Partner");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.PartnerAddress", b =>
                {
                    b.Property<string>("PartnerAddressID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("地址編號");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasComment("詳細地址");

                    b.Property<string>("City")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("縣市");

                    b.Property<string>("Country")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("國家地區");

                    b.Property<string>("District")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("鄉鎮市區");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("bit")
                        .HasComment("是否為主要地址");

                    b.Property<string>("PartnerID")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("商業夥伴編號");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("郵遞區號");

                    b.HasKey("PartnerAddressID");

                    b.HasIndex("PartnerID");

                    b.ToTable("Ims_PartnerAddress");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.PartnerContact", b =>
                {
                    b.Property<string>("PartnerID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("商業夥伴編號");

                    b.Property<string>("ContactID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("聯絡人編號");

                    b.Property<string>("ContactRoleID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("聯絡人角色編號");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("bit")
                        .HasComment("是否為主要聯絡人 (針對此夥伴)");

                    b.HasKey("PartnerID", "ContactID", "ContactRoleID");

                    b.HasIndex("ContactID");

                    b.HasIndex("ContactRoleID");

                    b.ToTable("Ims_PartnerContact");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.PriceType", b =>
                {
                    b.Property<string>("PriceTypeID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("價格類別編號");

                    b.Property<bool>("AllowStop")
                        .HasColumnType("bit")
                        .HasComment("允許停用");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("價格類別描述");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<bool>("IsStop")
                        .HasColumnType("bit")
                        .HasComment("停用");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("價格類別名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("PriceTypeID");

                    b.ToTable("Ims_PriceType");

                    b.HasData(
                        new
                        {
                            PriceTypeID = "c14a69b6-f866-4461-8f63-147296e2abe4",
                            AllowStop = false,
                            CreateTime = 1749814668L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "系統預設",
                            IsDeleted = false,
                            IsStop = false,
                            Name = "一般",
                            SortCode = 0
                        },
                        new
                        {
                            PriceTypeID = "972a6670-af33-4e38-8e4b-62e0da5d539b",
                            AllowStop = false,
                            CreateTime = 1749814696L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "系統預設",
                            IsDeleted = false,
                            IsStop = false,
                            Name = "會員",
                            SortCode = 1
                        },
                        new
                        {
                            PriceTypeID = "ee48d0f6-a227-4489-b822-58303d4aee9d",
                            AllowStop = false,
                            CreateTime = 1750058255L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "系統預設",
                            IsDeleted = false,
                            IsStop = false,
                            Name = "大批",
                            SortCode = 2
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.SupplierCategory", b =>
                {
                    b.Property<string>("SupplierCategoryID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("供應商分類編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("描述");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("名稱");

                    b.Property<string>("ParentID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("父分類ID");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("SupplierCategoryID");

                    b.HasIndex("ParentID");

                    b.ToTable("Ims_SupplierCategory");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.SupplierDetail", b =>
                {
                    b.Property<string>("PartnerID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("商業夥伴編號");

                    b.Property<int?>("SettlementDay")
                        .HasColumnType("int")
                        .HasComment("應付結帳日");

                    b.Property<string>("SupplierCategoryID")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("供應商分類編號");

                    b.Property<string>("SupplierCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("供應商代碼");

                    b.HasKey("PartnerID");

                    b.HasIndex("SupplierCategoryID");

                    b.ToTable("Ims_SupplierDetail");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Certification", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<long?>("certificateDate")
                        .HasColumnType("bigint")
                        .HasComment("發證日期");

                    b.Property<string>("certificateInstitution")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("檢覈機關");

                    b.Property<string>("certificateName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("檢覈名稱");

                    b.Property<string>("certificateNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("證書文號");

                    b.Property<string>("certificateYearMonth")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("檢覈年月");

                    b.Property<string>("remark")
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("userId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_Certification");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Dependent", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<long?>("dependentBirthday")
                        .HasColumnType("bigint")
                        .HasComment("被扶養者生日");

                    b.Property<string>("dependentName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("被扶養者姓名");

                    b.Property<string>("dependentRelationType")
                        .IsRequired()
                        .HasColumnType("nvarchar(3)")
                        .HasComment("被扶養者關係類型");

                    b.Property<string>("dependentRocId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("被扶養者身分證字號");

                    b.Property<string>("remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("userId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("扶養者員工");

                    b.HasKey("uid");

                    b.ToTable("Pas_Dependent");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Education", b =>
                {
                    b.Property<string>("Uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CertificateDate")
                        .HasColumnType("bigint")
                        .HasComment("發證日期");

                    b.Property<string>("CertificateNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("證件字號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<string>("DegreeType")
                        .IsRequired()
                        .HasColumnType("nvarchar(3)")
                        .HasComment("學位代號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("DepartmentName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("院系科別");

                    b.Property<string>("GraduateType")
                        .IsRequired()
                        .HasColumnType("nvarchar(3)")
                        .HasComment("結業代號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("PeriodDateEnd")
                        .HasColumnType("bigint")
                        .HasComment("修業迄日");

                    b.Property<long?>("PeriodDateStart")
                        .HasColumnType("bigint")
                        .HasComment("修業起日");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("SchoolName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("學校名稱");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("Uid");

                    b.ToTable("Pas_Education");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Employee", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.Property<long?>("Birthday")
                        .HasColumnType("bigint")
                        .HasComment("出生日期");

                    b.Property<string>("BloodType")
                        .IsRequired()
                        .HasColumnType("nvarchar(3)")
                        .HasComment("血型類別");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("EduLevel")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)")
                        .HasComment("最高學歷");

                    b.Property<string>("EmpNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("員工編號");

                    b.Property<long?>("HealthInsStartDate")
                        .HasColumnType("bigint")
                        .HasComment("健保加保日");

                    b.Property<long?>("HireDate")
                        .HasColumnType("bigint")
                        .HasComment("到職日");

                    b.Property<string>("IdNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("身分證字號");

                    b.Property<string>("IdType")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)")
                        .HasComment("證號別");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("LaborInsStartDate")
                        .HasColumnType("bigint")
                        .HasComment("勞保加保日");

                    b.Property<long?>("LeaveDate")
                        .HasColumnType("bigint")
                        .HasComment("離職日");

                    b.Property<long?>("OfficialHireDate")
                        .HasColumnType("bigint")
                        .HasComment("正式任用日");

                    b.Property<long?>("ProbStartDate")
                        .HasColumnType("bigint")
                        .HasComment("試用日");

                    b.Property<string>("SpouseIdNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("配偶身分證");

                    b.Property<string>("SpouseName")
                        .IsRequired()
                        .HasColumnType("nvarchar(12)")
                        .HasComment("配偶姓名");

                    b.Property<long?>("TransferDate")
                        .HasColumnType("bigint")
                        .HasComment("轉任日");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("errorMark")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)")
                        .HasComment("錯誤註記");

                    b.Property<string>("remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.HasKey("UserId");

                    b.ToTable("Pas_Employee");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.EmployeeRegularSalary", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<decimal>("amount")
                        .HasColumnType("decimal(8,0)")
                        .HasComment("設定金額");

                    b.Property<bool>("isEnable")
                        .HasColumnType("bit")
                        .HasComment("個別項目啟用");

                    b.Property<string>("remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("salaryItemUid")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("常態薪資資料編號");

                    b.Property<string>("userId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("員工userid");

                    b.HasKey("uid");

                    b.ToTable("Pas_EmployeeRegularSalary");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Ensure", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("ensureNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("保證書編號");

                    b.Property<string>("guarantorAddress")
                        .HasColumnType("nvarchar(250)")
                        .HasComment("保證人地址");

                    b.Property<long?>("guarantorBirthday")
                        .HasColumnType("bigint")
                        .HasComment("保證人生日");

                    b.Property<string>("guarantorName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("保證人姓名");

                    b.Property<string>("guarantorPersonalId")
                        .HasColumnType("nvarchar(20)")
                        .HasComment("保證人身分證");

                    b.Property<string>("guarantorPhone")
                        .HasColumnType("nvarchar(50)")
                        .HasComment("保證人電話");

                    b.Property<string>("guarantorProperty")
                        .HasColumnType("nvarchar(250)")
                        .HasComment("保證人財產");

                    b.Property<string>("propertyValue")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("財產價值");

                    b.Property<string>("relationship")
                        .HasColumnType("nvarchar(50)")
                        .HasComment("關係");

                    b.Property<string>("remark")
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("userId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_Ensure");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Examination", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("admittanceGrade")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("錄取等第");

                    b.Property<long?>("certificateDate")
                        .HasColumnType("bigint")
                        .HasComment("發證日期");

                    b.Property<string>("certificateNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("證書文號");

                    b.Property<long?>("examEndDate")
                        .HasColumnType("bigint")
                        .HasComment("考試迄日");

                    b.Property<string>("examInstitution")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("考試機關");

                    b.Property<string>("examName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("考試名稱");

                    b.Property<long?>("examStartDate")
                        .HasColumnType("bigint")
                        .HasComment("考試起日");

                    b.Property<string>("examType")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("種類科別");

                    b.Property<string>("remark")
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("userId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_Examination");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.ExpenseDepartmentChange", b =>
                {
                    b.Property<string>("Uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("ChangeDate")
                        .HasColumnType("bigint")
                        .HasComment("異動日期");

                    b.Property<string>("ChangeReason")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("異動原因");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<long?>("EffectiveDate")
                        .HasColumnType("bigint")
                        .HasComment("生效日期");

                    b.Property<string>("ExpenseDepartmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("開支部門編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("Uid");

                    b.ToTable("Pas_ExpenseDepartmentChange");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Hensure", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<long?>("HealthInsEndDate")
                        .HasColumnType("bigint")
                        .HasComment("健保投保迄日");

                    b.Property<long?>("HealthInsStartDate")
                        .HasColumnType("bigint")
                        .HasComment("健保投保起日");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("dependentName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("眷屬姓名");

                    b.Property<string>("dependentRelationType")
                        .IsRequired()
                        .HasColumnType("nvarchar(3)")
                        .HasComment("依附關係類型");

                    b.Property<string>("dependentRocId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("眷屬身分證字號");

                    b.Property<string>("remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("userId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("依附使用者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_Hensure");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.InsuranceGrade", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<long?>("EndDate")
                        .HasColumnType("bigint")
                        .HasComment("結束日期");

                    b.Property<int>("InsuranceType")
                        .HasColumnType("int")
                        .HasComment("保險類型 (1=勞保, 2=健保, 3=職災)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<int>("MonthlySalary")
                        .HasColumnType("int")
                        .HasComment("月投保薪資");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(500)")
                        .HasComment("備註");

                    b.Property<long?>("StartDate")
                        .HasColumnType("bigint")
                        .HasComment("生效日期");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_InsuranceGrade");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.InsuranceHistory", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<long?>("EndDate")
                        .HasColumnType("bigint")
                        .HasComment("結束日期 (timestamp，可為 null)");

                    b.Property<string>("InsuranceGradeUid")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("投保級距編號 (對應 InsuranceGrade.uid)");

                    b.Property<int>("InsuranceType")
                        .HasColumnType("int")
                        .HasComment("保險類型 (1=勞保, 2=健保, 3=職災)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long>("StartDate")
                        .HasColumnType("bigint")
                        .HasComment("生效日期 (timestamp)");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_InsuranceHistory", t =>
                        {
                            t.HasComment("員工保險級距歷程記錄");
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.PerformancePointGroup", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("群組UID");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("groupName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("群組名稱");

                    b.Property<decimal>("weightRatio")
                        .HasColumnType("decimal(5, 2)")
                        .HasComment("加權比例 (群組層級)");

                    b.HasKey("uid");

                    b.ToTable("Pas_PerformancePointGroup");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.PerformancePointRecord", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<decimal>("point")
                        .HasColumnType("decimal(10, 2)")
                        .HasComment("點數");

                    b.Property<long?>("pointDate")
                        .HasColumnType("bigint")
                        .HasComment("點數日期");

                    b.Property<string>("pointUid")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("點數類型UID");

                    b.Property<string>("remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("userId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_PerformancePointRecord");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.PerformancePointType", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("點數類型UID");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("groupUid")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("所屬群組UID");

                    b.Property<string>("pointName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("點數名稱");

                    b.Property<decimal>("weightRatio")
                        .HasColumnType("decimal(5, 2)")
                        .HasComment("加權比例 (點數項目層級)");

                    b.HasKey("uid");

                    b.ToTable("Pas_PerformancePointType");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Promotion", b =>
                {
                    b.Property<string>("Uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<string>("CategoryType")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("錄用類別");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<long?>("EffectiveDate")
                        .HasColumnType("bigint")
                        .HasComment("生效日期");

                    b.Property<string>("ExpenseDepartmentChangeUid")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("開支部門異動DATAUID");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("JobLevel")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("職等");

                    b.Property<string>("JobRank")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("級數");

                    b.Property<string>("JobTitle")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("職稱代號");

                    b.Property<string>("JobroleType")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("任用資格");

                    b.Property<long?>("PromotionDate")
                        .HasColumnType("bigint")
                        .HasComment("升遷日期");

                    b.Property<string>("PromotionReason")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("升遷原因");

                    b.Property<string>("PromotionType")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("升遷類型");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<decimal?>("SalaryAmount")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("薪俸");

                    b.Property<string>("SalaryType")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("薪俸類型");

                    b.Property<string>("ServiceDepartmentChangeUid")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("服務部門異動DATAUID");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("Uid");

                    b.ToTable("Pas_Promotion");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.RegularSalaryItem", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("description")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("項目描述");

                    b.Property<bool>("isEnable")
                        .HasColumnType("bit")
                        .HasComment("項目啟用");

                    b.Property<bool>("isTaxable")
                        .HasColumnType("bit")
                        .HasComment("扣稅類型");

                    b.Property<string>("itemName")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("項目名稱");

                    b.Property<string>("itemType")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)")
                        .HasComment("加減項類型");

                    b.HasKey("uid");

                    b.ToTable("Pas_RegularSalaryItem");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Salary", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<decimal?>("EmployeeContributionAmount")
                        .HasColumnType("decimal(12, 2)")
                        .HasComment("員工自提金額");

                    b.Property<decimal?>("EmployeeContributionRate")
                        .HasColumnType("decimal(5, 2)")
                        .HasComment("員工自提比例");

                    b.Property<string>("EmployeeContributionType")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)")
                        .HasComment("員工自提類型 (0:不提撥、1:提撥比率、2:固定自提金額)");

                    b.Property<decimal?>("EmployerContributionRate")
                        .HasColumnType("decimal(5, 2)")
                        .HasComment("雇主提撥比率");

                    b.Property<decimal?>("FixedTaxAmount")
                        .HasColumnType("decimal(12, 2)")
                        .HasComment("固定稅額");

                    b.Property<decimal?>("FixedTaxRate")
                        .HasColumnType("decimal(5, 2)")
                        .HasComment("固定稅率");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("SalaryStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)")
                        .HasComment("發薪狀態 (0:停薪 1:正常)");

                    b.Property<string>("TaxType")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)")
                        .HasComment("計稅型式 (1:自動計算、2:固定稅率、3:固定稅額)");

                    b.Property<string>("TransferAccount")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("轉帳帳號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("UserId");

                    b.ToTable("Pas_Salary");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.SalaryPoint", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<string>("AdjustmentReason")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("調整原因");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("薪點金額（每點對應金額）");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<long>("EffectiveDate")
                        .HasColumnType("bigint")
                        .HasComment("生效日期（timestamp）");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("PointLevel")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("薪點名稱");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_SalaryPoint", t =>
                        {
                            t.HasComment("薪點金額紀錄");
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.ServiceDepartmentChange", b =>
                {
                    b.Property<string>("Uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("ChangeDate")
                        .HasColumnType("bigint")
                        .HasComment("異動日期");

                    b.Property<string>("ChangeReason")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("異動原因");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<long?>("EffectiveDate")
                        .HasColumnType("bigint")
                        .HasComment("生效日期");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("ServiceDepartmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("服務部門編號");

                    b.Property<string>("ServiceDivisionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("服務組別編號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("Uid");

                    b.ToTable("Pas_ServiceDepartmentChange");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Suspend", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<long?>("approveDate")
                        .HasColumnType("bigint")
                        .HasComment("核准日期");

                    b.Property<string>("approveNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("核准文號");

                    b.Property<string>("remark")
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<long?>("suspendDate")
                        .HasColumnType("bigint")
                        .HasComment("留停日期");

                    b.Property<string>("suspendKind")
                        .IsRequired()
                        .HasColumnType("nvarchar(3)")
                        .HasComment("留停種類");

                    b.Property<string>("suspendReason")
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("留停原因");

                    b.Property<string>("suspendType")
                        .IsRequired()
                        .HasColumnType("nvarchar(3)")
                        .HasComment("留停類型");

                    b.Property<string>("userId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_Suspend");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Train", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<long?>("certificateDate")
                        .HasColumnType("bigint")
                        .HasComment("發證日期");

                    b.Property<string>("certificateNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("證書文號");

                    b.Property<int>("cost")
                        .HasColumnType("int")
                        .HasComment("費用");

                    b.Property<long?>("courseEndDate")
                        .HasColumnType("bigint")
                        .HasComment("課程迄日");

                    b.Property<string>("courseName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("課程名稱");

                    b.Property<long?>("courseStartDate")
                        .HasColumnType("bigint")
                        .HasComment("課程起日");

                    b.Property<string>("durationHours")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasComment("時數");

                    b.Property<string>("instructor")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("講師名稱");

                    b.Property<string>("ranking")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("名次");

                    b.Property<string>("remark")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("score")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("成績");

                    b.Property<string>("trainingInstitute")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("訓練機構名稱");

                    b.Property<string>("userId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_Train");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pas.Undergo", b =>
                {
                    b.Property<string>("uid")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("資料編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("agencyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("服務機關名稱");

                    b.Property<long?>("certificateDate")
                        .HasColumnType("bigint")
                        .HasComment("發證日期");

                    b.Property<string>("certificateNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("證書文號");

                    b.Property<string>("departmentName")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("服務部門名稱");

                    b.Property<string>("duty")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("職務");

                    b.Property<string>("hireDate")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("到職年月");

                    b.Property<string>("jobGrade")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("薪級");

                    b.Property<string>("jobTitle")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("職稱");

                    b.Property<string>("remark")
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<string>("supervisorName")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("主管姓名");

                    b.Property<string>("terminationDate")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("卸職年月");

                    b.Property<string>("userId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("uid");

                    b.ToTable("Pas_Undergo");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AccessoryEquipment", b =>
                {
                    b.Property<Guid>("AccessoryEquipmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("附屬設備編號");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("EquipmentName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("設備名稱");

                    b.Property<string>("EquipmentNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("設備編號");

                    b.Property<string>("EquipmentType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("設備類型");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<long?>("PurchaseDate")
                        .HasColumnType("bigint")
                        .HasComment("購入日期");

                    b.Property<decimal>("PurchasePrice")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("購入價格");

                    b.Property<string>("Remarks")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("備註");

                    b.Property<string>("Specification")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("規格/型號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("UsageStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用狀態");

                    b.HasKey("AccessoryEquipmentId");

                    b.HasIndex("AssetId");

                    b.ToTable("Pms_AccessoryEquipments");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AmortizationSource", b =>
                {
                    b.Property<Guid>("AmortizationSourceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("攤提來源編號");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("攤提來源金額");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("DepartmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("部門編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("攤提來源描述");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("SourceName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("攤提來源名稱");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("AmortizationSourceId");

                    b.ToTable("Pms_AmortizationSources");

                    b.HasData(
                        new
                        {
                            AmortizationSourceId = new Guid("e1a1f5b6-4c3b-4d5a-9f1e-2b5a1f5b6c3b"),
                            Amount = 100000.00m,
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteTime = 0L,
                            DeleteUserId = "",
                            DepartmentId = "bf4dadda-59b1-41c6-8eaa-bac176124017",
                            Description = "由政府提供的資金補助",
                            IsDeleted = false,
                            SourceName = "政府補助",
                            UpdateTime = 0L,
                            UpdateUserId = ""
                        },
                        new
                        {
                            AmortizationSourceId = new Guid("f2b2f6b7-5d4c-5e6b-0f2e-3c6b2f6b7d4c"),
                            Amount = 50000.00m,
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteTime = 0L,
                            DeleteUserId = "",
                            DepartmentId = "e1ac337b-d154-4e23-b603-cc51af2b995a",
                            Description = "由部門自行採購的資金來源",
                            IsDeleted = false,
                            SourceName = "自行採購",
                            UpdateTime = 0L,
                            UpdateUserId = ""
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.Asset", b =>
                {
                    b.Property<Guid>("AssetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產流水號");

                    b.Property<decimal>("AccumulatedDepreciationAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("累計折舊金額");

                    b.Property<long>("AcquisitionDate")
                        .HasColumnType("bigint")
                        .HasComment("取得日期");

                    b.Property<Guid>("AssetAccountId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產科目");

                    b.Property<string>("AssetCategoryId")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)")
                        .HasComment("資產類別編號");

                    b.Property<string>("AssetName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("財產名稱");

                    b.Property<string>("AssetNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("財產編號");

                    b.Property<string>("AssetShortName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("財產簡稱");

                    b.Property<string>("AssetStatusId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("財產狀態");

                    b.Property<Guid>("AssetSubAccountId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產子目");

                    b.Property<string>("BuildingAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("建物地址");

                    b.Property<string>("BuildingStructure")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("建物構造");

                    b.Property<string>("BuildingTaxItem")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("適用房屋稅目");

                    b.Property<string>("CertificateNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("權狀號碼");

                    b.Property<long?>("ConstructionDate")
                        .HasColumnType("bigint")
                        .HasComment("興建日期");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<string>("CustodianId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("保管人");

                    b.Property<string>("CustomAssetNo1")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("自訂財產編號一");

                    b.Property<string>("CustomAssetNo2")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("自訂財產編號二");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("DepartmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("部門");

                    b.Property<decimal>("DepreciationAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("折舊金額");

                    b.Property<string>("DivisionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("股別");

                    b.Property<Guid>("EquipmentTypeId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("設備類型");

                    b.Property<decimal>("EstimatedResidualValue")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("預估殘值");

                    b.Property<long?>("EstimatedScrapYear")
                        .HasColumnType("bigint")
                        .HasComment("預計報廢年度");

                    b.Property<decimal>("FloorArea")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("面積(m²)");

                    b.Property<int>("InsurancePeriod")
                        .HasColumnType("int")
                        .HasComment("保固年限");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<decimal>("LandArea")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("面積(m²)");

                    b.Property<string>("LandLocation")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("地段");

                    b.Property<string>("LandNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("地號");

                    b.Property<string>("LandSection")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("地目");

                    b.Property<Guid>("ManufacturerId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("廠牌型號");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("備註");

                    b.Property<decimal>("PublicArea")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("公設(m²)");

                    b.Property<decimal>("PublicValue")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("公告現值");

                    b.Property<decimal>("PurchaseAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("購入金額");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("數量");

                    b.Property<long?>("ScrapDate")
                        .HasColumnType("bigint")
                        .HasComment("報廢日期");

                    b.Property<string>("ScrapReason")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("報廢原因");

                    b.Property<int>("ServiceLife")
                        .HasColumnType("int")
                        .HasComment("耐用年限");

                    b.Property<string>("Specification")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("規格");

                    b.Property<long>("StatusChangeDate")
                        .HasColumnType("bigint")
                        .HasComment("狀態異動日期");

                    b.Property<string>("StorageLocationId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("存放地點");

                    b.Property<decimal>("SubsidyAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("輔助金額");

                    b.Property<Guid>("UnitId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("單位編號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("UsableAfterScrap")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)")
                        .HasComment("報廢後堪用");

                    b.Property<string>("Usage")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("使用狀態");

                    b.Property<long?>("UsageExpiryDate")
                        .HasColumnType("bigint")
                        .HasComment("使用執照日期");

                    b.Property<string>("UsageLicenseNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用執照號碼");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用人");

                    b.HasKey("AssetId");

                    b.ToTable("Pms_Assets");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetAccount", b =>
                {
                    b.Property<Guid>("AssetAccountId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產科目流水號");

                    b.Property<string>("AssetAccountName")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("財產科目名稱");

                    b.Property<string>("AssetAccountNo")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasComment("財產科目編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("AssetAccountId");

                    b.ToTable("Pms_AssetAccounts");

                    b.HasData(
                        new
                        {
                            AssetAccountId = new Guid("1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"),
                            AssetAccountName = "土地",
                            AssetAccountNo = "1",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetAccountId = new Guid("2a3b4c5d-6e7f-8a9b-0c1d-2e3f4a5b6c7d"),
                            AssetAccountName = "房屋及建築",
                            AssetAccountNo = "2",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetAccountId = new Guid("3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d"),
                            AssetAccountName = "機器及設備",
                            AssetAccountNo = "3",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetAccountId = new Guid("4a5b6c7d-8e9f-0a1b-2c3d-4e5f6a7b8c9d"),
                            AssetAccountName = "電腦設備",
                            AssetAccountNo = "4",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetAccountId = new Guid("5a6b7c8d-9e0f-1a2b-3c4d-5e6f7a8b9c0d"),
                            AssetAccountName = "農林設備",
                            AssetAccountNo = "5",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetAccountId = new Guid("6a7b8c9d-0e1f-2a3b-4c5d-6e7f8a9b0c1d"),
                            AssetAccountName = "畜產設備",
                            AssetAccountNo = "6",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetAccountId = new Guid("7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d"),
                            AssetAccountName = "交通運輸設備",
                            AssetAccountNo = "7",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetAccountId = new Guid("8a9b0c1d-2e3f-4a5b-6c7d-8e9f0a1b2c3d"),
                            AssetAccountName = "雜項設備",
                            AssetAccountNo = "8",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetAccountId = new Guid("9a0b1c2d-3e4f-5a6b-7c8d-9e0f1a2b3c4d"),
                            AssetAccountName = "未完工程",
                            AssetAccountNo = "9",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetCarryOut", b =>
                {
                    b.Property<Guid>("CarryOutId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<long?>("ActualCarryOutDate")
                        .HasColumnType("bigint")
                        .HasComment("實際攜出日期");

                    b.Property<long?>("ActualReturnDate")
                        .HasColumnType("bigint")
                        .HasComment("實際歸還日期");

                    b.Property<string>("ApplicantId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("攜出申請人");

                    b.Property<long>("ApplicationDate")
                        .HasColumnType("bigint")
                        .HasComment("攜出申請日期");

                    b.Property<string>("ApprovalComment")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("審核意見");

                    b.Property<long?>("ApprovalDate")
                        .HasColumnType("bigint")
                        .HasComment("審核日期");

                    b.Property<string>("ApproverId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("審核人員");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CarryOutNo")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("攜出申請單號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Destination")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("攜出地點");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("備註");

                    b.Property<long>("PlannedCarryOutDate")
                        .HasColumnType("bigint")
                        .HasComment("預計攜出日期");

                    b.Property<long>("PlannedReturnDate")
                        .HasColumnType("bigint")
                        .HasComment("預計歸還日期");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("攜出目的");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("攜出狀態");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("CarryOutId");

                    b.HasIndex("AssetId");

                    b.ToTable("PmsAssetCarryOut");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetCategory", b =>
                {
                    b.Property<string>("AssetCategoryId")
                        .HasColumnType("nvarchar(1)")
                        .HasComment("資產類別編號");

                    b.Property<string>("AssetCategoryName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("資產類別名稱");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("AssetCategoryId");

                    b.ToTable("Pms_AssetCategory");

                    b.HasData(
                        new
                        {
                            AssetCategoryId = "A",
                            AssetCategoryName = "固定資產",
                            CreateTime = 0L,
                            CreateUserId = "",
                            DeleteTime = 0L,
                            DeleteUserId = "",
                            IsDeleted = false,
                            SortCode = 0,
                            UpdateTime = 0L,
                            UpdateUserId = ""
                        },
                        new
                        {
                            AssetCategoryId = "N",
                            AssetCategoryName = "非固定資產",
                            CreateTime = 0L,
                            CreateUserId = "",
                            DeleteTime = 0L,
                            DeleteUserId = "",
                            IsDeleted = false,
                            SortCode = 0,
                            UpdateTime = 0L,
                            UpdateUserId = ""
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetLocationTransfer", b =>
                {
                    b.Property<Guid>("TransferId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("位置變動單編號");

                    b.Property<string>("ApplicantDepartmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("申請部門");

                    b.Property<string>("ApplicantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("申請人員");

                    b.Property<string>("ApprovalComments")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("審核意見");

                    b.Property<long?>("ApprovalDate")
                        .HasColumnType("bigint")
                        .HasComment("審核日期");

                    b.Property<string>("ApprovalStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("審核狀態");

                    b.Property<string>("ApproverId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("審核人員");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<long?>("ExecutionDate")
                        .HasColumnType("bigint")
                        .HasComment("執行日期");

                    b.Property<string>("ExecutionStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("執行狀態");

                    b.Property<string>("ExecutorId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("執行人員");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("備註");

                    b.Property<long>("TransferDate")
                        .HasColumnType("bigint")
                        .HasComment("變動日期");

                    b.Property<string>("TransferNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("變動單號");

                    b.Property<string>("TransferReason")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("變動原因");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("TransferId");

                    b.ToTable("Pms_AssetLocationTransfer");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetLocationTransferDetail", b =>
                {
                    b.Property<Guid>("DetailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("明細編號");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產編號");

                    b.Property<string>("ChangeItems")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("變動項目");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("DetailNotes")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("明細備註");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("NewCustodianId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新保管人");

                    b.Property<string>("NewDepartmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新部門");

                    b.Property<string>("NewDivisionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新股別");

                    b.Property<string>("NewLocationId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新存放地點");

                    b.Property<string>("NewUserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新使用人");

                    b.Property<string>("OriginalCustodianId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("原保管人");

                    b.Property<string>("OriginalDepartmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("原部門");

                    b.Property<string>("OriginalDivisionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("原股別");

                    b.Property<string>("OriginalLocationId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("原存放地點");

                    b.Property<string>("OriginalUserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("原使用人");

                    b.Property<Guid>("TransferId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("位置變動單編號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("DetailId");

                    b.HasIndex("AssetId");

                    b.HasIndex("TransferId");

                    b.ToTable("Pms_AssetLocationTransferDetail");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetSource", b =>
                {
                    b.Property<Guid>("AssetSourceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("資產來源編號");

                    b.Property<string>("AssetSourceName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("資產來源名稱");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("AssetSourceId");

                    b.ToTable("Pms_AssetSources");

                    b.HasData(
                        new
                        {
                            AssetSourceId = new Guid("308dffc2-0b7b-48d0-9fe1-155e08d36011"),
                            AssetSourceName = "其他來源",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetSourceId = new Guid("6a4d84aa-9a4e-4d65-aad2-5cffeddca95a"),
                            AssetSourceName = "自行採購",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetSourceId = new Guid("7e8b99bf-f254-4d6f-be39-20bd1892d690"),
                            AssetSourceName = "調撥轉入",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetSourceId = new Guid("8ea785e3-5743-4f61-9cd7-cee8dae3b3d9"),
                            AssetSourceName = "租賃",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetSourceId = new Guid("dd50750d-fd8b-4b75-901a-c6ae6a58892b"),
                            AssetSourceName = "捐贈",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetSourceId = new Guid("f5eacef8-aebb-4416-9757-44c64e3bf373"),
                            AssetSourceName = "政府補助",
                            CreateTime = 0L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetStatus", b =>
                {
                    b.Property<Guid>("AssetStatusId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產狀態流水號");

                    b.Property<string>("AssetStatusNo")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasComment("財產狀態編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("財產狀態名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("AssetStatusId");

                    b.ToTable("Pms_AssetStatus");

                    b.HasData(
                        new
                        {
                            AssetStatusId = new Guid("a1b2c3d4-e5f6-4a5b-8c9d-1e2f3a4b5c6d"),
                            AssetStatusNo = "N",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "正常使用",
                            SortCode = 0
                        },
                        new
                        {
                            AssetStatusId = new Guid("b2c3d4e5-f6a7-5b6c-9d0e-2f3a4b5c6d7e"),
                            AssetStatusNo = "I",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "閒置中",
                            SortCode = 1
                        },
                        new
                        {
                            AssetStatusId = new Guid("c3d4e5f6-a7b8-6c7d-0e1f-3a4b5c6d7e8f"),
                            AssetStatusNo = "M",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "維修中",
                            SortCode = 2
                        },
                        new
                        {
                            AssetStatusId = new Guid("d4e5f6a7-b8c9-7d8e-1f2a-4b5c6d7e8f9a"),
                            AssetStatusNo = "R",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "租借中",
                            SortCode = 3
                        },
                        new
                        {
                            AssetStatusId = new Guid("e5f6a7b8-c9d0-8e9f-2a3b-5c6d7e8f9a0b"),
                            AssetStatusNo = "WS",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "待報廢",
                            SortCode = 4
                        },
                        new
                        {
                            AssetStatusId = new Guid("f6a7b8c9-d0e1-9f0a-3b4c-6d7e8f9a0b1c"),
                            AssetStatusNo = "S",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "已報廢",
                            SortCode = 5
                        },
                        new
                        {
                            AssetStatusId = new Guid("a7b8c9d0-e1f2-0a1b-4c5d-7e8f9a0b1c2d"),
                            AssetStatusNo = "WA",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "待驗收",
                            SortCode = 6
                        },
                        new
                        {
                            AssetStatusId = new Guid("b8c9d0e1-f2a3-1b2c-5d6e-8f9a0b1c2d3e"),
                            AssetStatusNo = "ST",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            Name = "庫存中",
                            SortCode = 7
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetSubAccount", b =>
                {
                    b.Property<Guid>("AssetSubAccountId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產子目流水號");

                    b.Property<Guid>("AssetAccountId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產科目編號");

                    b.Property<string>("AssetSubAccountName")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("財產子目名稱");

                    b.Property<string>("AssetSubAccountNo")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasComment("財產科目編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("AssetSubAccountId");

                    b.ToTable("Pms_AssetSubAccounts");

                    b.HasData(
                        new
                        {
                            AssetSubAccountId = new Guid("f2b6e1a4-5c3d-4e8f-9a7b-2d1c0e3f4a5b"),
                            AssetAccountId = new Guid("1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"),
                            AssetSubAccountName = "一般用地",
                            AssetSubAccountNo = "01",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 0
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("a1c4e3b2-8d7f-4e6a-9b5c-3f2d1e0a4b8c"),
                            AssetAccountId = new Guid("1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"),
                            AssetSubAccountName = "農用地",
                            AssetSubAccountNo = "02",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 1
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("b3d5f7e9-2a1c-4b8d-6e3f-5a4c2b1d0e9f"),
                            AssetAccountId = new Guid("2a3b4c5d-6e7f-8a9b-0c1d-2e3f4a5b6c7d"),
                            AssetSubAccountName = "辦公大樓",
                            AssetSubAccountNo = "01",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 2
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("c4e6a8b0-3f2d-5c7e-8b9a-1d4f2e3a5c7b"),
                            AssetAccountId = new Guid("2a3b4c5d-6e7f-8a9b-0c1d-2e3f4a5b6c7d"),
                            AssetSubAccountName = "倉庫",
                            AssetSubAccountNo = "02",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 3
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("d5f7b9c1-4e2a-6d8b-9c0e-2f3a4b5d6e7f"),
                            AssetAccountId = new Guid("3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d"),
                            AssetSubAccountName = "農業機械",
                            AssetSubAccountNo = "01",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 4
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("e6a8c0d2-5f3b-7e9c-0d1f-3a4b5c6d7e8f"),
                            AssetAccountId = new Guid("3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d"),
                            AssetSubAccountName = "生產設備",
                            AssetSubAccountNo = "02",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 5
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("1e9d2e7d-1c3b-4a5f-8e0c-9b0a1c2d3e4f"),
                            AssetAccountId = new Guid("3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d"),
                            AssetSubAccountName = "其他",
                            AssetSubAccountNo = "03",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 6
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("f7b9d1e3-6a4c-8f0d-1e2a-4b5c6d7e8f9a"),
                            AssetAccountId = new Guid("4a5b6c7d-8e9f-0a1b-2c3d-4e5f6a7b8c9d"),
                            AssetSubAccountName = "個人電腦",
                            AssetSubAccountNo = "01",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 6
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("a8c0e2f4-7b5d-9e1a-2f3b-5c6d7e8f9a0b"),
                            AssetAccountId = new Guid("4a5b6c7d-8e9f-0a1b-2c3d-4e5f6a7b8c9d"),
                            AssetSubAccountName = "伺服器",
                            AssetSubAccountNo = "02",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 7
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("f5d9a0c1-b2e3-4f5a-8d7c-9b0e1f2a3c4d"),
                            AssetAccountId = new Guid("4a5b6c7d-8e9f-0a1b-2c3d-4e5f6a7b8c9d"),
                            AssetSubAccountName = "其他",
                            AssetSubAccountNo = "03",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 8
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("b9d1f3e5-8c6a-0f2b-3a4c-6d7e8f9a0b1c"),
                            AssetAccountId = new Guid("5a6b7c8d-9e0f-1a2b-3c4d-5e6f7a8b9c0d"),
                            AssetSubAccountName = "灌溉設備",
                            AssetSubAccountNo = "01",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 8
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("c0e2f4a6-9d7b-1a3c-4b5d-7e8f9a0b1c2d"),
                            AssetAccountId = new Guid("5a6b7c8d-9e0f-1a2b-3c4d-5e6f7a8b9c0d"),
                            AssetSubAccountName = "溫室設備",
                            AssetSubAccountNo = "02",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 9
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("d1f3e5b7-0e8c-2b4d-5c6e-8f9a0b1c2d3e"),
                            AssetAccountId = new Guid("6a7b8c9d-0e1f-2a3b-4c5d-6e7f8a9b0c1d"),
                            AssetSubAccountName = "飼養設備",
                            AssetSubAccountNo = "01",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 10
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("e2f4a6c8-1f9d-3c5e-6d7f-9a0b1c2d3e4f"),
                            AssetAccountId = new Guid("6a7b8c9d-0e1f-2a3b-4c5d-6e7f8a9b0c1d"),
                            AssetSubAccountName = "孵化設備",
                            AssetSubAccountNo = "02",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 11
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("f3e5b7d9-2a0e-4d6f-7e8a-0b1c2d3e4f5a"),
                            AssetAccountId = new Guid("7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d"),
                            AssetSubAccountName = "貨車",
                            AssetSubAccountNo = "01",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 12
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("a4f6c8e0-3b1f-5e7a-8f9b-1c2d3e4f5a6b"),
                            AssetAccountId = new Guid("7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d"),
                            AssetSubAccountName = "小客車",
                            AssetSubAccountNo = "02",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 13
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("3f1a2b4c-5d6e-7f8a-9b0c-1d2e3f4a5b6c"),
                            AssetAccountId = new Guid("7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d"),
                            AssetSubAccountName = "堆高機",
                            AssetSubAccountNo = "03",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 14
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("b5a7d9f1-4c2a-6f8b-9a0c-2d3e4f5a6b7c"),
                            AssetAccountId = new Guid("8a9b0c1d-2e3f-4a5b-6c7d-8e9f0a1b2c3d"),
                            AssetSubAccountName = "辦公設備",
                            AssetSubAccountNo = "01",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 14
                        },
                        new
                        {
                            AssetSubAccountId = new Guid("c6b8e0a2-5d3b-7a9c-0b1d-3e4f5a6b7c8d"),
                            AssetAccountId = new Guid("8a9b0c1d-2e3f-4a5b-6c7d-8e9f0a1b2c3d"),
                            AssetSubAccountName = "空調設備",
                            AssetSubAccountNo = "02",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            IsDeleted = false,
                            SortCode = 15
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.DepreciationForm", b =>
                {
                    b.Property<string>("DepreciationFormId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("固定資產折舊單編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<long>("DepreciationDate")
                        .HasColumnType("bigint")
                        .HasComment("折舊日期");

                    b.Property<string>("DepreciationId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("折舊紀錄編號");

                    b.Property<int>("DepreciationMonth")
                        .HasColumnType("int")
                        .HasComment("折舊月份");

                    b.Property<int>("DepreciationYear")
                        .HasColumnType("int")
                        .HasComment("折舊年度");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("備註");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("DepreciationFormId");

                    b.ToTable("Pms_DepreciationForms");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.DepreciationFormDetail", b =>
                {
                    b.Property<string>("DepreciationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("折舊紀錄編號");

                    b.Property<decimal>("AccumulatedDepreciation")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("累計折舊金額");

                    b.Property<string>("AdjustmentReason")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("調整原因");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產編號");

                    b.Property<decimal>("BeginningBookValue")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("期初帳面價值");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<decimal>("CurrentDepreciation")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("本期折舊金額");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<long>("DepreciationDate")
                        .HasColumnType("bigint")
                        .HasComment("折舊日期");

                    b.Property<string>("DepreciationMethod")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("折舊方法");

                    b.Property<int>("DepreciationMonth")
                        .HasColumnType("int")
                        .HasComment("折舊月份");

                    b.Property<decimal>("DepreciationRate")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("折舊率");

                    b.Property<int>("DepreciationYear")
                        .HasColumnType("int")
                        .HasComment("折舊年度");

                    b.Property<decimal>("EndingBookValue")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("期末帳面價值");

                    b.Property<bool>("IsAdjustment")
                        .HasColumnType("bit")
                        .HasComment("是否為調整紀錄");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("備註");

                    b.Property<decimal>("OriginalAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("原始金額");

                    b.Property<int>("ServiceLifeRemaining")
                        .HasColumnType("int")
                        .HasComment("剩餘耐用年限");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("DepreciationId");

                    b.ToTable("Pms_DepreciationFormDetail");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.EquipmentType", b =>
                {
                    b.Property<Guid>("EquipmentTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("設備類型流水號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("EquipmentTypeNo")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasComment("設備類型編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("設備類型名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("EquipmentTypeId");

                    b.ToTable("Pms_EquipmentType");

                    b.HasData(
                        new
                        {
                            EquipmentTypeId = new Guid("a1b2c3d4-e5f6-4a5b-8c9d-1e2f3a4b5c6d"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "C",
                            IsDeleted = false,
                            Name = "電腦設備",
                            SortCode = 0
                        },
                        new
                        {
                            EquipmentTypeId = new Guid("b2c3d4e5-f6a7-5b6c-9d0e-2f3a4b5c6d7e"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "O",
                            IsDeleted = false,
                            Name = "辦公設備",
                            SortCode = 1
                        },
                        new
                        {
                            EquipmentTypeId = new Guid("c3d4e5f6-a7b8-6c7d-0e1f-3a4b5c6d7e8f"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "T",
                            IsDeleted = false,
                            Name = "通訊設備",
                            SortCode = 2
                        },
                        new
                        {
                            EquipmentTypeId = new Guid("d4e5f6a7-b8c9-7d8e-1f2a-4b5c6d7e8f9a"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "N",
                            IsDeleted = false,
                            Name = "網路設備",
                            SortCode = 3
                        },
                        new
                        {
                            EquipmentTypeId = new Guid("e5f6a7b8-c9d0-8e9f-2a3b-5c6d7e8f9a0b"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "M",
                            IsDeleted = false,
                            Name = "監控設備",
                            SortCode = 4
                        },
                        new
                        {
                            EquipmentTypeId = new Guid("f6a7b8c9-d0e1-9f0a-3b4c-6d7e8f9a0b1c"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "A",
                            IsDeleted = false,
                            Name = "空調設備",
                            SortCode = 5
                        },
                        new
                        {
                            EquipmentTypeId = new Guid("a7b8c9d0-e1f2-0a1b-4c5d-7e8f9a0b1c2d"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "L",
                            IsDeleted = false,
                            Name = "照明設備",
                            SortCode = 6
                        },
                        new
                        {
                            EquipmentTypeId = new Guid("b8c9d0e1-f2a3-1b2c-5d6e-8f9a0b1c2d3e"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "CM",
                            IsDeleted = false,
                            Name = "會議設備",
                            SortCode = 7
                        },
                        new
                        {
                            EquipmentTypeId = new Guid("c9d0e1f2-a3b4-2c3d-6e7f-9a0b1c2d3e4f"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "S",
                            IsDeleted = false,
                            Name = "安全設備",
                            SortCode = 8
                        },
                        new
                        {
                            EquipmentTypeId = new Guid("e3f2c7a1-4d8e-4f7b-935e-5b0d2f9c7e3a"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "T",
                            IsDeleted = false,
                            Name = "交通設備",
                            SortCode = 9
                        },
                        new
                        {
                            EquipmentTypeId = new Guid("d0e1f2a3-b4c5-3d4e-7f8a-0b1c2d3e4f5a"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            EquipmentTypeNo = "OTH",
                            IsDeleted = false,
                            Name = "其他",
                            SortCode = 9
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.InsuranceUnit", b =>
                {
                    b.Property<Guid>("InsuranceUnitId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("承保單位編號");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("公司地址");

                    b.Property<string>("CompanyNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("公司統一編號");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("聯絡信箱");

                    b.Property<string>("ContactPerson")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("聯絡人");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("聯絡電話");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("說明描述");

                    b.Property<decimal>("InsuranceAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("投保金額");

                    b.Property<long?>("InsuranceExpiryDate")
                        .HasColumnType("bigint")
                        .HasComment("投保迄日");

                    b.Property<long?>("InsuranceStartDate")
                        .HasColumnType("bigint")
                        .HasComment("投保起日");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("承保單位名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("Website")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("公司網站");

                    b.HasKey("InsuranceUnitId");

                    b.ToTable("Pms_InsuranceUnits");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.Manufacturer", b =>
                {
                    b.Property<string>("ManufacturerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("廠牌型號編號");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("聯絡信箱");

                    b.Property<string>("ContactPerson")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("聯絡人");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasComment("聯絡電話");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("說明描述");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("ManufacturerName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("製造商名稱");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("型號");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("廠牌名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<string>("Supplier")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("供應商名稱");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("ManufacturerId");

                    b.ToTable("Pms_Manufacturers");

                    b.HasData(
                        new
                        {
                            ManufacturerId = "c5f771fb-a645-4ba1-8c25-ef42ad58ac13",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "張維修",
                            ContactPhone = "02-27287888",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "法人級桌上型電腦，適合辦公室使用",
                            IsDeleted = false,
                            ManufacturerName = "Dell Inc.",
                            Model = "OptiPlex 7090",
                            Name = "Dell",
                            SortCode = 0,
                            Supplier = "台灣戴爾股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "f1d7e39a-0c83-4b4a-9ec5-712b85d27311",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "李業務",
                            ContactPhone = "02-37897890",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "商務筆記型電腦，適合移動辦公",
                            IsDeleted = false,
                            ManufacturerName = "Hewlett-Packard",
                            Model = "ProBook 450 G8",
                            Name = "HP",
                            SortCode = 1,
                            Supplier = "台灣惠普資訊科技有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "a2e9f6b3-8c71-4d5e-b26f-9f5a8d7c1e3b",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "王經理",
                            ContactPhone = "02-87939168",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "迷你電腦，節省桌面空間",
                            IsDeleted = false,
                            ManufacturerName = "Lenovo Group Ltd.",
                            Model = "ThinkCentre M70q",
                            Name = "Lenovo",
                            SortCode = 2,
                            Supplier = "聯想科技股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "b7d4c8e5-9f2a-4d6b-8c3e-1a5f9d0e7b4c",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "陳經理",
                            ContactPhone = "02-26586868",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "彩色多功能事務機，可列印、掃描、影印、傳真",
                            IsDeleted = false,
                            ManufacturerName = "Xerox Corporation",
                            Model = "WorkCentre 6515",
                            Name = "Xerox",
                            SortCode = 3,
                            Supplier = "富士全錄股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "c8e5a2f1-7b3d-4c9a-8e6f-2d1b5c4a3f7d",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "林小姐",
                            ContactPhone = "02-27319090",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "辦公傢俱與文具，提供完整辦公解決方案",
                            IsDeleted = false,
                            ManufacturerName = "KOKUYO Co., Ltd.",
                            Model = "Campus系列",
                            Name = "KOKUYO",
                            SortCode = 4,
                            Supplier = "國譽文具股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "d9f4b3e2-6c5a-4b8d-9f7e-1a2c3d4e5f6a",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "黃工程師",
                            ContactPhone = "02-27196000",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "法人IP電話，高清晰度語音通話",
                            IsDeleted = false,
                            ManufacturerName = "Cisco Systems, Inc.",
                            Model = "IP Phone 8841",
                            Name = "Cisco",
                            SortCode = 5,
                            Supplier = "台灣思科系統股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "e8d7c6b5-4a3f-2e1d-9c8b-7a6f5d4e3c2b",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "吳專員",
                            ContactPhone = "02-27583588",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "法人通訊手機，支援多人視訊會議",
                            IsDeleted = false,
                            ManufacturerName = "Samsung Electronics",
                            Model = "Galaxy A52",
                            Name = "Samsung",
                            SortCode = 6,
                            Supplier = "台灣三星電子股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "f7e6d5c4-3b2a-1f9e-8d7c-6b5a4d3e2f1c",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "黃工程師",
                            ContactPhone = "02-27196000",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "法人級網路交換器，高性能、高安全性",
                            IsDeleted = false,
                            ManufacturerName = "Cisco Systems, Inc.",
                            Model = "Catalyst 9200",
                            Name = "Cisco",
                            SortCode = 7,
                            Supplier = "台灣思科系統股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "周工程師",
                            ContactPhone = "02-27327988",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "法人級Wi-Fi 6路由器，高速穩定連線",
                            IsDeleted = false,
                            ManufacturerName = "ASUSTeK Computer Inc.",
                            Model = "RT-AX86U",
                            Name = "ASUS",
                            SortCode = 8,
                            Supplier = "華碩電腦股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "劉工程師",
                            ContactPhone = "02-27887799",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "4MP紅外線半球網路攝影機，適合室內監控",
                            IsDeleted = false,
                            ManufacturerName = "Hangzhou Hikvision Digital Technology",
                            Model = "DS-2CD2143G0-I",
                            Name = "HIKVISION",
                            SortCode = 9,
                            Supplier = "海康威視數位科技股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "張工程師",
                            ContactPhone = "02-23145678",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "6MP紅外線變焦半球網路攝影機，適合室外監控",
                            IsDeleted = false,
                            ManufacturerName = "Zhejiang Dahua Technology",
                            Model = "IPC-HDBW4631R-ZS",
                            Name = "Dahua",
                            SortCode = 10,
                            Supplier = "大華系統科技股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "d4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "許經理",
                            ContactPhone = "02-27861234",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "變頻冷暖空調，適合辦公室使用",
                            IsDeleted = false,
                            ManufacturerName = "Daikin Industries, Ltd.",
                            Model = "FTXM50UVMA",
                            Name = "Daikin",
                            SortCode = 11,
                            Supplier = "大金空調股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "e5f6a7b8-c9d0-1e2f-3a4b-5c6d7e8f9a0b",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "楊經理",
                            ContactPhone = "02-28756789",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "變頻冷專空調，節能省電",
                            IsDeleted = false,
                            ManufacturerName = "Mitsubishi Electric Corporation",
                            Model = "MSY-GR42VF",
                            Name = "Mitsubishi Electric",
                            SortCode = 12,
                            Supplier = "台灣三菱電機股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "f6a7b8c9-d0e1-2f3a-4b5c-6d7e8f9a0b1c",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "林經理",
                            ContactPhone = "02-34567890",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "LED平板燈，適合辦公室照明",
                            IsDeleted = false,
                            ManufacturerName = "Philips Lighting",
                            Model = "LED Panel RC048B",
                            Name = "Philips",
                            SortCode = 13,
                            Supplier = "飛利浦照明股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "a7b8c9d0-e1f2-3a4b-5c6d-7e8f9a0b1c2d",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "張經理",
                            ContactPhone = "02-45678901",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "LED燈管，可直接替換傳統螢光燈管",
                            IsDeleted = false,
                            ManufacturerName = "OSRAM GmbH",
                            Model = "SubstiTUBE T8 Universal",
                            Name = "Osram",
                            SortCode = 14,
                            Supplier = "台灣歐司朗照明股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "b8c9d0e1-f2a3-4b5c-6d7e-8f9a0b1c2d3e",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "劉經理",
                            ContactPhone = "02-56789012",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "無線藍牙喇叭，適合中小型會議室",
                            IsDeleted = false,
                            ManufacturerName = "Sony Corporation",
                            Model = "SRS-XB43",
                            Name = "SONY",
                            SortCode = 15,
                            Supplier = "台灣索尼股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "c9d0e1f2-a3b4-5c6d-7e8f-9a0b1c2d3e4f",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "周經理",
                            ContactPhone = "02-67890123",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "高階視訊會議系統，適合大型會議室",
                            IsDeleted = false,
                            ManufacturerName = "Logitech International S.A.",
                            Model = "Rally Plus",
                            Name = "Logitech",
                            SortCode = 16,
                            Supplier = "台灣羅技電子股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "d0e1f2a3-b4c5-6d7e-8f9a-0b1c2d3e4f5a",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "許工程師",
                            ContactPhone = "02-78901234",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "門禁管理系統，高安全性",
                            IsDeleted = false,
                            ManufacturerName = "dormakaba Group",
                            Model = "Exos 9300",
                            Name = "Kaba",
                            SortCode = 17,
                            Supplier = "台灣多瑪科巴股份有限公司",
                            UpdateUserId = ""
                        },
                        new
                        {
                            ManufacturerId = "e1f2a3b4-c5d6-7e8f-9a0b-1c2d3e4f5a6b",
                            ContactEmail = "<EMAIL>",
                            ContactPerson = "王工程師",
                            ContactPhone = "02-89012345",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            Description = "防盜警報系統，適合辦公室安全防護",
                            IsDeleted = false,
                            ManufacturerName = "Honeywell International Inc.",
                            Model = "Vista-21iP",
                            Name = "Honeywell",
                            SortCode = 18,
                            Supplier = "台灣霍尼威爾股份有限公司",
                            UpdateUserId = ""
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.PmsSystemParameter", b =>
                {
                    b.Property<string>("ParameterId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("系統參數編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit")
                        .HasComment("是否啟用");

                    b.Property<string>("ParameterDescription")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("參數描述");

                    b.Property<string>("ParameterName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("參數名稱");

                    b.Property<string>("ParameterType")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("參數類型");

                    b.Property<string>("ParameterValue")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("參數值");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasComment("排序順序");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("ParameterId");

                    b.ToTable("Pms_SystemParameters");

                    b.HasData(
                        new
                        {
                            ParameterId = "a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "直線法是將資產的成本減去預計殘值後，除以預計使用年限，每年提列相同金額的折舊費用",
                            ParameterName = "直線法",
                            ParameterType = "depreciation_method",
                            ParameterValue = "{\"key\":\"straight_line\",\"description\":\"按照資產成本減去殘值後，依耐用年限平均計算每年折舊額\",\"formula\":\"(原始成本-殘值)/耐用年限\",\"isDefault\":true}",
                            SortOrder = 1,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "餘額遞減法是以資產的賬面價值乘以一個固定的折舊率來計算折舊費用，折舊率通常為直線法折舊率的2倍",
                            ParameterName = "餘額遞減法",
                            ParameterType = "depreciation_method",
                            ParameterValue = "{\"key\":\"declining_balance\",\"description\":\"按照資產賬面價值乘以一個固定折舊率計算每年折舊額\",\"formula\":\"賬面價值*折舊率\",\"rate\": 0.4,\"isDefault\":false}",
                            SortOrder = 2,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "e5f6a7b8-9c0d-1e2f-3a4b-5c6d7e8f9a0b",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "土地不計提折舊",
                            ParameterName = "餘額遞減法折舊率-土地",
                            ParameterType = "declining_balance_rate",
                            ParameterValue = "{\"assetAccountId\":\"1A2B3C4D-5E6F-7A8B-9C0D-1E2F3A4B5C6D\",\"rate\":0}",
                            SortOrder = 10,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "f6a7b8c9-0d1e-2f3a-4b5c-6d7e8f9a0b1c",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "估計使用壽命長，折舊率低(參考使用年限:50年)",
                            ParameterName = "餘額遞減法折舊率-房屋及建築",
                            ParameterType = "declining_balance_rate",
                            ParameterValue = "{\"assetAccountId\":\"2A3B4C5D-6E7F-8A9B-0C1D-2E3F4A5B6C7D\",\"rate\":0.04}",
                            SortOrder = 11,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "a7b8c9d0-1e2f-3a4b-5c6d-7e8f9a0b1c2d",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "中長期設備(參考使用年限:10年)",
                            ParameterName = "餘額遞減法折舊率-機器及設備",
                            ParameterType = "declining_balance_rate",
                            ParameterValue = "{\"assetAccountId\":\"3A4B5C6D-7E8F-9A0B-1C2D-3E4F5A6B7C8D\",\"rate\":0.2}",
                            SortOrder = 12,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "b8c9d0e1-2f3a-4b5c-6d7e-8f9a0b1c2d3e",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "技術更新快，壽命短(參考使用年限:5年)",
                            ParameterName = "餘額遞減法折舊率-電腦設備",
                            ParameterType = "declining_balance_rate",
                            ParameterValue = "{\"assetAccountId\":\"4A5B6C7D-8E9F-0A1B-2C3D-4E5F6A7B8C9D\",\"rate\":0.4}",
                            SortOrder = 13,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "c9d0e1f2-3a4b-5c6d-7e8f-9a0b1c2d3e4f",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "農用機械或設施(參考使用年限:8年)",
                            ParameterName = "餘額遞減法折舊率-農林設備",
                            ParameterType = "declining_balance_rate",
                            ParameterValue = "{\"assetAccountId\":\"5A6B7C8D-9E0F-1A2B-3C4D-5E6F7A8B9C0D\",\"rate\":0.25}",
                            SortOrder = 14,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "d0e1f2a3-4b5c-6d7e-8f9a-0b1c2d3e4f5a",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "用於養殖的設施(參考使用年限:6年)",
                            ParameterName = "餘額遞減法折舊率-畜產設備",
                            ParameterType = "declining_balance_rate",
                            ParameterValue = "{\"assetAccountId\":\"6A7B8C9D-0E1F-2A3B-4C5D-6E7F8A9B0C1D\",\"rate\":0.33}",
                            SortOrder = 15,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "e1f2a3b4-5c6d-7e8f-9a0b-1c2d3e4f5a6b",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "車輛、機車等(參考使用年限:5年)",
                            ParameterName = "餘額遞減法折舊率-交通運輸設備",
                            ParameterType = "declining_balance_rate",
                            ParameterValue = "{\"assetAccountId\":\"7A8B9C0D-1E2F-3A4B-5C6D-7E8F9A0B1C2D\",\"rate\":0.4}",
                            SortOrder = 16,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "f2a3b4c5-6d7e-8f9a-0b1c-2d3e4f5a6b7c",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "各類小型設備(參考使用年限:5年)",
                            ParameterName = "餘額遞減法折舊率-雜項設備",
                            ParameterType = "declining_balance_rate",
                            ParameterValue = "{\"assetAccountId\":\"8A9B0C1D-2E3F-4A5B-6C7D-8E9F0A1B2C3D\",\"rate\":0.4}",
                            SortOrder = 17,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "a3b4c5d6-7e8f-9a0b-1c2d-3e4f5a6b7c8d",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "尚未完工，不能提列折舊",
                            ParameterName = "餘額遞減法折舊率-未完工程",
                            ParameterType = "declining_balance_rate",
                            ParameterValue = "{\"assetAccountId\":\"9A0B1C2D-3E4F-5A6B-7C8D-9E0F1A2B3C4D\",\"rate\":0}",
                            SortOrder = 18,
                            UpdateUserId = ""
                        },
                        new
                        {
                            ParameterId = "f7e6d5c4-b3a2-1098-7654-3210fedcba98",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            DeleteUserId = "",
                            IsDeleted = false,
                            IsEnabled = true,
                            ParameterDescription = "標示系統是否已完成初始化設定",
                            ParameterName = "系統初始化狀態",
                            ParameterType = "initialization",
                            ParameterValue = "{\"isInitialized\": false, \"initializationDate\": null}",
                            SortOrder = 0,
                            UpdateUserId = ""
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.PmsUserRole", b =>
                {
                    b.Property<Guid>("PmsUserRoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產系統使用者身分編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(MAX)")
                        .HasComment("說明");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("身分名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序編碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("PmsUserRoleId");

                    b.ToTable("PmsUserRoles");

                    b.HasData(
                        new
                        {
                            PmsUserRoleId = new Guid("1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "負責保管及管理財產資產",
                            IsDeleted = false,
                            RoleName = "保管人",
                            SortCode = 1
                        },
                        new
                        {
                            PmsUserRoleId = new Guid("2b3c4d5e-6f7a-8b9c-0d1e-2f3a4b5c6d7e"),
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "實際使用財產資產的人員",
                            IsDeleted = false,
                            RoleName = "使用人",
                            SortCode = 2
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.PmsUserRoleMapping", b =>
                {
                    b.Property<string>("PmsUserRoleMappingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("對應編號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<Guid>("PmsUserRoleId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產系統使用者身分編號");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("使用者編號");

                    b.HasKey("PmsUserRoleMappingId");

                    b.HasIndex("PmsUserRoleId");

                    b.HasIndex("UserId");

                    b.ToTable("PmsUserRoleMappings");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.StorageLocation", b =>
                {
                    b.Property<Guid>("StorageLocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasComment("存放地點編號");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasComment("詳細地址");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(500)")
                        .HasComment("地點描述");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("存放地點名稱");

                    b.Property<int>("SortCode")
                        .HasColumnType("int")
                        .HasComment("排序號碼");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.HasKey("StorageLocationId");

                    b.ToTable("Pms_StorageLocations");

                    b.HasData(
                        new
                        {
                            StorageLocationId = new Guid("08291a5c-7906-4a7d-9b4c-57d6fd119642"),
                            Address = "高雄市鳳山區中山西路316號1樓",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "行政大樓一樓辦公室及接待區",
                            IsDeleted = false,
                            Name = "行政大樓一樓",
                            SortCode = 0
                        },
                        new
                        {
                            StorageLocationId = new Guid("18c64fe1-62f8-4e7c-9b8a-0e28e3f4b7c9"),
                            Address = "高雄市鳳山區中山西路316號2樓",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "行政大樓二樓辦公室及會議室",
                            IsDeleted = false,
                            Name = "行政大樓二樓",
                            SortCode = 1
                        },
                        new
                        {
                            StorageLocationId = new Guid("2a7b9d32-5e4c-4f1d-8c7a-6b3e2a1f8d9c"),
                            Address = "高雄市鳳山區中山西路316號3樓",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "行政大樓三樓行政管理處",
                            IsDeleted = false,
                            Name = "行政大樓三樓",
                            SortCode = 2
                        },
                        new
                        {
                            StorageLocationId = new Guid("3b8c0e43-6f5d-4a2e-9d8b-7c4f3b2e1d0a"),
                            Address = "高雄市鳳山區中山西路316號4樓",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "行政大樓四樓資訊中心辦公室",
                            IsDeleted = false,
                            Name = "行政大樓四樓",
                            SortCode = 3
                        },
                        new
                        {
                            StorageLocationId = new Guid("4c9d1f54-7a6e-5b3f-0e9c-8d5a4c3f2e1b"),
                            Address = "高雄市鳳山區中山西路316號B1",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "資訊機房及伺服器區域",
                            IsDeleted = false,
                            Name = "資訊機房",
                            SortCode = 4
                        },
                        new
                        {
                            StorageLocationId = new Guid("5d0e2a65-8b7f-6c4a-1f0d-9e6b5d4a3f2c"),
                            Address = "高雄市鳳山區中山西路318號",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "一般物品及設備儲存倉庫",
                            IsDeleted = false,
                            Name = "一號倉庫",
                            SortCode = 5
                        },
                        new
                        {
                            StorageLocationId = new Guid("6e1f3b76-9c8a-7d5b-2a1e-0f7c6e5b4a3d"),
                            Address = "高雄市大寮區光明路1號",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "農業示範及實驗區域",
                            IsDeleted = false,
                            Name = "農業示範區",
                            SortCode = 6
                        },
                        new
                        {
                            StorageLocationId = new Guid("7f2a4c87-0d9b-8e6c-3b2f-1a8d7f6c5b4e"),
                            Address = "高雄市鳳山區中山西路320號",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "農業設備及物資儲存倉庫",
                            IsDeleted = false,
                            Name = "二號倉庫",
                            SortCode = 7
                        },
                        new
                        {
                            StorageLocationId = new Guid("8a3b5d98-1e0c-9f7d-4c3a-2b9e8a7d6c5f"),
                            Address = "高雄市鳳山區中山西路322號",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "農業技術研發及實驗中心",
                            IsDeleted = false,
                            Name = "研發中心",
                            SortCode = 8
                        },
                        new
                        {
                            StorageLocationId = new Guid("9b4c6e09-2f1d-0a8e-5d4b-3c0f9b8e7d6a"),
                            Address = "高雄市鳳山區中山西路324號",
                            CreateTime = 1742266132L,
                            CreateUserId = "9a23dc8b-9dec-4d79-a9fa-fc5a094d2d65",
                            Description = "教育訓練及會議中心",
                            IsDeleted = false,
                            Name = "教育訓練中心",
                            SortCode = 9
                        });
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.VendorMaintenance", b =>
                {
                    b.Property<string>("MaintenanceNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("修繕單號");

                    b.Property<decimal?>("ActualCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("ActualEndDate")
                        .HasColumnType("bigint");

                    b.Property<long>("ActualStartDate")
                        .HasColumnType("bigint");

                    b.Property<string>("ApplicantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("申請人編號");

                    b.Property<long>("ApplicationDate")
                        .HasColumnType("bigint")
                        .HasComment("申請日期");

                    b.Property<string>("ApprovalComment")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("ApprovalDate")
                        .HasColumnType("bigint");

                    b.Property<string>("ApproverId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("審核人編號");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("財產流水號");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint")
                        .HasComment("新增時間");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("新增者編號");

                    b.Property<long?>("DeleteTime")
                        .HasColumnType("bigint")
                        .HasComment("刪除時間");

                    b.Property<string>("DeleteUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("刪除者編號");

                    b.Property<string>("DepartmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasComment("申請部門編號");

                    b.Property<decimal>("EstimatedCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("預估修繕費用");

                    b.Property<string>("FaultDescription")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("故障描述");

                    b.Property<long>("InspectionDate")
                        .HasColumnType("bigint");

                    b.Property<string>("InspectionNotes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("InspectionResult")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("InspectorId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("驗收人編號");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("刪除狀態");

                    b.Property<string>("MaintenanceResult")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("MaintenanceType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("修繕類型");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("ScheduledEndDate")
                        .HasColumnType("bigint");

                    b.Property<long>("ScheduledStartDate")
                        .HasColumnType("bigint");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint")
                        .HasComment("更新時間");

                    b.Property<string>("UpdateUserId")
                        .HasColumnType("nvarchar(100)")
                        .HasComment("更新者編號");

                    b.Property<string>("UrgencyLevel")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("緊急程度");

                    b.Property<string>("VendorContact")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("廠商聯絡人");

                    b.Property<string>("VendorName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("廠商名稱");

                    b.Property<string>("VendorPhone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("廠商聯絡電話");

                    b.HasKey("MaintenanceNumber");

                    b.HasIndex("AssetId");

                    b.ToTable("VendorMaintenance");
                });

            modelBuilder.Entity("AssetAmortizationSourceMapping", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Pms.AmortizationSource", "AmortizationSource")
                        .WithMany("AssetAmortizationSources")
                        .HasForeignKey("AmortizationSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FAST_ERP_Backend.Models.Pms.Asset", "Asset")
                        .WithMany("AssetAmortizationSources")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AmortizationSource");

                    b.Navigation("Asset");
                });

            modelBuilder.Entity("AssetAssetSourceMapping", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Pms.Asset", "Asset")
                        .WithMany("AssetAssetSources")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FAST_ERP_Backend.Models.Pms.AssetSource", "AssetSource")
                        .WithMany("AssetAssetSources")
                        .HasForeignKey("AssetSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("AssetSource");
                });

            modelBuilder.Entity("AssetInsuranceUnitMapping", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Pms.Asset", "Asset")
                        .WithMany("AssetInsuranceUnits")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FAST_ERP_Backend.Models.Pms.InsuranceUnit", "InsuranceUnit")
                        .WithMany("AssetInsuranceUnits")
                        .HasForeignKey("InsuranceUnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("InsuranceUnit");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.FileUpload", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Common.EnterpriseGroups", "EnterpriseGroups")
                        .WithMany()
                        .HasForeignKey("EnterpriseGroupsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EnterpriseGroups");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.RolesPermissions", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Common.Roles", "Roles")
                        .WithMany("RolesPermissions")
                        .HasForeignKey("RolesId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("FAST_ERP_Backend.Models.Common.SystemMenu", "SystemMenu")
                        .WithMany("RolesPermissions")
                        .HasForeignKey("SystemMenuId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Roles");

                    b.Navigation("SystemMenu");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.CustomerCategory", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.CustomerCategory", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentID");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.CustomerDetail", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.CustomerCategory", "CustomerCategory")
                        .WithMany()
                        .HasForeignKey("CustomerCategoryID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("FAST_ERP_Backend.Models.Ims.Partner", null)
                        .WithOne("CustomerDetail")
                        .HasForeignKey("FAST_ERP_Backend.Models.Ims.CustomerDetail", "PartnerID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CustomerCategory");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.EnterpriseDetail", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.Partner", null)
                        .WithOne("EnterpriseDetail")
                        .HasForeignKey("FAST_ERP_Backend.Models.Ims.EnterpriseDetail", "PartnerID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.IndividualDetail", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.Partner", null)
                        .WithOne("IndividualDetail")
                        .HasForeignKey("FAST_ERP_Backend.Models.Ims.IndividualDetail", "PartnerID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.Item", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.ItemCategory", "ItemCategory")
                        .WithMany("Items")
                        .HasForeignKey("ItemCategoryID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ItemCategory");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.ItemCategory", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.ItemCategory", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentID");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.ItemPrice", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.Item", "Item")
                        .WithMany("Prices")
                        .HasForeignKey("ItemID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FAST_ERP_Backend.Models.Ims.PriceType", "PriceType")
                        .WithMany()
                        .HasForeignKey("PriceTypeID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Item");

                    b.Navigation("PriceType");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.Partner", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.SupplierCategory", null)
                        .WithMany("Partners")
                        .HasForeignKey("SupplierCategoryID");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.PartnerAddress", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.Partner", "Partner")
                        .WithMany("Addresses")
                        .HasForeignKey("PartnerID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Partner");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.PartnerContact", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.Contact", "Contact")
                        .WithMany("PartnerContacts")
                        .HasForeignKey("ContactID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FAST_ERP_Backend.Models.Ims.ContactRole", "ContactRole")
                        .WithMany("PartnerContacts")
                        .HasForeignKey("ContactRoleID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FAST_ERP_Backend.Models.Ims.Partner", "Partner")
                        .WithMany("PartnerContacts")
                        .HasForeignKey("PartnerID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contact");

                    b.Navigation("ContactRole");

                    b.Navigation("Partner");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.SupplierCategory", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.SupplierCategory", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentID");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.SupplierDetail", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Ims.Partner", null)
                        .WithOne("SupplierDetail")
                        .HasForeignKey("FAST_ERP_Backend.Models.Ims.SupplierDetail", "PartnerID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FAST_ERP_Backend.Models.Ims.SupplierCategory", "SupplierCategory")
                        .WithMany()
                        .HasForeignKey("SupplierCategoryID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("SupplierCategory");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AccessoryEquipment", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Pms.Asset", "Asset")
                        .WithMany("AccessoryEquipments")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetCarryOut", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Pms.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetLocationTransferDetail", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Pms.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FAST_ERP_Backend.Models.Pms.AssetLocationTransfer", "Transfer")
                        .WithMany("TransferDetails")
                        .HasForeignKey("TransferId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("Transfer");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.PmsUserRoleMapping", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Pms.PmsUserRole", "PmsUserRole")
                        .WithMany()
                        .HasForeignKey("PmsUserRoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FAST_ERP_Backend.Models.Common.Users", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("PmsUserRole");

                    b.Navigation("User");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.VendorMaintenance", b =>
                {
                    b.HasOne("FAST_ERP_Backend.Models.Pms.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.Roles", b =>
                {
                    b.Navigation("RolesPermissions");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Common.SystemMenu", b =>
                {
                    b.Navigation("RolesPermissions");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.Contact", b =>
                {
                    b.Navigation("PartnerContacts");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.ContactRole", b =>
                {
                    b.Navigation("PartnerContacts");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.CustomerCategory", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.Item", b =>
                {
                    b.Navigation("Prices");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.ItemCategory", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("Items");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.Partner", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("CustomerDetail");

                    b.Navigation("EnterpriseDetail");

                    b.Navigation("IndividualDetail");

                    b.Navigation("PartnerContacts");

                    b.Navigation("SupplierDetail");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Ims.SupplierCategory", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("Partners");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AmortizationSource", b =>
                {
                    b.Navigation("AssetAmortizationSources");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.Asset", b =>
                {
                    b.Navigation("AccessoryEquipments");

                    b.Navigation("AssetAmortizationSources");

                    b.Navigation("AssetAssetSources");

                    b.Navigation("AssetInsuranceUnits");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetLocationTransfer", b =>
                {
                    b.Navigation("TransferDetails");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.AssetSource", b =>
                {
                    b.Navigation("AssetAssetSources");
                });

            modelBuilder.Entity("FAST_ERP_Backend.Models.Pms.InsuranceUnit", b =>
                {
                    b.Navigation("AssetInsuranceUnits");
                });
#pragma warning restore 612, 618
        }
    }
}
