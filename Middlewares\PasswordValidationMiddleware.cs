﻿using System.Security.Claims;
using FAST_ERP_Backend.Models.Common;
using Microsoft.IdentityModel.Tokens;

namespace FAST_ERP_Backend.Middlewares
{
    public class PasswordValidationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _configuration;
        private readonly TokenHandler _tokenHandler;

        public PasswordValidationMiddleware(RequestDelegate next, IConfiguration configuration)
        {
            _next = next;
            _configuration = configuration;
            _tokenHandler = new TokenHandler(configuration);
        }

        public async Task InvokeAsync(HttpContext context)
        {
            //驗證路徑
            if (!ShouldValidatePassword(context))
            {
                // 先檢查是否帶有 Token,沒有則設成空字串
                var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last() ?? string.Empty;
                try
                {
                    // 驗證 Token
                    var claimsPrincipal = _tokenHandler.DecodeJwtToken(token);
                    context.User = claimsPrincipal;
                    await _next(context);
                    return;
                }
                catch (SecurityTokenException)
                {
                    context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    await context.Response.WriteAsync("請重新登入");
                    return;
                }
            }
            await _next(context);
        }
        //驗證路徑
        private bool ShouldValidatePassword(HttpContext context)
        {
            // 依需求新增其他路徑
            var protectedPaths = new[] {
                "/api/Login" ,
                "/Hubs/SignalRHub"
            };
            return protectedPaths.Any(path =>
                context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase));
        }
    }
}
