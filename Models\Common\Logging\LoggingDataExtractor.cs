using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using FAST_ERP_Backend.Models.Common.Logging;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 日誌資料提取器
    /// 負責將 Entity Framework 實體轉換為序列化安全的日誌資料
    /// </summary>
    public static class LoggingDataExtractor
    {
        #region 快取和設定

        /// <summary> 屬性資訊快取，提升效能 </summary>
        private static readonly ConcurrentDictionary<Type, PropertyInfo[]> _propertyCache = new();

        /// <summary> 安全屬性類型清單 </summary>
        private static readonly HashSet<Type> _safeTypes = new()
        {
            typeof(string), typeof(int), typeof(long), typeof(short), typeof(byte),
            typeof(decimal), typeof(double), typeof(float), typeof(bool),
            typeof(DateTime), typeof(DateTimeOffset), typeof(TimeSpan), typeof(Guid),
            typeof(int?), typeof(long?), typeof(short?), typeof(byte?),
            typeof(decimal?), typeof(double?), typeof(float?), typeof(bool?),
            typeof(DateTime?), typeof(DateTimeOffset?), typeof(TimeSpan?), typeof(Guid?)
        };

        /// <summary> 最大屬性數量限制 </summary>
        private const int MaxPropertiesPerEntity = 50;

        #endregion

        #region 公開方法

        /// <summary>
        /// 從實體變更項目提取日誌資料
        /// </summary>
        /// <param name="entry">實體變更項目</param>
        /// <returns>實體日誌 DTO</returns>
        public static EntityLoggingDTO ExtractFromEntityEntry(EntityEntry entry)
        {
            var entityType = entry.Entity.GetType();
            var entityId = ExtractEntityId(entry.Entity);
            var userId = ExtractUserId(entry.Entity);

            var loggingDto = new EntityLoggingDTO
            {
                EntityType = entityType.Name,
                EntityId = entityId,
                EntityState = entry.State.ToString(),
                Timestamp = DateTime.UtcNow,
                UserId = userId
            };

            try
            {
                // 提取當前屬性值
                loggingDto.Properties = ExtractSafeProperties(entry.Entity);

                // 如果是修改狀態，提取原始值和變更屬性
                if (entry.State == EntityState.Modified)
                {
                    loggingDto.OriginalProperties = ExtractOriginalProperties(entry);
                    loggingDto.ChangedProperties = GetChangedProperties(entry);
                }
            }
            catch (Exception ex)
            {
                // 如果屬性提取失敗，至少保留基本資訊
                loggingDto.Metadata = new Dictionary<string, object>
                {
                    { "ExtractionError", ex.Message },
                    { "ExtractionTime", DateTime.UtcNow }
                };
            }

            return loggingDto;
        }

        /// <summary>
        /// 從任意物件提取安全屬性
        /// </summary>
        /// <param name="obj">要提取的物件</param>
        /// <returns>安全屬性字典</returns>
        public static Dictionary<string, object?> ExtractSafeProperties(object obj)
        {
            if (obj == null) return new Dictionary<string, object?>();

            var result = new Dictionary<string, object?>();
            var properties = GetCachedProperties(obj.GetType());
            var count = 0;

            foreach (var prop in properties)
            {
                if (count >= MaxPropertiesPerEntity) break;

                try
                {
                    // 跳過不安全的屬性
                    if (!IsSafeProperty(prop)) continue;

                    var value = prop.GetValue(obj);
                    result[prop.Name] = ProcessPropertyValue(value);
                    count++;
                }
                catch (Exception ex)
                {
                    // 記錄屬性讀取錯誤，但不中斷整個過程
                    result[prop.Name] = $"[屬性讀取錯誤: {ex.Message}]";
                }
            }

            return result;
        }

        /// <summary>
        /// 建立變更日誌 DTO
        /// </summary>
        /// <param name="changedEntries">變更的實體項目清單</param>
        /// <param name="transactionId">交易 ID</param>
        /// <param name="source">來源</param>
        /// <returns>變更日誌 DTO</returns>
        public static EntityChangeLogDTO CreateChangeLog(
            IEnumerable<EntityEntry> changedEntries, 
            string transactionId, 
            string source = "System")
        {
            var changeLog = new EntityChangeLogDTO
            {
                TransactionId = transactionId,
                Source = source,
                ChangeTime = DateTime.UtcNow
            };

            foreach (var entry in changedEntries)
            {
                try
                {
                    var entityLog = ExtractFromEntityEntry(entry);
                    changeLog.ChangedEntities.Add(entityLog);
                }
                catch (Exception ex)
                {
                    // 如果單個實體提取失敗，建立錯誤記錄
                    var errorLog = new EntityLoggingDTO
                    {
                        EntityType = entry.Entity.GetType().Name,
                        EntityId = ExtractEntityId(entry.Entity),
                        EntityState = entry.State.ToString(),
                        Timestamp = DateTime.UtcNow,
                        Metadata = new Dictionary<string, object>
                        {
                            { "Error", ex.Message },
                            { "ErrorType", "EntityExtractionFailure" }
                        }
                    };
                    changeLog.ChangedEntities.Add(errorLog);
                }
            }

            changeLog.TotalChanges = changeLog.ChangedEntities.Count;
            return changeLog;
        }

        #endregion

        #region 私有輔助方法

        /// <summary> 取得快取的屬性資訊 </summary>
        private static PropertyInfo[] GetCachedProperties(Type type)
        {
            return _propertyCache.GetOrAdd(type, t => 
                t.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                 .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                 .ToArray());
        }

        /// <summary> 檢查屬性是否安全可序列化 </summary>
        private static bool IsSafeProperty(PropertyInfo property)
        {
            var propertyType = property.PropertyType;

            // 檢查是否為安全類型
            if (_safeTypes.Contains(propertyType)) return true;

            // 檢查是否為列舉類型
            if (propertyType.IsEnum || (Nullable.GetUnderlyingType(propertyType)?.IsEnum == true))
                return true;

            // 排除導航屬性和集合
            if (IsNavigationProperty(property)) return false;

            return false;
        }

        /// <summary> 檢查是否為導航屬性 </summary>
        private static bool IsNavigationProperty(PropertyInfo property)
        {
            var propertyType = property.PropertyType;

            // 檢查是否有 EF 相關屬性
            if (property.IsDefined(typeof(System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute)) ||
                property.IsDefined(typeof(System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute)))
                return true;

            // 檢查是否為虛擬屬性 (EF 延遲載入標誌)
            var getMethod = property.GetGetMethod();
            if (getMethod?.IsVirtual == true && !getMethod.IsFinal)
            {
                // 檢查是否為實體類型或集合
                if (IsEntityType(propertyType) || IsEntityCollection(propertyType))
                    return true;
            }

            return false;
        }

        /// <summary> 檢查是否為實體類型 </summary>
        private static bool IsEntityType(Type type)
        {
            return typeof(ModelBaseEntity).IsAssignableFrom(type) ||
                   type.GetProperties().Any(p => p.IsDefined(typeof(System.ComponentModel.DataAnnotations.KeyAttribute)));
        }

        /// <summary> 檢查是否為實體集合 </summary>
        private static bool IsEntityCollection(Type type)
        {
            if (!type.IsGenericType) return false;

            var genericType = type.GetGenericArguments().FirstOrDefault();
            return genericType != null && IsEntityType(genericType);
        }

        /// <summary> 處理屬性值 </summary>
        private static object? ProcessPropertyValue(object? value)
        {
            if (value == null) return null;

            var valueType = value.GetType();

            // 處理列舉類型
            if (valueType.IsEnum)
                return value.ToString();

            // 處理 Guid
            if (valueType == typeof(Guid))
                return value.ToString();

            // 處理日期時間
            if (valueType == typeof(DateTime) || valueType == typeof(DateTimeOffset))
                return value.ToString();

            return value;
        }

        /// <summary> 提取實體 ID </summary>
        private static string ExtractEntityId(object entity)
        {
            try
            {
                var idProperty = entity.GetType().GetProperties()
                    .FirstOrDefault(p => p.Name.EndsWith("ID") || p.Name.EndsWith("Id") || 
                                        p.IsDefined(typeof(System.ComponentModel.DataAnnotations.KeyAttribute)));

                if (idProperty != null)
                {
                    var value = idProperty.GetValue(entity);
                    return value?.ToString() ?? "Unknown";
                }
            }
            catch
            {
                // 忽略錯誤，返回預設值
            }

            return "Unknown";
        }

        /// <summary> 提取使用者 ID </summary>
        private static string? ExtractUserId(object entity)
        {
            try
            {
                var userIdProperty = entity.GetType().GetProperty("UpdateUserId") ?? 
                                   entity.GetType().GetProperty("CreateUserId");
                return userIdProperty?.GetValue(entity)?.ToString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary> 提取原始屬性值 </summary>
        private static Dictionary<string, object?> ExtractOriginalProperties(EntityEntry entry)
        {
            var result = new Dictionary<string, object?>();

            foreach (var property in entry.OriginalValues.Properties)
            {
                try
                {
                    var value = entry.OriginalValues[property];
                    result[property.Name] = ProcessPropertyValue(value);
                }
                catch
                {
                    result[property.Name] = "[無法讀取原始值]";
                }
            }

            return result;
        }

        /// <summary> 取得變更的屬性清單 </summary>
        private static List<string> GetChangedProperties(EntityEntry entry)
        {
            return entry.Properties
                .Where(p => p.IsModified)
                .Select(p => p.Metadata.Name)
                .ToList();
        }

        #endregion
    }
}
