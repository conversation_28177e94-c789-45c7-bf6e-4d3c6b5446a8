"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { Table, Card, Button, Space, Typography, Descriptions, Popconfirm, Tag } from 'antd';
import type { TableProps, ColumnsType, ColumnType } from 'antd/es/table';

/**
 * 響應式表格組件
 * 
 * 自動在移動端切換為卡片列表模式，在桌面端使用標準表格模式。
 * 提供統一的分頁處理和響應式行為，簡化新頁面開發。
 * 
 * 特性：
 * - 自動檢測移動端 (≤768px) 並切換為卡片模式
 * - 統一的分頁邏輯，支援移動端和桌面端
 * - 可自定義移動端卡片渲染
 * - 保持所有 Antd Table 的原有功能
 * - 支援 TypeScript 泛型
 * 
 * @example
 * ```tsx
 * <ResponsiveTable
 *   columns={columns}
 *   dataSource={data}
 *   rowKey="id"
 *   mobileCardRender={(record, actions) => (
 *     <div>
 *       <h3>{record.name}</h3>
 *       <p>{record.description}</p>
 *       {actions}
 *     </div>
 *   )}
 * />
 * ```
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

export interface MobileCardRenderProps<T = any> {
  /** 當前記錄數據 */
  record: T;
  /** 記錄索引 */
  index: number;
  /** 操作按鈕組件 */
  actions?: React.ReactNode;
}

export interface ResponsiveTableProps<T = any> extends Omit<TableProps<T>, 'pagination'> {
  /** 移動端卡片渲染函數 */
  mobileCardRender?: (props: MobileCardRenderProps<T>) => React.ReactNode;
  /** 移動端每頁顯示數量 */
  mobilePageSize?: number;
  /** 是否在移動端顯示分頁 */
  showMobilePagination?: boolean;
  /** 移動端斷點 */
  mobileBreakpoint?: number;
  /** 分頁配置 */
  pagination?: TableProps<T>['pagination'] | false;
  /** 移動端卡片額外樣式 */
  mobileCardStyle?: React.CSSProperties;
  /** 移動端卡片額外類名 */
  mobileCardClassName?: string;
}

/**
 * 默認移動端卡片渲染函數
 * 自動根據列配置生成卡片內容
 */
const defaultMobileCardRender = <T extends Record<string, any>>(
  columns: ColumnsType<T>,
  { record, index, actions }: MobileCardRenderProps<T>
): React.ReactNode => {
  // 過濾掉操作列和不適合移動端顯示的列
  const displayColumns = columns.filter(col =>
    col.key !== 'action' &&
    col.key !== 'actions' &&
    !col.fixed &&
    'dataIndex' in col &&
    col.dataIndex
  );

  return (
    <div>
      <div style={{ marginBottom: 12 }}>
        {/* 主要標題 - 使用第一個非操作列 */}
        {displayColumns[0] && (
          <Typography.Text strong style={{ fontSize: '16px', display: 'block' }}>
            {(() => {
              const col = displayColumns[0] as ColumnType<T>;
              if (typeof col.render === 'function') {
                return col.render(
                  record[col.dataIndex as string],
                  record,
                  index
                );
              }
              return record[col.dataIndex as string] || '-';
            })()}
          </Typography.Text>
        )}
      </div>

      {/* 詳細信息 */}
      <Descriptions size="small" column={1} colon={false}>
        {displayColumns.slice(1).map((col, colIndex) => {
          const column = col as ColumnType<T>;
          const value = record[column.dataIndex as string];
          const renderedValue = typeof column.render === 'function'
            ? column.render(value, record, index)
            : value;

          return (
            <Descriptions.Item
              key={column.key || column.dataIndex as string || colIndex}
              label={column.title as string}
            >
              {renderedValue || '-'}
            </Descriptions.Item>
          );
        })}
      </Descriptions>
    </div>
  );
};

/**
 * 提取操作列的渲染內容
 */
const extractActions = <T extends Record<string, any>>(
  columns: ColumnsType<T>,
  record: T,
  index: number
): React.ReactNode => {
  const actionColumn = columns.find(col =>
    col.key === 'action' ||
    col.key === 'actions' ||
    ('dataIndex' in col && (col.dataIndex === 'action' || col.dataIndex === 'actions'))
  ) as ColumnType<T> | undefined;

  if (!actionColumn || !actionColumn.render) {
    return null;
  }

  return actionColumn.render(record, record, index) as React.ReactNode;
};

const ResponsiveTable = <T extends Record<string, any>>({
  columns = [],
  dataSource = [],
  mobileCardRender,
  mobilePageSize = 10,
  showMobilePagination = true,
  mobileBreakpoint = 768,
  pagination,
  mobileCardStyle,
  mobileCardClassName = '',
  rowKey = 'key',
  ...tableProps
}: ResponsiveTableProps<T>) => {
  const [isMobile, setIsMobile] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= mobileBreakpoint);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, [mobileBreakpoint]);

  // 分頁數據計算
  const paginatedData = useMemo(() => {
    if (!isMobile || pagination === false) {
      return dataSource;
    }

    const start = (currentPage - 1) * mobilePageSize;
    const end = start + mobilePageSize;
    return dataSource.slice(start, end);
  }, [dataSource, currentPage, mobilePageSize, isMobile, pagination]);

  // 移動端列表渲染
  const renderMobileList = () => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        {paginatedData.map((record, index) => {
          const actualIndex = (currentPage - 1) * mobilePageSize + index;
          const actions = extractActions(columns, record, actualIndex);
          const key = typeof rowKey === 'function' ? rowKey(record) : record[rowKey];

          const cardContent = mobileCardRender 
            ? mobileCardRender({ record, index: actualIndex, actions })
            : defaultMobileCardRender(columns, { record, index: actualIndex, actions });

          return (
            <Card
              key={key}
              size="small"
              className={`mobile-item ${mobileCardClassName}`}
              style={{
                marginBottom: 0,
                ...mobileCardStyle
              }}
              actions={actions ? [actions] : undefined}
            >
              {cardContent}
            </Card>
          );
        })}

        {/* 移動端分頁 */}
        {showMobilePagination && dataSource.length > mobilePageSize && (
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Space>
              <Button
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
                size="small"
              >
                上一頁
              </Button>
              <span style={{ fontSize: '14px', color: '#666' }}>
                {currentPage} / {Math.ceil(dataSource.length / mobilePageSize)}
              </span>
              <Button
                disabled={currentPage >= Math.ceil(dataSource.length / mobilePageSize)}
                onClick={() => setCurrentPage(currentPage + 1)}
                size="small"
              >
                下一頁
              </Button>
            </Space>
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              共 {dataSource.length} 項，每頁 {mobilePageSize} 項
            </div>
          </div>
        )}
      </div>
    );
  };

  // 桌面端表格渲染
  const renderDesktopTable = () => {
    const finalPagination = pagination === false ? false : {
      current: currentPage,
      pageSize: pageSize,
      total: dataSource.length,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: [number, number]) => {
        const isFiltered = dataSource.length !== (tableProps.loading ? 0 : dataSource.length);
        return isFiltered
          ? `第 ${range[0]}-${range[1]} 項，共 ${total} 項 (已篩選)`
          : `第 ${range[0]}-${range[1]} 項，共 ${total} 項`;
      },
      pageSizeOptions: ['10', '20', '50', '100'],
      onChange: (page: number, size: number) => {
        setCurrentPage(page);
        if (size !== pageSize) {
          setPageSize(size);
          setCurrentPage(1);
        }
      },
      onShowSizeChange: (_: number, size: number) => {
        setPageSize(size);
        setCurrentPage(1);
      },
      ...pagination
    };

    return (
      <Table
        {...tableProps}
        columns={columns}
        dataSource={dataSource}
        rowKey={rowKey}
        pagination={finalPagination}
        scroll={{ x: 'max-content', ...tableProps.scroll }}
        size="middle"
        rowClassName={(_, index) =>
          index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
        }
      />
    );
  };

  return isMobile ? renderMobileList() : renderDesktopTable();
};

export default ResponsiveTable;
