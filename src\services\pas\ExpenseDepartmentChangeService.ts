import { httpClient } from '@/services/http';

// 開支部門異動資料類型定義
export interface ExpenseDepartmentChange {
    uid?: string;
    userId?: string;
    expenseDepartmentId: string;
    expenseDepartmentName?: string;
    changeDate: string;
    effectiveDate: string;
    changeReason: string;
    remark: string;
    updateTime?: number;
}

// API回應類型
interface ApiResponse<T> {
    success: boolean;
    data?: T;
    message?: string;
}

// 取得開支部門異動列表
export const getExpenseDepartmentChangeList = async (userId: string): Promise<ApiResponse<ExpenseDepartmentChange[]>> => {
    try {
        const response = await httpClient<ExpenseDepartmentChange[]>(`/ExpenseDepartmentChange/GetAll/${userId}`);
        return {
            success: response.success || true,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '載入開支部門異動資料失敗'
        };
    }
};

// 取得開支部門異動明細
export const getExpenseDepartmentChangeDetail = async (uid: string): Promise<ApiResponse<ExpenseDepartmentChange>> => {
    try {
        const response = await httpClient<ExpenseDepartmentChange>(`/ExpenseDepartmentChange/Get/${uid}`);
        return {
            success: response.success || true,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '載入開支部門異動明細失敗'
        };
    }
};

// 新增開支部門異動
export const addExpenseDepartmentChange = async (formData: FormData): Promise<ApiResponse<any>> => {
    try {
        const response = await httpClient<any>('/ExpenseDepartmentChange/Add', {
            method: 'POST',
            body: formData
        });
        return {
            success: response.success,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '新增開支部門異動失敗'
        };
    }
};

// 編輯開支部門異動
export const editExpenseDepartmentChange = async (formData: FormData): Promise<ApiResponse<any>> => {
    try {
        const response = await httpClient<any>('/ExpenseDepartmentChange/Edit', {
            method: 'POST',
            body: formData
        });
        return {
            success: response.success,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '編輯開支部門異動失敗'
        };
    }
};

// 刪除開支部門異動
export const deleteExpenseDepartmentChange = async (uid: string): Promise<ApiResponse<any>> => {
    try {
        const response = await httpClient<any>('/ExpenseDepartmentChange/Delete', {
            method: 'POST',
            body: JSON.stringify(uid)
        });
        return {
            success: response.success,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '刪除開支部門異動失敗'
        };
    }
};

// 建立空的開支部門異動資料
export const createEmptyExpenseDepartmentChange = (userId: string): ExpenseDepartmentChange => {
    return {
        uid: '',
        userId: userId,
        expenseDepartmentId: '',
        expenseDepartmentName: '',
        changeDate: '',
        effectiveDate: '',
        changeReason: '',
        remark: ''
    };
};

// 將開支部門異動資料轉換為FormData
export const createExpenseDepartmentChangeFormData = (data: ExpenseDepartmentChange): FormData => {
    const formData = new FormData();

    formData.append('uid', data.uid || '');
    formData.append('userId', data.userId || '');
    formData.append('expenseDepartmentId', data.expenseDepartmentId);
    formData.append('changeDate', data.changeDate);
    formData.append('effectiveDate', data.effectiveDate);
    formData.append('changeReason', data.changeReason);
    formData.append('remark', data.remark);

    return formData;
}; 