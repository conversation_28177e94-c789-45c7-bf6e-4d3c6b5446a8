"use client";

import React, { useState, useCallback } from 'react';
import { Card, Input, Button, Tag, Space, Select, TreeSelect } from 'antd';
import { FilterOutlined, SearchOutlined, ClearOutlined, CheckCircleOutlined, ApartmentOutlined } from '@ant-design/icons';
import AdvancedFilterComponent from './AdvancedFilterComponent';
import { FilterOption, FilterStateChangeEvent as FilterChangeEvent, FilterUtils } from '@/app/ims/types/filter';

// TypeScript 介面定義
export interface FilterSearchPanelProps {
  /** 篩選選項配置 */
  filterOptions: FilterOption[];
  /** 搜尋文字變更回調 */
  onSearchChange?: (searchText: string) => void;
  /** 篩選條件變更回調 */
  onFilterChange?: (event: FilterChangeEvent) => void;
  /** 清除所有篩選回調 */
  onClearAll?: () => void;
  /** 搜尋框佔位符 */
  searchPlaceholder?: string;
  /** 標題 */
  title?: string;
  /** 是否顯示統計資訊 */
  showStats?: boolean;
  /** 統計資訊 */
  stats?: {
    total: number;
    filtered: number;
  };
  /** 初始搜尋文字 */
  initialSearchText?: string;
  /** 初始篩選條件 */
  initialFilters?: {
    activeFilters: string[];
    filterValues: Record<string, any>;
  };
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否緊湊模式 */
  compact?: boolean;
  /** 自定義樣式 */
  className?: string;
}

/**
 * 篩選搜尋面板 - 整合篩選和搜尋功能的高層次組件
 * 
 * 提供統一的篩選和搜尋介面，包含：
 * - 進階篩選功能
 * - 文字搜尋
 * - 統一的清除功能
 * - 篩選結果統計
 * - 響應式設計
 * 
 * @example
 * ```tsx
 * const filterOptions = [
 *   { label: "庫存品名稱", value: "name", type: "input" },
 *   { label: "庫存品狀態", value: "status", children: [
 *     { label: "啟用", value: "active" },
 *     { label: "停用", value: "inactive" }
 *   ]}
 * ];
 * 
 * <FilterSearchPanel
 *   title="篩選與搜尋"
 *   filterOptions={filterOptions}
 *   searchPlaceholder="搜尋庫存品名稱、編號或條碼"
 *   onSearchChange={(text) => setSearchText(text)}
 *   onFilterChange={(event) => handleFilterChange(event)}
 *   onClearAll={() => handleClearAll()}
 *   showStats={true}
 *   stats={{ total: 1000, filtered: 50 }}
 * />
 * ```
 */
const FilterSearchPanel: React.FC<FilterSearchPanelProps> = ({
  filterOptions,
  onSearchChange,
  onFilterChange,
  onClearAll,
  searchPlaceholder = "搜尋...",
  title = "篩選與搜尋",
  showStats = true,
  stats,
  initialSearchText = '',
  initialFilters = { activeFilters: [], filterValues: {} },
  disabled = false,
  compact = false,
  className = ''
}) => {
  // 內部狀態
  const [searchText, setSearchText] = useState(initialSearchText);
  const [filterData, setFilterData] = useState(initialFilters);
  const [isMobile, setIsMobile] = useState(false);

  // 檢測移動端
  React.useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 同步外部狀態變更
  React.useEffect(() => {
    setSearchText(initialSearchText);
  }, [initialSearchText]);

  React.useEffect(() => {
    setFilterData(initialFilters);
  }, [initialFilters]);

  // 處理搜尋文字變更
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    onSearchChange?.(value);
  }, [onSearchChange]);

  // 處理篩選變更
  const handleFilterChange = useCallback((event: FilterChangeEvent) => {
    setFilterData({
      activeFilters: event.activeFilters,
      filterValues: event.filterValues
    });
    onFilterChange?.(event);
  }, [onFilterChange]);

  // 處理清除所有
  const handleClearAll = useCallback(() => {
    const emptyState = { activeFilters: [], filterValues: {} };
    setSearchText('');
    setFilterData(emptyState);

    // 先調用外部清除函數，讓父組件清除其他狀態
    onClearAll?.();

    // 然後觸發篩選變更事件，確保所有相關組件都收到清除信號
    onFilterChange?.({
      filterKey: '',
      value: undefined,
      activeFilters: [],
      filterValues: {},
      searchText: '',
      hasActiveFilters: false,
      filterCount: 0
    });
  }, [onClearAll, onFilterChange]);

  // 檢查是否有活躍的篩選條件
  const hasActiveFilters = filterData.activeFilters.length > 0 || searchText.trim().length > 0;

  return (
    <Card
      className={`filter-search-panel ${className}`}
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FilterOutlined style={{ color: '#1890ff' }} />
          <span>{title}</span>
        </div>
      }
      size={compact ? "small" : "default"}
      styles={{
        header: {
          padding: isMobile ? '12px 16px' : '16px 24px',
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: '#fafafa'
        },
        body: {
          padding: isMobile ? '12px 16px' : '16px 24px'
        }
      }}
      style={{
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
        border: '1px solid #e8e8e8'
      }}
    >
      {/* 頂部控制區域：篩選按鈕和搜尋框 */}
      <div style={{
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        gap: '16px',
        alignItems: isMobile ? 'stretch' : 'center',
        justifyContent: 'space-between',
        marginBottom: hasActiveFilters ? '16px' : '0'
      }}>
        {/* 篩選按鈕區域 */}
        <div style={{ flex: isMobile ? 'none' : '0 0 auto' }}>
          <AdvancedFilterComponent
            key={`filter-${filterData.activeFilters.length}-${Object.keys(filterData.filterValues).length}`}
            filterOptions={filterOptions}
            onFilterChange={handleFilterChange}
            onClear={handleClearAll}
            initialActiveFilters={filterData.activeFilters}
            initialFilterValues={filterData.filterValues}
            showClearButton={false}
            compact={compact || isMobile}
            layout="horizontal"
            buttonOnly={true}
            disabled={disabled}
          />
        </div>

        {/* 搜尋框和清除按鈕 */}
        <div style={{
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          gap: '12px',
          alignItems: isMobile ? 'stretch' : 'center',
          flex: isMobile ? 'none' : '0 0 auto'
        }}>
          <Input
            placeholder={searchPlaceholder}
            prefix={<SearchOutlined />}
            allowClear
            onChange={handleSearchChange}
            value={searchText}
            disabled={disabled}
            style={{
              width: isMobile ? '100%' : 280,
              borderRadius: '6px',
              boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
            }}
          />

          {hasActiveFilters && (
            <Button
              icon={<ClearOutlined />}
              onClick={handleClearAll}
              danger
              type="dashed"
              disabled={disabled}
              style={{
                borderRadius: '6px',
                boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                whiteSpace: 'nowrap'
              }}
            >
              清除所有篩選
            </Button>
          )}
        </div>
      </div>

      {/* 活躍篩選條件顯示區域 - 充分利用整個寬度 */}
      {hasActiveFilters && (
        <div style={{
          marginTop: 16,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16
        }}>
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '12px',
            alignItems: 'flex-start'
          }}>
            {filterData.activeFilters.map((filterKey) => {
              const filterOption = filterOptions.find(opt => opt.value === filterKey);
              if (!filterOption) return null;

              const hasValue = filterData.filterValues[filterKey] !== undefined &&
                              filterData.filterValues[filterKey] !== null &&
                              filterData.filterValues[filterKey] !== '' &&
                              !(Array.isArray(filterData.filterValues[filterKey]) && filterData.filterValues[filterKey].length === 0);

              const width = filterOption.width || (filterOption.type === 'treeSelect' ? 250 : 200);

              const commonStyle = {
                width,
                borderColor: hasValue ? '#1890ff' : undefined,
                boxShadow: hasValue ? '0 0 0 2px rgba(24, 144, 255, 0.2)' : undefined
              };

              return (
                <div key={filterKey} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  marginBottom: compact ? '8px' : '12px',
                  flexWrap: 'wrap'
                }}>
                  <Tag
                    color="blue"
                    style={{
                      margin: 0,
                      padding: '2px 8px',
                      fontSize: '12px',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {filterOption.label}
                  </Tag>

                  {/* 渲染對應的篩選控制元件 */}
                  {filterOption.type === "input" ? (
                    <Input
                      style={commonStyle}
                      onChange={(e) => {
                        const newFilterValues = { ...filterData.filterValues, [filterKey]: e.target.value };
                        const newActiveFilters = e.target.value ?
                          (filterData.activeFilters.includes(filterKey) ? filterData.activeFilters : [...filterData.activeFilters, filterKey]) :
                          filterData.activeFilters.filter(f => f !== filterKey);

                        const newData = { activeFilters: newActiveFilters, filterValues: newFilterValues };
                        setFilterData(newData);

                        const newState = {
                          searchText,
                          activeFilters: newActiveFilters,
                          filterValues: newFilterValues
                        };
                        onFilterChange?.(FilterUtils.createFilterStateChangeEvent(
                          newState,
                          filterKey,
                          e.target.value
                        ));
                      }}
                      placeholder={filterOption.placeholder || `輸入${filterOption.label}`}
                      allowClear
                      value={filterData.filterValues[filterKey] || ''}
                      disabled={disabled}
                    />
                  ) : filterOption.type === "treeSelect" ? (
                    <TreeSelect
                      style={commonStyle}
                      multiple
                      treeData={filterOption.treeData}
                      onChange={(value) => {
                        const newFilterValues = { ...filterData.filterValues, [filterKey]: value };
                        const newActiveFilters = (value && value.length > 0) ?
                          (filterData.activeFilters.includes(filterKey) ? filterData.activeFilters : [...filterData.activeFilters, filterKey]) :
                          filterData.activeFilters.filter(f => f !== filterKey);

                        const newData = { activeFilters: newActiveFilters, filterValues: newFilterValues };
                        setFilterData(newData);

                        const newState = {
                          searchText,
                          activeFilters: newActiveFilters,
                          filterValues: newFilterValues
                        };
                        onFilterChange?.(FilterUtils.createFilterStateChangeEvent(
                          newState,
                          filterKey,
                          value
                        ));
                      }}
                      placeholder={filterOption.placeholder || `選擇${filterOption.label}（可多選）`}
                      allowClear
                      showSearch
                      treeDefaultExpandAll={true}
                      treeNodeFilterProp="title"
                      treeNodeLabelProp="title"
                      maxTagCount="responsive"
                      value={filterData.filterValues[filterKey] || []}
                      suffixIcon={<ApartmentOutlined />}
                      disabled={disabled}
                      filterTreeNode={(inputValue, treeNode) => {
                        const title = treeNode.title as string;
                        return title.toLowerCase().includes(inputValue.toLowerCase());
                      }}
                    />
                  ) : (
                    <Select
                      style={commonStyle}
                      mode="multiple"
                      options={filterOption.children}
                      onChange={(value) => {
                        const newFilterValues = { ...filterData.filterValues, [filterKey]: value };
                        const newActiveFilters = (value && value.length > 0) ?
                          (filterData.activeFilters.includes(filterKey) ? filterData.activeFilters : [...filterData.activeFilters, filterKey]) :
                          filterData.activeFilters.filter(f => f !== filterKey);

                        const newData = { activeFilters: newActiveFilters, filterValues: newFilterValues };
                        setFilterData(newData);

                        const newState = {
                          searchText,
                          activeFilters: newActiveFilters,
                          filterValues: newFilterValues
                        };
                        onFilterChange?.(FilterUtils.createFilterStateChangeEvent(
                          newState,
                          filterKey,
                          value
                        ));
                      }}
                      placeholder={filterOption.placeholder || `選擇${filterOption.label}（可多選）`}
                      allowClear
                      showSearch
                      maxTagCount="responsive"
                      value={filterData.filterValues[filterKey] || []}
                      optionFilterProp="label"
                      disabled={disabled}
                    />
                  )}

                  <Button
                    type="text"
                    size="small"
                    icon={<ClearOutlined />}
                    onClick={() => {
                      const newActiveFilters = filterData.activeFilters.filter(f => f !== filterKey);
                      const newFilterValues = { ...filterData.filterValues };
                      delete newFilterValues[filterKey];

                      const newData = { activeFilters: newActiveFilters, filterValues: newFilterValues };
                      setFilterData(newData);

                      const newState = {
                        searchText,
                        activeFilters: newActiveFilters,
                        filterValues: newFilterValues
                      };
                      onFilterChange?.(FilterUtils.createFilterStateChangeEvent(
                        newState,
                        filterKey,
                        undefined
                      ));
                    }}
                    style={{ color: '#ff4d4f', flexShrink: 0 }}
                    disabled={disabled}
                    title={`移除 ${filterOption.label} 篩選`}
                  />
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* 篩選結果統計 */}
      {showStats && hasActiveFilters && stats && (
        <div style={{
          marginTop: 12,
          padding: '8px 12px',
          backgroundColor: '#f6f8fa',
          borderRadius: '6px',
          border: '1px solid #e1e4e8'
        }}>
          <Space wrap>
            <Tag color="blue" icon={<CheckCircleOutlined />}>
              篩選結果: {stats.filtered.toLocaleString()} / {stats.total.toLocaleString()} 項
            </Tag>
            {filterData.activeFilters.length > 0 && (
              <Tag color="green" icon={<FilterOutlined />}>
                已套用 {filterData.activeFilters.length} 個篩選條件
              </Tag>
            )}
            {searchText && (
              <Tag color="orange" icon={<SearchOutlined />}>
                搜尋關鍵字: "{searchText}"
              </Tag>
            )}
          </Space>
        </div>
      )}
    </Card>
  );
};

export default FilterSearchPanel;
