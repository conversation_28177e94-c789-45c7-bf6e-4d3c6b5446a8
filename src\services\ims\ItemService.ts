import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";
import { devLoading, devSuccess, devWarn, devError, devDebug } from '@/utils/devLogger';

// 庫存品價格介面 - 根據實際API回應更新
export interface ItemPrice {
    itemPriceID: string;
    itemID: string;
    priceTypeID: string;
    price: number;
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

// 價格類型介面 - 根據實際API回應更新
export interface PriceType {
    priceTypeID: string;
    name: string;
    description: string;
    sortCode: number;
    allowStop: boolean;
    isStop: boolean;
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

// 庫存品分類介面
export interface ItemCategory {
    itemCategoryID: string;
    name: string;
    description: string;
    parentId: string | null;
    sortCode: number;
    parent: ItemCategory | null;
    children: ItemCategory[];
    items: Item[];
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

// 庫存品介面
export interface Item {
    itemID: string;
    customNO: string;
    name: string;
    internationalBarCode: string;
    unit: string;
    itemCategoryID: string | null;
    description: string;
    isStop: boolean;
    taxType: ItemTaxType;
    prices: ItemPrice[];
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

// 庫存品稅別列舉
export enum ItemTaxType {
    Taxable = 1,   // 應稅
    TaxFree = 2,   // 免稅
    ZeroRate = 3   // 零稅率
}

// 庫存品稅別介面
export interface ItemTaxTypeOption {
    code: number;
    name: string;
    description: string;
    taxRate: number;
}

export const createEmptyItem = (): Item => ({
    itemID: '',
    customNO: '',
    name: '',
    internationalBarCode: '',
    unit: '',
    itemCategoryID: null,
    description: '',
    isStop: false,
    taxType: 1, // ItemTaxType.Taxable
    prices: [],
    createTime: null,
    createUserId: null,
    updateTime: null,
    updateUserId: null,
    deleteTime: null,
    deleteUserId: null,
    isDeleted: false,
});



const validateSingleItem = (data: any): Item | null => {
    if (!data || typeof data !== 'object' || !data.itemID) {
        console.warn('⚠️ ItemService: 無效的庫存品資料:', data);
        return null;
    }

    // 確保所有 ID 欄位都是字串格式
    const validatedItem: Item = {
        ...data,
        itemID: String(data.itemID || ''),
        itemCategoryID: data.itemCategoryID ? String(data.itemCategoryID) : null,
        // 確保其他必要欄位的格式
        customNO: String(data.customNO || ''),
        name: String(data.name || ''),
        internationalBarCode: String(data.internationalBarCode || ''),
        unit: String(data.unit || ''),
        description: String(data.description || ''),
        isStop: Boolean(data.isStop),
        taxType: Number(data.taxType) || 1,
        prices: Array.isArray(data.prices) ? data.prices.map((price: any) => ({
            ...price,
            itemPriceID: String(price.itemPriceID || ''),
            itemID: String(price.itemID || ''),
            priceTypeID: String(price.priceTypeID || ''),
            price: Number(price.price) || 0
        })) : [],
        createTime: data.createTime || null,
        createUserId: data.createUserId ? String(data.createUserId) : null,
        updateTime: data.updateTime || null,
        updateUserId: data.updateUserId ? String(data.updateUserId) : null,
        deleteTime: data.deleteTime || null,
        deleteUserId: data.deleteUserId ? String(data.deleteUserId) : null,
        isDeleted: Boolean(data.isDeleted)
    };

    // 調試日誌 (僅開發環境)
    devDebug('validateSingleItem - 轉換後的庫存品資料:', {
        itemID: validatedItem.itemID,
        name: validatedItem.name,
        itemCategoryID: validatedItem.itemCategoryID,
        categoryType: typeof validatedItem.itemCategoryID
    });

    return validatedItem;
};

// 搜尋庫存品列表
export async function getItemList(): Promise<ApiResponse<Item[]>> {
    try {
        devLoading('ItemService: 開始載入庫存品列表...');
        const response = await httpClient(`${apiEndpoints.getItemList}`, {
            method: "GET",
        });

        devSuccess('ItemService: API回應完成', response);
        return response;
    } catch (error: any) {
        devError('ItemService: 載入庫存品列表時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "搜尋庫存品列表失敗",
            data: []
        };
    }
}

// 搜尋庫存品明細
export async function getItem(uid: string): Promise<ApiResponse<Item>> {
    try {
        if (!uid || typeof uid !== 'string') {
            return {
                success: false,
                message: "庫存品ID不能為空",
            };
        }

        console.log(`🔄 ItemService: 載入庫存品詳細資料 (ID: ${uid})...`);
        const response = await httpClient(`${apiEndpoints.getItem}/${uid}`, {
            method: "GET",
        });

        if (response.success && response.data) {
            // 後端回傳陣列格式，取第一個元素
            if (Array.isArray(response.data)) {
                if (response.data.length > 0) {
                    const item = validateSingleItem(response.data[0]);
                    if (item) {
                        console.log(`✅ ItemService: 成功載入庫存品 ${item.name}`);
                        return {
                            ...response,
                            data: item
                        };
                    }
                }
                return {
                    success: false,
                    message: "找不到指定的庫存品",
                };
            }

            // 如果不是陣列，直接驗證
            const item = validateSingleItem(response.data);
            if (item) {
                console.log(`✅ ItemService: 成功載入庫存品 ${item.name}`);
                return {
                    ...response,
                    data: item
                };
            }
        }

        console.warn('⚠️ ItemService: 載入庫存品失敗:', response.message);
        return {
            success: false,
            message: response.message || "載入庫存品詳細資料失敗",
        };
    } catch (error: any) {
        console.error('❌ ItemService: 載入庫存品詳細資料時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "搜尋庫存品失敗",
        };
    }
}

// 驗證庫存品資料
const validateItemData = (data: Partial<Item>): { isValid: boolean; message?: string } => {
    if (!data.name || data.name.trim() === '') {
        return { isValid: false, message: '庫存品名稱不能為空' };
    }
    if (!data.customNO || data.customNO.trim() === '') {
        return { isValid: false, message: '庫存品編號不能為空' };
    }
    if (!data.unit || data.unit.trim() === '') {
        return { isValid: false, message: '單位不能為空' };
    }
    return { isValid: true };
};

// 新增庫存品
export async function addItem(data: Partial<Item>): Promise<ApiResponse> {
    try {
        // 資料驗證
        const validation = validateItemData(data);
        if (!validation.isValid) {
            return {
                success: false,
                message: validation.message || "庫存品資料驗證失敗",
            };
        }

        devLoading('ItemService: 新增庫存品...', data.name);
        const response = await httpClient(apiEndpoints.addItem, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            devSuccess('ItemService: 庫存品新增成功');
        } else {
            devWarn('ItemService: 庫存品新增失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        devError('ItemService: 新增庫存品時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "新增庫存品失敗",
        };
    }
}

// 編輯庫存品
export async function editItem(data: Partial<Item>): Promise<ApiResponse> {
    try {
        // 檢查必要的ID
        if (!data.itemID) {
            return {
                success: false,
                message: "庫存品ID不能為空",
            };
        }

        // 資料驗證
        const validation = validateItemData(data);
        if (!validation.isValid) {
            return {
                success: false,
                message: validation.message || "庫存品資料驗證失敗",
            };
        }

        console.log('🔄 ItemService: 更新庫存品...', data.name);
        const response = await httpClient(apiEndpoints.editItem, {
            method: "PATCH",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ ItemService: 庫存品更新成功');
        } else {
            console.warn('⚠️ ItemService: 庫存品更新失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ ItemService: 更新庫存品時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "編輯庫存品失敗",
        };
    }
}

// 刪除庫存品
export async function deleteItem(itemId: string): Promise<ApiResponse> {
    try {
        if (!itemId || typeof itemId !== 'string') {
            return {
                success: false,
                message: "庫存品ID不能為空",
            };
        }

        console.log('🔄 ItemService: 刪除庫存品...', itemId);
        const response = await httpClient(apiEndpoints.deleteItem, {
            method: "DELETE",
            body: JSON.stringify({ itemID: itemId }),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ ItemService: 庫存品刪除成功');
        } else {
            console.warn('⚠️ ItemService: 庫存品刪除失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ ItemService: 刪除庫存品時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "刪除庫存品失敗",
        };
    }
}

// ==================== 測試資料產生功能 ====================

/**
 * 測試資料產生回應介面
 */
export interface TestDataGenerationResponse {
    createdCount: number;
    batchInfo: {
        totalBatches: number;
        batchSize: number;
        processingTime: number;
    };
}

/**
 * 進度回報回調函數類型
 */
export type ProgressCallback = (progress: {
    current: number;
    total: number;
    percentage: number;
    message: string;
}) => void;

/**
 * 產生隨機庫存品編號
 * @param existingNumbers 現有的庫存品編號集合，用於避免重複
 * @returns 唯一的庫存品編號
 */
const generateUniqueItemNumber = (existingNumbers: Set<string>): string => {
    let attempts = 0;
    const maxAttempts = 1000;

    while (attempts < maxAttempts) {
        const randomNumber = Math.floor(Math.random() * 999999).toString().padStart(6, '0');
        const itemNumber = `ITEM-${randomNumber}`;

        if (!existingNumbers.has(itemNumber)) {
            existingNumbers.add(itemNumber);
            return itemNumber;
        }
        attempts++;
    }

    // 如果隨機產生失敗，使用時間戳記確保唯一性
    const timestamp = Date.now().toString().slice(-6);
    const fallbackNumber = `ITEM-${timestamp}`;
    existingNumbers.add(fallbackNumber);
    return fallbackNumber;
};

/**
 * 產生符合 EAN-13 格式的條碼
 * @returns 13位數的 EAN-13 條碼
 */
const generateEAN13Barcode = (): string => {
    // 產生前12位數字
    let barcode = '';
    for (let i = 0; i < 12; i++) {
        barcode += Math.floor(Math.random() * 10).toString();
    }

    // 計算校驗位
    let sum = 0;
    for (let i = 0; i < 12; i++) {
        const digit = parseInt(barcode[i]);
        sum += (i % 2 === 0) ? digit : digit * 3;
    }

    const checkDigit = (10 - (sum % 10)) % 10;
    return barcode + checkDigit.toString();
};

/**
 * 產生隨機庫存品資料
 * @param categoryIds 可用的分類ID陣列
 * @param existingNumbers 現有庫存品編號集合
 * @returns 隨機庫存品資料
 */
const generateRandomItemData = (
    categoryIds: string[],
    existingNumbers: Set<string>
): Partial<Item> => {
    // 庫存品名稱模板
    const nameTemplates = [
        '電子產品', '日用品', '食品', '服飾', '家具', '文具', '運動用品',
        '美妝保養', '書籍', '玩具', '廚具', '清潔用品', '3C配件', '健康食品',
        '辦公用品', '園藝用品', '汽車用品', '寵物用品', '嬰幼兒用品', '工具'
    ];

    // 單位選項
    const units = ['個', '公斤', '公升', '盒', '包', '組', '套', '張', '支', '瓶'];

    // 隨機選擇模板和編號
    const template = nameTemplates[Math.floor(Math.random() * nameTemplates.length)];
    const randomId = Math.floor(Math.random() * 9999) + 1;
    const name = `${template}-${randomId.toString().padStart(4, '0')}`;

    // 產生唯一庫存品編號
    const customNO = generateUniqueItemNumber(existingNumbers);

    // 隨機選擇分類（如果有的話）
    const itemCategoryID = categoryIds.length > 0
        ? categoryIds[Math.floor(Math.random() * categoryIds.length)]
        : null;

    // 隨機選擇單位
    const unit = units[Math.floor(Math.random() * units.length)];

    // 70% 機率為啟用狀態
    const isStop = Math.random() > 0.7;

    // 30% 機率有描述
    const description = Math.random() < 0.3
        ? `測試庫存品 - 自動產生於 ${new Date().toLocaleString('zh-TW')}`
        : '';

    // 產生國際條碼
    const internationalBarCode = generateEAN13Barcode();

    return {
        name,
        customNO,
        internationalBarCode,
        unit,
        itemCategoryID,
        description,
        isStop,
        prices: [] // 價格資料將在後續處理
    };
};

/**
 * 呼叫後端測試資料產生 API（帶超時處理）
 * @param count 要產生的庫存品數量
 * @param timeoutMs 超時時間（毫秒），預設 120 秒
 * @returns API 回應
 */
const callBackendGenerateTestData = async (
    count: number,
    timeoutMs: number = 120000
): Promise<ApiResponse<TestDataGenerationResponse>> => {
    try {
        console.log(`🔄 ItemService: 呼叫後端產生 ${count} 筆測試資料...`);

        // 創建超時 Promise
        const timeoutPromise = new Promise<never>((_, reject) => {
            setTimeout(() => {
                reject(new Error(`請求超時：後端處理 ${count} 筆資料超過 ${timeoutMs / 1000} 秒`));
            }, timeoutMs);
        });

        // 創建 API 請求 Promise
        const apiPromise = httpClient(apiEndpoints.generateTestItems, {
            method: 'POST',
            body: JSON.stringify({ count: count })
        });

        // 使用 Promise.race 實現超時控制
        const response = await Promise.race([apiPromise, timeoutPromise]);

        devSuccess('ItemService: 後端測試資料產生成功', response);
        return response;

    } catch (error: any) {
        devError('ItemService: 後端測試資料產生失敗:', error);

        // 判斷是否為超時錯誤
        const isTimeout = error.message?.includes('請求超時');

        return {
            success: false,
            message: isTimeout
                ? `${error.message}\n\n💡 提示：大量資料產生需要較長時間，後端可能仍在處理中。請稍後手動重新載入頁面查看結果。`
                : error.message || "後端測試資料產生失敗",
            data: {
                createdCount: 0,
                batchInfo: {
                    totalBatches: 0,
                    batchSize: 0,
                    processingTime: 0
                }
            }
        };
    }
};

/**
 * 取得庫存品稅別選項
 * @returns 庫存品稅別選項列表
 */
export async function getItemTaxTypes(): Promise<ApiResponse<ItemTaxTypeOption[]>> {
    try {
        const response = await httpClient(apiEndpoints.getItemTaxTypes, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        console.error('取得庫存品稅別選項失敗:', error);
        return {
            success: false,
            message: error.response?.data?.message || error.message || "取得庫存品稅別選項失敗",
        };
    }
};

/**
 * 產生大量測試庫存品資料
 * @param count 要產生的庫存品數量，預設 20,000 筆
 * @returns 簡單的成功訊息
 */
export async function generateTestItems( count: number = 20000 ): Promise<ApiResponse<null>>
{
    try {
        // 環境檢查
        if (process.env.NODE_ENV === 'production') {
            return {
                success: false,
                message: "測試資料產生功能僅在開發環境中可用",
            };
        }

        // 參數驗證
        if (count <= 0 || count > 50000) {
            return {
                success: false,
                message: "庫存品數量必須在 1 到 50,000 之間",
            };
        }

        // 發送 API 請求但不等待回應
        httpClient(apiEndpoints.generateTestItems, {
            method: "POST",
            body: JSON.stringify({ count })
        }).catch(error => {
            console.warn('測試資料產生 API 呼叫:', error);
        });

        // 立即回傳成功訊息
        return {
            success: true,
            message: `測試資料產生已開始，預計產生 ${count.toLocaleString()} 筆庫存品。請等待 2-3 分鐘後重新載入頁面查看結果。`,
        };

    } catch (error: any) {
        console.error('產生測試資料失敗:', error);
        return {
            success: false,
            message: "啟動測試資料產生失敗，請稍後再試",
        };
    }
}