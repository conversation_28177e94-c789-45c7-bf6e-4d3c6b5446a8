using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 庫存品稅別列舉 </summary>
public enum ItemTaxType
{
    /// <summary> 應稅 (5% 營業稅) </summary>
    [Display(Name = "應稅")]
    [Description("應稅 (5% 營業稅)")]
    Taxable = 1,
    
    /// <summary> 免稅 (免徵營業稅) </summary>
    [Display(Name = "免稅")]
    [Description("免稅 (免徵營業稅，如金融服務、未加工農產品)")]
    TaxFree = 2,
    
    /// <summary> 零稅率 (0% 營業稅) </summary>
    [Display(Name = "零稅率")]
    [Description("零稅率 (0% 營業稅，如外銷庫存品)")]
    ZeroRate = 3
}

/// <summary> 庫存品稅別擴展方法 </summary>
public static class ItemTaxTypeExtensions
{
    /// <summary> 取得稅別顯示名稱 </summary>
    public static string GetDisplayName(this ItemTaxType taxType)
    {
        var field = taxType.GetType().GetField(taxType.ToString());
        var attribute = field?.GetCustomAttributes(typeof(DisplayAttribute), false)
                             .FirstOrDefault() as DisplayAttribute;
        return attribute?.Name ?? taxType.ToString();
    }
    
    /// <summary> 取得稅別說明 </summary>
    public static string GetDescription(this ItemTaxType taxType)
    {
        var field = taxType.GetType().GetField(taxType.ToString());
        var attribute = field?.GetCustomAttributes(typeof(DescriptionAttribute), false)
                             .FirstOrDefault() as DescriptionAttribute;
        return attribute?.Description ?? taxType.ToString();
    }
    
    /// <summary> 取得稅率 </summary>
    public static decimal GetTaxRate(this ItemTaxType taxType)
    {
        return taxType switch
        {
            ItemTaxType.Taxable => 0.05m,   // 5%
            ItemTaxType.TaxFree => 0.00m,   // 0%
            ItemTaxType.ZeroRate => 0.00m,  // 0%
            _ => 0.00m
        };
    }
    
    /// <summary> 取得所有稅別選項 </summary>
    public static IEnumerable<ItemTaxType> GetAllTypes()
    {
        return Enum.GetValues<ItemTaxType>();
    }
}

/// <summary> 庫存品稅別 DTO </summary>
public class ItemTaxTypeDTO
{
    /// <summary> 稅別代碼 </summary>
    public int Code { get; set; }
    
    /// <summary> 稅別名稱 </summary>
    public string Name { get; set; }
    
    /// <summary> 稅別說明 </summary>
    public string Description { get; set; }
    
    /// <summary> 稅率 </summary>
    public decimal TaxRate { get; set; }
    
    /// <summary> 建構式 </summary>
    public ItemTaxTypeDTO()
    {
        Code = 0;
        Name = string.Empty;
        Description = string.Empty;
        TaxRate = 0.00m;
    }
    
    /// <summary> 建構式 </summary>
    public ItemTaxTypeDTO(ItemTaxType taxType)
    {
        Code = (int)taxType;
        Name = taxType.GetDisplayName();
        Description = taxType.GetDescription();
        TaxRate = taxType.GetTaxRate();
    }
}
