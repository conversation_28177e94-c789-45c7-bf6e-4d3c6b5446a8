"use client";

//** 個人資料
//app/profile/page.tsx
//**

import { useEffect, useState } from "react";
import {
  Card,
  Form,
  Input,
  Button,
  Spin,
  Avatar,
  Modal,
  Space,
  AutoComplete,
} from "antd";
import { useAuth } from "@/contexts/AuthContext";
import { updateUser } from "@/services/common/userService";
import { notifySuccess, notifyError } from "@/utils/notification";
import { AntDesignOutlined, ExclamationCircleFilled } from "@ant-design/icons";
import { MaskedInput } from "antd-mask-input";
import { siteConfig } from "@/config/site";

export default function ProfilePage() {
  const { user, getMyInfo } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [modal, contextHolder] = Modal.useModal();
  const [emailOptions, setEmailOptions] = useState<
    { label: string; value: string }[]
  >([]);

  // 處理表單初始值
  useEffect(() => {
    if (user) {
      const telNo = user.telNo?.replace("string", "") || "";
      const [areaCode, number] = telNo.split("-");

      form.setFieldsValue({
        name: user.name?.replace("string", ""),
        eMail: user.eMail?.replace("string", ""),
        phone: user.phone?.replace("string", ""),
        telNo: {
          areaCode: areaCode || "",
          number: number || "",
        },
      });
    }
  }, [user, form]);

  // 姓名驗證規則
  const validateName = (_: any, value: string) => {
    if (!value) {
      return Promise.reject("請輸入姓名");
    }
    if (value.length > 50) {
      return Promise.reject("姓名不能超過50個字元");
    }
    return Promise.resolve();
  };

  // 手機號碼驗證規則
  const validateMobile = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    const mobileRegex = /^[0-9]{10}$/;
    if (!mobileRegex.test(value)) {
      return Promise.reject("請輸入手機號碼");
    }
    return Promise.resolve();
  };

  // 處理電子郵件自動完成
  const handleEmailSearch = (value: string) => {
    if (!value || value.includes("@")) {
      setEmailOptions([]);
      return;
    }
    const options = siteConfig.email.domains.map((domain) => ({
      label: `${value}@${domain}`,
      value: `${value}@${domain}`,
    }));
    setEmailOptions(options);
  };

  const handleUpdate = async (values: any) => {
    if (!user?.userId) {
      notifyError("更新失敗", "無法取得使用者編號");
      return;
    }

    setLoading(true);

    // 合併區碼和電話號碼
    const areaCode = values.telNo?.areaCode || "";
    const phoneNumber = values.telNo?.number || "";
    const fullPhoneNumber =
      areaCode && phoneNumber ? `${areaCode}-${phoneNumber}` : "";

    try {
      const result = await updateUser({
        ...user,
        ...values,
        telNo: fullPhoneNumber,
      });

      if (result.success) {
        // 重新獲取用戶資訊以更新狀態
        await getMyInfo();
        notifySuccess("更新成功", "個人資料已更新");
      } else {
        notifyError("更新失敗", result.message || "請稍後再試");
      }
    } catch (error) {
      notifyError("更新錯誤", "發生未預期的錯誤");
    } finally {
      setLoading(false);
    }
  };

  const onFinish = (values: any) => {
    modal.confirm({
      title: "確認儲存",
      icon: <ExclamationCircleFilled />,
      content: "確定要儲存個人資料嗎？",
      okText: "確定",
      cancelText: "取消",
      onOk: () => handleUpdate(values),
    });
  };

  if (!user) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Card title="個人資料" bordered={false}>
      {contextHolder}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          marginBottom: 24,
        }}
      >
        <Avatar
          size={{ xs: 24, sm: 32, md: 40, lg: 64, xl: 80, xxl: 100 }}
          icon={<AntDesignOutlined />}
          style={{ marginBottom: 16 }}
        />
        <div style={{ fontSize: 18, fontWeight: 500 }}>{user.name}</div>
        <div style={{ color: "#666" }}>{user.account}</div>
      </div>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        style={{ maxWidth: 600, margin: "0 auto" }}
      >
        <Form.Item
          label="姓名"
          name="name"
          rules={[{ validator: validateName }]}
        >
          <Input maxLength={50} showCount />
        </Form.Item>

        <Form.Item
          label="電子郵件"
          name="eMail"
          rules={[{ type: "email", message: "請輸入有效的電子郵件" }]}
        >
          <AutoComplete
            options={emailOptions}
            onSearch={handleEmailSearch}
            placeholder="請輸入電子郵件"
            style={{ width: "100%" }}
          />
        </Form.Item>

        <Form.Item
          label="手機"
          name="phone"
          rules={[{ validator: validateMobile }]}
        >
          <Input maxLength={10} showCount />
        </Form.Item>

        <Form.Item label="電話">
          <Space.Compact>
            <Form.Item name={["telNo", "areaCode"]} noStyle>
              <Input
                style={{ width: "20%" }}
                placeholder="區碼"
                maxLength={3}
                onKeyDown={(e) => {
                  if (
                    !/\d/.test(e.key) &&
                    e.key !== "Backspace" &&
                    e.key !== "Delete" &&
                    e.key !== "Tab"
                  ) {
                    e.preventDefault();
                  }
                }}
              />
            </Form.Item>
            <Form.Item name={["telNo", "number"]} noStyle>
              <Input
                style={{ width: "80%" }}
                placeholder="電話號碼"
                maxLength={8}
                onKeyDown={(e) => {
                  if (
                    !/\d/.test(e.key) &&
                    e.key !== "Backspace" &&
                    e.key !== "Delete" &&
                    e.key !== "Tab"
                  ) {
                    e.preventDefault();
                  }
                }}
              />
            </Form.Item>
          </Space.Compact>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            儲存資料
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
}
