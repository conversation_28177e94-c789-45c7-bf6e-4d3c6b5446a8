import React, { useEffect, useState } from "react";
import {
    Table,
    Button,
    Modal,
    Form,
    Input,
    DatePicker,
    Popconfirm,
    Card,
    message,
    Row,
    Col,
    Typography,
    Space,
    Divider
} from "antd";
import dayjs from "dayjs";
import type { ColumnsType } from "antd/es/table";

import {
    getHensureList,
    addHensure,
    editHensure,
    deleteHensure,
    Hensure,
    createEmptyHensure,
} from "@/services/pas/HensureService";
import { getDepTypeOptions } from '@/services/pas/OptionParameterService';
import ApiSelect from '@/app/pas/components/ApiSelect';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    IdcardOutlined,
    CalendarOutlined,
    FileTextOutlined,
    UserOutlined,
    TeamOutlined,
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    PlusOutlined,
    MedicineBoxOutlined,
    ScheduleOutlined
} from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;

type HensureGroup = {
    userId: string;
    dependentRocId: string;
    dependentName: string;
    dependentRelationType: string;
    dependentRelationTypeName: string;
    periods: Hensure[];
};

type HensureInfoProps = {
    userId: string;
    active: boolean;
    tabUpdateidx?: number;
};

function groupByDependent(data: Hensure[]): HensureGroup[] {
    const groups: Record<string, Hensure[]> = {};
    data.forEach((item) => {
        const key = `${item.userId}_${item.dependentRocId}_${item.dependentName}`;
        if (!groups[key]) groups[key] = [];
        groups[key].push(item);
    });
    return Object.entries(groups).map(([_, items]) => ({
        userId: items[0].userId,
        dependentRocId: items[0].dependentRocId,
        dependentName: items[0].dependentName,
        dependentRelationType: items[0].dependentRelationType,
        dependentRelationTypeName: items[0].dependentRelationTypeName,
        periods: items,
    }));
}

const HensureInfo: React.FC<HensureInfoProps> = ({ userId, active, tabUpdateidx }) => {
    const [groups, setGroups] = useState<HensureGroup[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [readonlyIdentityFields, setReadonlyIdentityFields] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [editingPeriod, setEditingPeriod] = useState<Hensure | null>(null);
    const [currentGroup, setCurrentGroup] = useState<HensureGroup | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);

    const [form] = Form.useForm();

    useEffect(() => {
        if (active) {
            fetchHensureList();
        }
    }, [active, userId, tabUpdateidx]);

    const fetchHensureList = async () => {
        setLoading(true);
        setErrorMsg('');
        try {
            const res = await getHensureList(userId);
            if (res.success && res.data) {
                setGroups(groupByDependent(res.data));
            } else {
                message.error(res.message || "載入失敗");
                setErrorMsg(res.message || "載入失敗");
            }
        } catch (err) {
            message.error("發生錯誤，無法載入");
            setErrorMsg("發生錯誤，無法載入");
        } finally {
            setLoading(false);
        }
    };

    const openModal = (group?: HensureGroup, record?: Hensure) => {
        setCurrentGroup(group ?? null);

        if (record) {
            // 編輯既有投保期間
            setEditingPeriod(record);
            setReadonlyIdentityFields(true);
            form.setFieldsValue({
                ...record,
                HealthInsStartDate: record.healthInsStartDate
                    ? dayjs(record.healthInsStartDate)
                    : null,
                HealthInsEndDate: record.healthInsEndDate
                    ? dayjs(record.healthInsEndDate)
                    : null,
            });
        } else if (group) {
            // 新增投保期間（針對既有眷屬）
            setEditingPeriod(null);
            setReadonlyIdentityFields(true);
            form.resetFields();
            form.setFieldsValue({
                ...createEmptyHensure(),
                userId: userId,
                dependentRocId: group.dependentRocId,
                dependentName: group.dependentName,
                dependentRelationType: group.dependentRelationType,
            });
        } else {
            // 新增新的眷屬與投保期間
            setEditingPeriod(null);
            setReadonlyIdentityFields(false);
            form.resetFields();
            form.setFieldsValue({
                ...createEmptyHensure(),
                userId: userId,
            });
        }

        setIsModalOpen(true);
    };

    const handleModalSubmit = async () => {
        try {
            const values = await form.validateFields();
            const payload: Hensure = {
                ...values,
                userId: userId,
                healthInsStartDate: values.HealthInsStartDate
                    ? values.HealthInsStartDate.format("YYYY-MM-DD")
                    : "",
                healthInsEndDate: values.HealthInsEndDate
                    ? values.HealthInsEndDate.format("YYYY-MM-DD")
                    : "",
                isNewDependent: currentGroup === null,
            };

            setModalLoading(true);
            let res;
            if (editingPeriod) {
                res = await editHensure({ ...payload, uid: editingPeriod.uid });
            } else {
                res = await addHensure(payload);
            }

            if (res.success && res.data?.result) {
                message.success(editingPeriod ? "更新成功" : "新增成功");
                setIsModalOpen(false);
                fetchHensureList();
            } else {
                message.error(res.data?.msg || "操作失敗");
            }
        } catch (err) {
            if (!(err as any)?.errorFields) {
                message.error("儲存發生錯誤");
            }
        } finally {
            setModalLoading(false);
        }
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteHensure(uid);
            if (res.success && res.data?.result) {
                message.success("刪除成功");
                fetchHensureList();
            } else {
                message.error(res.data?.msg || "刪除失敗");
            }
        } catch {
            message.error("刪除發生錯誤");
        }
    };

    const groupColumns: ColumnsType<HensureGroup> = [
        {
            title: "身分證字號",
            dataIndex: "dependentRocId",
            render: (text) => (
                <Space>
                    <IdcardOutlined style={{ color: '#1890ff' }} />
                    <Text strong>{text}</Text>
                </Space>
            )
        },
        {
            title: "姓名",
            dataIndex: "dependentName",
            render: (text) => (
                <Space>
                    <UserOutlined style={{ color: '#52c41a' }} />
                    <Text>{text}</Text>
                </Space>
            )
        },
        {
            title: "關係",
            dataIndex: "dependentRelationTypeName",
            render: (text) => (
                <Space>
                    <TeamOutlined style={{ color: '#722ed1' }} />
                    <Text>{text}</Text>
                </Space>
            )
        },
        {
            title: "操作",
            render: (_, record) => (
                <Button
                    type="primary"
                    ghost
                    icon={<PlusOutlined />}
                    onClick={() => openModal(record)}
                    style={{ borderRadius: '6px' }}
                >
                    新增投保期間
                </Button>
            ),
        },
    ];

    const periodColumns: ColumnsType<Hensure> = [
        {
            title: "健保起日",
            dataIndex: "healthInsStartDate",
            render: (text) => (
                <Space>
                    <CalendarOutlined style={{ color: '#1890ff' }} />
                    <Text>{text || '-'}</Text>
                </Space>
            )
        },
        {
            title: "健保迄日",
            dataIndex: "healthInsEndDate",
            render: (text) => (
                <Space>
                    <ScheduleOutlined style={{ color: '#52c41a' }} />
                    <Text>{text || '-'}</Text>
                </Space>
            )
        },
        {
            title: "備註",
            dataIndex: "remark",
            render: (text) => (
                <Space>
                    <FileTextOutlined style={{ color: '#722ed1' }} />
                    <Text>{text || '-'}</Text>
                </Space>
            )
        },
        {
            title: "操作",
            render: (_, record) => (
                <Space>
                    <Button
                        type="text"
                        icon={<EditOutlined />}
                        onClick={() => openModal(currentGroup!, record)}
                    >
                        編輯
                    </Button>
                    <Popconfirm
                        title={
                            <div>
                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                <Text>確定要刪除此筆資料嗎？</Text>
                            </div>
                        }
                        onConfirm={() => setDeleteUid(record.uid)}
                        okText="確認"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                    >
                        <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                        >
                            刪除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    if (!active) return null;
    if (errorMsg) return (
        <div style={{
            color: '#ff4d4f',
            textAlign: 'center',
            padding: '40px 20px',
            background: '#fff1f0',
            borderRadius: '8px',
            border: '1px solid #ffccc7'
        }}>
            <ExclamationCircleOutlined style={{ marginRight: 8 }} />
            錯誤：{errorMsg}
        </div>
    );

    return (
        <>
            <Card
                title={
                    <Space>
                        <MedicineBoxOutlined />
                        <Title level={4} style={{ margin: 0 }}>眷保資料</Title>
                    </Space>
                }
                loading={loading}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => openModal()}
                        style={{ borderRadius: '6px' }}
                    >
                        新增眷保資料
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <Table
                    columns={groupColumns}
                    dataSource={groups}
                    loading={loading}
                    rowKey={(r) => r.dependentRocId}
                    expandable={{
                        expandedRowRender: (record) => (
                            <div style={{
                                padding: '16px',
                                background: 'rgba(0, 0, 0, 0.02)',
                                borderRadius: '8px',
                                margin: '8px 24px'
                            }}>
                                <Table
                                    columns={periodColumns}
                                    dataSource={record.periods}
                                    rowKey={(r) => r.uid}
                                    pagination={false}
                                    rowClassName={(r) =>
                                        r.uid === deleteUid ? 'row-deleting-pulse' : ''
                                    }
                                    style={{
                                        backgroundColor: '#ffffff',
                                        borderRadius: '6px',
                                    }}
                                />
                            </div>
                        ),
                    }}
                    style={{ marginTop: '16px' }}
                />
            </Card>

            <Modal
                title={
                    <Space>
                        {editingPeriod ? <ScheduleOutlined /> : <MedicineBoxOutlined />}
                        <Title level={5} style={{ margin: 0 }}>
                            {editingPeriod
                                ? `編輯眷保資料（${form.getFieldValue("dependentName") || ""}）`
                                : "新增眷保資料"}
                        </Title>
                    </Space>
                }
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                onOk={handleModalSubmit}
                confirmLoading={modalLoading}
                width={800}
                centered
                maskClosable={false}
                destroyOnClose
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <Form layout="vertical" form={form} className="mt-4">
                    {/* 基本資料 */}
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <IdcardOutlined />
                                基本資料
                            </Space>
                        </Title>
                        <Row gutter={[24, 16]}>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>身分證字號</Text>}
                                    name="dependentRocId"
                                    rules={[{ required: true, message: "請輸入身分證字號" }]}
                                >
                                    <Input
                                        placeholder="請輸入身分證字號"
                                        disabled={readonlyIdentityFields}
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>姓名</Text>}
                                    name="dependentName"
                                    rules={[{ required: true, message: "請輸入姓名" }]}
                                >
                                    <Input
                                        placeholder="請輸入姓名"
                                        disabled={readonlyIdentityFields}
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>關係</Text>}
                                    name="dependentRelationType"
                                    rules={[{ required: true, message: "請選擇依附者關係" }]}
                                >
                                    <ApiSelect
                                        fetchOptions={getDepTypeOptions}
                                        disabled={readonlyIdentityFields}
                                        placeholder="請選擇關係"
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 投保期間 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <CalendarOutlined />
                                投保期間
                            </Space>
                        </Title>
                        <Row gutter={[24, 16]}>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>投保起日</Text>}
                                    name="HealthInsStartDate"
                                    rules={[{ required: true, message: "請選擇投保起日" }]}
                                >
                                    <DatePicker
                                        style={{ width: "100%", borderRadius: '6px' }}
                                        placeholder="請選擇投保起日"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>投保迄日</Text>}
                                    name="HealthInsEndDate"
                                >
                                    <DatePicker
                                        style={{ width: "100%", borderRadius: '6px' }}
                                        placeholder="請選擇投保迄日"
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 其他資訊 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <FileTextOutlined />
                                其他資訊
                            </Space>
                        </Title>
                        <Row gutter={[24, 16]}>
                            <Col span={24}>
                                <Form.Item
                                    label={<Text strong>備註</Text>}
                                    name="remark"
                                >
                                    <Input.TextArea
                                        rows={3}
                                        placeholder="請輸入備註"
                                        style={{ resize: 'none', borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>
                </Form>
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </>
    );
};

export default HensureInfo;
