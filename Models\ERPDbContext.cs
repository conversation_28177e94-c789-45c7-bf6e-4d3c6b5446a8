using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection.Emit;
using System.Threading;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Models
{
    public class ERPDbContext : DbContext
    {
        private readonly ILoggerService _logger;
        public ERPDbContext(ILoggerService logger, DbContextOptions<ERPDbContext> options) : base(options)
        {
            _logger = logger;
        }
        #region DbSet
        //Common
        public DbSet<Users> Common_Users { get; set; }
        public DbSet<Department> Common_Departments { get; set; }
        public DbSet<Roles> Common_Roles { get; set; }
        public DbSet<RolesPermissions> Common_RolesPermissions { get; set; }
        public DbSet<AuditLogs> Common_AuditLogs { get; set; }
        public DbSet<SystemMenu> Common_SystemMenu { get; set; }
        public DbSet<SystemGroups> Common_SystemGroups { get; set; }
        public DbSet<EnterpriseGroups> Common_EnterpriseGroups { get; set; }
        public DbSet<Position> Common_Positions { get; set; }
        public DbSet<Division> Common_Divisions { get; set; }
        public DbSet<Unit> Common_Units { get; set; }
        public DbSet<City> Common_Cities { get; set; }
        public DbSet<District> Common_Districts { get; set; }

        public DbSet<EnterpriseImage> Common_EnterpriseImage { get; set; }

        public DbSet<SystemParameters> Common_SystemParameters { get; set; }
        public DbSet<SystemParametersItem> Common_SystemParametersItem { get; set; }
        public DbSet<FileList> Common_FileList { get; set; }
        public DbSet<FileUpload> Common_FileUploads { get; set; }

        //PMS
        public DbSet<Asset> Pms_Assets { get; set; }
        public DbSet<InsuranceUnit> Pms_InsuranceUnits { get; set; }
        public DbSet<Manufacturer> Pms_Manufacturers { get; set; }
        public DbSet<StorageLocation> Pms_StorageLocations { get; set; }
        public DbSet<DepreciationForm> Pms_DepreciationForms { get; set; }
        public DbSet<DepreciationFormDetail> Pms_DepreciationFormDetail { get; set; }
        public DbSet<AssetAccount> Pms_AssetAccounts { get; set; }
        public DbSet<AssetSubAccount> Pms_AssetSubAccounts { get; set; }
        public DbSet<AssetSource> Pms_AssetSources { get; set; }
        public DbSet<AssetCategory> Pms_AssetCategory { get; set; }
        public DbSet<AccessoryEquipment> Pms_AccessoryEquipments { get; set; }
        public DbSet<PmsSystemParameter> Pms_SystemParameters { get; set; }

        public DbSet<AmortizationSource> Pms_AmortizationSources { get; set; }

        public DbSet<AssetStatus> Pms_AssetStatus { get; set; }

        public DbSet<PmsUserRole> PmsUserRoles { get; set; }
        public DbSet<PmsUserRoleMapping> PmsUserRoleMappings { get; set; }

        public DbSet<EquipmentType> Pms_EquipmentType { get; set; }
        public DbSet<AssetCarryOut> Pms_AssetCarryOut { get; set; }
        public DbSet<VendorMaintenance> Pms_VendorMaintenance { get; set; }
        public DbSet<AssetLocationTransfer> Pms_AssetLocationTransfer { get; set; }
        public DbSet<AssetLocationTransferDetail> Pms_AssetLocationTransferDetail { get; set; }

        //Pas
        public DbSet<Employee> Pas_Employee { get; set; }
        public DbSet<Education> Pas_Education { get; set; }
        public DbSet<Train> Pas_Train { get; set; }
        public DbSet<Examination> Pas_Examination { get; set; }
        public DbSet<Certification> Pas_Certification { get; set; }
        public DbSet<Undergo> Pas_Undergo { get; set; }
        public DbSet<Ensure> Pas_Ensure { get; set; }
        public DbSet<Suspend> Pas_Suspend { get; set; }
        public DbSet<Salary> Pas_Salary { get; set; }
        public DbSet<Hensure> Pas_Hensure { get; set; }
        public DbSet<Dependent> Pas_Dependent { get; set; }
        public DbSet<PerformancePointGroup> Pas_PerformancePointGroup { get; set; }
        public DbSet<PerformancePointType> Pas_PerformancePointType { get; set; }
        public DbSet<PerformancePointRecord> Pas_PerformancePointRecord { get; set; }
        public DbSet<RegularSalaryItem> Pas_RegularSalaryItem { get; set; }
        public DbSet<EmployeeRegularSalary> Pas_EmployeeRegularSalary { get; set; }
        public DbSet<SalaryPoint> Pas_SalaryPoint { get; set; }
        public DbSet<InsuranceGrade> Pas_InsuranceGrade { get; set; }
        public DbSet<InsuranceHistory> Pas_InsuranceHistory { get; set; }
        public DbSet<Promotion> Pas_Promotion { get; set; }
        public DbSet<ExpenseDepartmentChange> Pas_ExpenseDepartmentChange { get; set; }
        public DbSet<ServiceDepartmentChange> Pas_ServiceDepartmentChange { get; set; }


        //Ims
        /// <summary> 商業夥伴 </summary>
        public DbSet<Partner> Ims_Partner { get; set; }
        /// <summary> 自然人 </summary>
        public DbSet<IndividualDetail> Ims_IndividualDetail { get; set; }
        /// <summary> 法人 </summary>
        public DbSet<EnterpriseDetail> Ims_EnterpriseDetail { get; set; }
        /// <summary> 客戶詳細資訊 </summary>
        public DbSet<CustomerDetail> Ims_CustomerDetail { get; set; }
        /// <summary> 供應商詳細資訊 </summary>
        public DbSet<SupplierDetail> Ims_SupplierDetail { get; set; }
        /// <summary> 商業夥伴聯絡人聯結表 </summary>
        public DbSet<PartnerContact> Ims_PartnerContact { get; set; }
        /// <summary> 聯絡人 </summary>
        public DbSet<Contact> Ims_Contact { get; set; }
        /// <summary> 聯絡人角色 </summary>
        public DbSet<ContactRole> Ims_ContactRole { get; set; }
        /// <summary> 商業夥伴地址 </summary>
        public DbSet<PartnerAddress> Ims_PartnerAddress { get; set; }
        /// <summary> 庫存品 </summary>
        public DbSet<Item> Ims_Item { get; set; }
        /// <summary> 庫存品分類 </summary>
        public DbSet<ItemCategory> Ims_ItemCategory { get; set; }
        /// <summary> 主分類 </summary>
        public DbSet<ItemPrice> Ims_ItemPrice { get; set; }
        /// <summary> 價格類型 </summary>
        public DbSet<PriceType> Ims_PriceType { get; set; }
        /// <summary> 客戶分類 </summary>
        public DbSet<CustomerCategory> Ims_CustomerCategory { get; set; }
        /// <summary> 供應商分類 </summary>
        public DbSet<SupplierCategory> Ims_SupplierCategory { get; set; }

        #endregion

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // Design-time configuration - use a default connection string
                // This will be used by EF tools when no DI container is available
                optionsBuilder.UseSqlServer("Data Source=.\\SQLEXPRESS2022;Initial Catalog=FAST_ERP;Persist Security Info=True;User ID=sa;Password=********;Encrypt=True;TrustServerCertificate=True;");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            new DbSeederData().SeedData(modelBuilder);

            //資料表關聯設定
            modelBuilder.ConfigureSystemMenuRelationships();

            // Entity Framework Core 生成 SQL 查詢時，會自動將過濾條件加入到 WHERE 子句中
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(ModelBaseEntity).IsAssignableFrom(entityType.ClrType))
                {
                    var parameter = Expression.Parameter(entityType.ClrType, "e");
                    var property = Expression.Property(parameter, nameof(ModelBaseEntity.IsDeleted));
                    var filter = Expression.Lambda(
                        Expression.Equal(
                            Expression.Convert(property, typeof(bool?)),
                            Expression.Constant(false, typeof(bool?))),
                        parameter);

                    modelBuilder.Entity(entityType.ClrType).HasQueryFilter(filter);
                }
            }

            // PmsUserRole 配置
            modelBuilder.Entity<PmsUserRole>()
                .HasQueryFilter(e => !e.IsDeleted);

            // PmsUserRoleMapping 配置
            modelBuilder.Entity<PmsUserRoleMapping>()
                .HasQueryFilter(e => !e.IsDeleted);

            // PmsUserRoleMapping 與 Users 關聯
            modelBuilder.Entity<PmsUserRoleMapping>()
                .HasOne(m => m.User)
                .WithMany()
                .HasForeignKey(m => m.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // PmsUserRoleMapping 與 PmsUserRole 關聯
            modelBuilder.Entity<PmsUserRoleMapping>()
                .HasOne(m => m.PmsUserRole)
                .WithMany()
                .HasForeignKey(m => m.PmsUserRoleId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ItemPrice>(Entity =>
            {
                Entity
                .HasOne(ip => ip.Item)
                .WithMany(ip => ip.Prices)
                .HasForeignKey(i => i.ItemID)
                .OnDelete(DeleteBehavior.Restrict);
                Entity
                .HasOne(ip => ip.PriceType)
                .WithMany()
                .HasForeignKey(ip => ip.PriceTypeID)
                .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<Item>(Entity =>
            {
                Entity
                .HasOne(i => i.ItemCategory)
                .WithMany(ic => ic.Items)
                .HasForeignKey(i => i.ItemCategoryID)
                .OnDelete(DeleteBehavior.Restrict);
            });

            // Ims 模組的 Partner 相關配置
            modelBuilder.Entity<Partner>(entity =>
            {
                // Partner 與 PartnerAddress 的一對多關聯
                entity.HasMany(p => p.Addresses)
                      .WithOne(pa => pa.Partner)
                      .HasForeignKey(pa => pa.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 PartnerAddress 也會被刪除

                // Partner 與 IndividualDetail 的一對一關聯
                entity.HasOne(p => p.IndividualDetail)
                      .WithOne()
                      .HasForeignKey<IndividualDetail>(id => id.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 IndividualDetail 也會被刪除

                // Partner 與 EnterpriseDetail 的一對一關聯
                entity.HasOne(p => p.EnterpriseDetail)
                      .WithOne()
                      .HasForeignKey<EnterpriseDetail>(od => od.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 EnterpriseDetail 也會被刪除

                // Partner 與 CustomerDetail 的一對一關聯
                entity.HasOne(p => p.CustomerDetail)
                      .WithOne()
                      .HasForeignKey<CustomerDetail>(cd => cd.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 CustomerDetail 也會被刪除

                // Partner 與 SupplierDetail 的一對一關聯
                entity.HasOne(p => p.SupplierDetail)
                      .WithOne()
                      .HasForeignKey<SupplierDetail>(sd => sd.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 SupplierDetail 也會被刪除
            });

            // SupplierDetail 與 SupplierCategory 的多對一關聯
            modelBuilder.Entity<SupplierDetail>(entity =>
            {
                entity.HasOne(sd => sd.SupplierCategory)
                      .WithMany()
                      .HasForeignKey(sd => sd.SupplierCategoryID)
                      .OnDelete(DeleteBehavior.Restrict); // 當 SupplierCategory 被刪除時，不允許刪除 (保護資料完整性)
            });

            // CustomerDetail 與 CustomerCategory 的多對一關聯
            modelBuilder.Entity<CustomerDetail>(entity =>
            {
                entity.HasOne(cd => cd.CustomerCategory)
                      .WithMany()
                      .HasForeignKey(cd => cd.CustomerCategoryID)
                      .OnDelete(DeleteBehavior.Restrict); // 當 CustomerCategory 被刪除時，不允許刪除 (保護資料完整性)
            });

            // Partner 與 Contact 的多對多關聯 (透過 PartnerContact 聯結表)
            modelBuilder.Entity<PartnerContact>(entity =>
            {
                entity.HasKey(pc => new { pc.PartnerID, pc.ContactID, pc.ContactRoleID }); // 定義複合主鍵

                entity.HasOne(pc => pc.Partner)
                      .WithMany(p => p.PartnerContacts)
                      .HasForeignKey(pc => pc.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的聯結記錄也會被刪除

                entity.HasOne(pc => pc.Contact)
                      .WithMany(c => c.PartnerContacts)
                      .HasForeignKey(pc => pc.ContactID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Contact 被刪除時，相關的聯結記錄也會被刪除

                entity.HasOne(pc => pc.ContactRole)
                      .WithMany(cr => cr.PartnerContacts)
                      .HasForeignKey(pc => pc.ContactRoleID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 ContactRole 被刪除時，相關的聯結記錄也會被刪除
            });
        }

        /// <summary>
        /// 覆寫 SaveChangesAsync 以自動偵測資料變化並記錄日誌
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>受影響的行數</returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            var changedEntries = CaptureChangedEntries();
            var transactionId = Guid.NewGuid().ToString();

            try
            {
                var result = await base.SaveChangesAsync(cancellationToken);

                if (result > 0 && changedEntries.Any())
                {
                    await LogEntityChangesAsync(changedEntries, transactionId);
                }

                return result;
            }
            catch (Exception ex)
            {
                if (_logger != null)
                {
                    await _logger.LogErrorAsync(
                        $"資料庫保存變更失敗，交易ID: {transactionId}",
                        ex,
                        "ERPDbContext"
                    );
                }
                throw;
            }
        }

        /// <summary> 捕獲變更的實體 </summary>
        /// <returns>變更的實體列表</returns>
        private List<EntityChangeInfo> CaptureChangedEntries()
        {
            return ChangeTracker.Entries()
                .Where(e => e.State != EntityState.Unchanged)
                .Select(e => new EntityChangeInfo
                {
                    EntityType = e.Entity.GetType().Name,
                    State = e.State,
                    Original = e.State == EntityState.Added ? null : e.OriginalValues.ToObject(),
                    Current = e.Entity,
                    UserId = ExtractUserId(e.Entity)
                })
                .ToList();
        }

        /// <summary> 記錄實體變更日誌 </summary>
        /// <param name="changedEntries">變更的實體</param>
        /// <param name="transactionId">交易ID</param>
        private async Task LogEntityChangesAsync(List<EntityChangeInfo> changedEntries, string transactionId)
        {
            if (_logger == null) return; // Skip logging during design-time

            try
            {
                var processed = new HashSet<object>();

                foreach (var entry in changedEntries)
                {
                    if (processed.Contains(entry.Current))
                        continue;

                    var message = $"資料變更: {entry.EntityType} - 狀態: {entry.State}";
                    var logData = new
                    {
                        EntityType = entry.EntityType,
                        State = entry.State.ToString(),
                        UserId = entry.UserId,
                        Original = entry.Original,
                        Modified = entry.Current
                    };

                    await _logger.LogDataAsync(message, logData, transactionId, "ERPDbContext");
                    processed.Add(entry.Current);
                }
            }
            catch (Exception ex)
            {
                // 日誌記錄失敗不應該影響主要業務流程
                // 使用 Console.WriteLine 避免遞歸日誌錯誤
                Console.WriteLine($"[ERPDbContext] 實體變更日誌記錄失敗: {ex.Message}");
            }
        }

        /// <summary> 提取用戶ID </summary>
        /// <param name="entity">實體對象</param>
        /// <returns>用戶ID</returns>
        private static string? ExtractUserId(object entity)
        {
            try
            {
                return entity.GetType()
                    .GetProperty("UpdateUserId")?
                    .GetValue(entity, null)?
                    .ToString();
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// 實體變更信息
    /// </summary>
    internal class EntityChangeInfo
    {
        public string EntityType { get; set; } = string.Empty;
        public EntityState State { get; set; }
        public object? Original { get; set; }
        public object Current { get; set; } = null!;
        public string? UserId { get; set; }
    }
}