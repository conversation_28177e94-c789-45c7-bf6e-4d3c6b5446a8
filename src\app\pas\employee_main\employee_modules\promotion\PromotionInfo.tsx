import { useEffect, useState } from 'react';
import { Modal, Button, message, Table, Form, Input, DatePicker, Card, Typography, Space, Row, Col, Divider, Select, Popconfirm, InputNumber, Tag, Tooltip } from 'antd';
import dayjs from 'dayjs';
import {
    EditOutlined,
    DeleteOutlined,
    PlusOutlined,
    UserOutlined,
    CalendarOutlined,
    SettingOutlined,
    BankOutlined,
    UpOutlined,
    FilterOutlined,
    ApartmentOutlined,
    TeamOutlined,
    SwapOutlined,
    QuestionCircleOutlined
} from '@ant-design/icons';
import {
    getPromotionList,
    getPromotionDetail,
    getLatestPromotion,
    addPromotion,
    editPromotion,
    deletePromotion,
    createEmptyPromotion,
    createPromotionFormData,
    type Promotion,
    type ExpenseDepartmentChange,
    type ServiceDepartmentChange
} from '@/services/pas/PromotionService';
import { getDepartments } from '@/services/common/departmentService';
import { getDivisions } from '@/services/common/divisionService';
import {
    getJobroleTypeOptions,
    getSalaryTypeOptions,
    getCategoryTypeOptions,
    getJobtitleOptions,
    getJobLevelOptions,
    getJobRankOptions,
    getPromotionTypeOptions
} from '@/services/pas/OptionParameterService';
import ApiSelect from '@/app/pas/components/ApiSelect';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import ExpenseDepartmentChangeModal from '@/app/pas/employee_main/employee_modules/expenseDepartment/ExpenseDepartmentChangeModal';
import ServiceDepartmentChangeModal from '@/app/pas/employee_main/employee_modules/serviceDepartment/ServiceDepartmentChangeModal';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;
const { Option } = Select;

type PromotionInfoProps = {
    userId: string;
    active: boolean;
    tabUpdateidx?: number;
};

const PromotionInfo: React.FC<PromotionInfoProps> = ({ userId, active, tabUpdateidx }) => {
    const [promotionList, setPromotionList] = useState<Promotion[]>([]);
    const [filteredList, setFilteredList] = useState<Promotion[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [promotionDetail, setPromotionDetail] = useState<Promotion | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [deletingRows, setDeletingRows] = useState<Set<string>>(new Set());
    const [form] = Form.useForm();

    // 篩選狀態
    const [selectedPromotionType, setSelectedPromotionType] = useState<string | null>(null);
    const [promotionTypeOptions, setPromotionTypeOptions] = useState<string[]>([]);

    // 部門異動Modal狀態
    const [isExpenseModalOpen, setIsExpenseModalOpen] = useState(false);
    const [isServiceModalOpen, setIsServiceModalOpen] = useState(false);

    // 部門異動資料
    const [expenseDepartmentChange, setExpenseDepartmentChange] = useState<ExpenseDepartmentChange | null>(null);
    const [serviceDepartmentChange, setServiceDepartmentChange] = useState<ServiceDepartmentChange | null>(null);

    // 選項資料
    const [departmentOptions, setDepartmentOptions] = useState<any[]>([]);
    const [divisionOptions, setDivisionOptions] = useState<any[]>([]);

    useEffect(() => {
        if (active) {
            fetchPromotionList();
            loadOptions();
        }
    }, [active, userId, tabUpdateidx]);

    // 當篩選條件改變時，重新篩選資料
    useEffect(() => {
        filterData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [promotionList, selectedPromotionType]);

    const loadOptions = async () => {
        try {
            const [deptRes, divRes] = await Promise.all([
                getDepartments(),
                getDivisions()
            ]);

            if (deptRes.success) setDepartmentOptions(deptRes.data || []);
            if (divRes.success) setDivisionOptions(divRes.data || []);
        } catch (error) {
            console.error('載入選項資料失敗:', error);
        }
    };

    const fetchPromotionList = async () => {
        setLoading(true);
        setErrorMsg('');
        try {
            const { success, data, message: msg } = await getPromotionList(userId);
            if (success && data) {
                setPromotionList(data);
                // 提取所有升遷類型選項
                const types = Array.from(new Set(data.map(item => item.promotionTypeName).filter(Boolean) as string[]));
                setPromotionTypeOptions(types);
            } else {
                message.error(msg || '載入升遷異動資料失敗');
            }
        } catch (error: any) {
            setErrorMsg(error.message || '未知錯誤');
            message.error(error.message || '載入失敗');
        } finally {
            setLoading(false);
        }
    };

    const filterData = () => {
        let filtered = [...promotionList];

        // 升遷類型篩選
        if (selectedPromotionType) {
            filtered = filtered.filter(item => item.promotionTypeName === selectedPromotionType);
        }

        setFilteredList(filtered);
    };

    const handlePromotionTypeChange = (value: string) => {
        setSelectedPromotionType(value === 'all' ? null : value);
    };

    const clearFilters = () => {
        setSelectedPromotionType(null);
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        try {
            const { success, data, message: msg } = await getPromotionDetail(uid);
            if (success && data) {
                setPromotionDetail(data);
                form.resetFields();

                // 設定升遷基本資料
                form.setFieldsValue({
                    promotionType: data.promotionType,
                    jobTitle: data.jobTitle,
                    jobLevel: data.jobLevel,
                    jobRank: data.jobRank,
                    promotionDate: data.promotionDate ? dayjs(data.promotionDate) : null,
                    effectiveDate: data.effectiveDate ? dayjs(data.effectiveDate) : null,
                    promotionReason: data.promotionReason,
                    jobroleType: data.jobroleType,
                    salaryType: data.salaryType,
                    salaryAmount: data.salaryAmount ? parseFloat(data.salaryAmount) : null,
                    categoryType: data.categoryType,
                    remark: data.remark,
                });

                // 載入部門異動資料
                setExpenseDepartmentChange(data.expenseDepartmentChange || null);
                setServiceDepartmentChange(data.serviceDepartmentChange || null);

                setIsModalOpen(true);
            } else {
                message.error(msg || '載入升遷異動資料失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '載入升遷異動資料時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            setModalLoading(true);

            // 建立升遷資料物件（包含部門異動資料）
            const promotionData: Promotion = {
                uid: promotionDetail?.uid || '',
                userId: userId,
                promotionType: values.promotionType || '',
                jobTitle: values.jobTitle || '',
                jobLevel: values.jobLevel || '',
                jobRank: values.jobRank || '',
                promotionDate: values.promotionDate ? values.promotionDate.format('YYYY-MM-DD') : '',
                effectiveDate: values.effectiveDate ? values.effectiveDate.format('YYYY-MM-DD') : '',
                promotionReason: values.promotionReason || '',
                jobroleType: values.jobroleType || '',
                salaryType: values.salaryType || '',
                salaryAmount: values.salaryAmount ? values.salaryAmount.toString() : '',
                categoryType: values.categoryType || '',
                remark: values.remark || '',
                expenseDepartmentChange: expenseDepartmentChange || undefined,
                serviceDepartmentChange: serviceDepartmentChange || undefined
            };

            // 轉換為FormData
            const formData = createPromotionFormData(promotionData);

            let res;
            if (promotionDetail) {
                res = await editPromotion(formData);
            } else {
                res = await addPromotion(formData);
            }

            if (res.success) {
                message.success(promotionDetail ? '更新成功' : '新增成功');
                setIsModalOpen(false);
                fetchPromotionList();
            } else {
                throw new Error(res.message || '儲存失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '儲存時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleDeleteConfirm = (uid: string) => {
        // 第一步：添加刪除動畫類別到行
        setDeletingRows(prev => new Set(prev.add(uid)));

        // 第二步：設置要刪除的 UID，觸發倒數刪除組件
        setDeleteUid(uid);
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deletePromotion(uid);
            if (res.success) {
                message.success('刪除成功');
                fetchPromotionList();
            } else {
                throw new Error(res.message || '刪除失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '刪除時發生錯誤');
        } finally {
            // 清除刪除狀態
            setDeleteUid(null);
            setDeletingRows(prev => {
                const newSet = new Set(prev);
                newSet.delete(uid);
                return newSet;
            });
        }
    };

    const handleDeleteCancel = () => {
        if (deleteUid) {
            // 取消刪除時清除動畫狀態
            setDeletingRows(prev => {
                const newSet = new Set(prev);
                newSet.delete(deleteUid);
                return newSet;
            });
        }
        setDeleteUid(null);
    };

    const handleAddNew = async () => {
        setModalLoading(true);
        try {
            // 載入最新升遷資料作為預設值
            const { success, data, message: msg } = await getLatestPromotion(userId);

            let defaultData: Promotion;
            if (success && data) {
                // 使用最新資料作為預設值，但清空其他欄位
                defaultData = {
                    ...data,
                    uid: '',
                    promotionType: '',
                    promotionDate: '',
                    effectiveDate: '',
                    promotionReason: '',
                    remark: ''
                };
                message.info('已載入最新職稱、職等、級數、任用資格、薪俸類型、薪俸、錄用類別作為預設值');
            } else {
                defaultData = createEmptyPromotion(userId);
            }

            setPromotionDetail(null);
            form.resetFields();

            // 設定預設值
            form.setFieldsValue({
                promotionType: '',
                jobTitle: defaultData.jobTitle,
                jobLevel: defaultData.jobLevel,
                jobRank: defaultData.jobRank,
                jobroleType: defaultData.jobroleType,
                salaryType: defaultData.salaryType,
                salaryAmount: defaultData.salaryAmount ? parseFloat(defaultData.salaryAmount) : null,
                categoryType: defaultData.categoryType,
                promotionDate: null,
                effectiveDate: null,
                promotionReason: '',
                remark: ''
            });

            // 清除部門異動資料
            setExpenseDepartmentChange(null);
            setServiceDepartmentChange(null);

            setIsModalOpen(true);
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '準備新增表單時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleModalCancel = () => {
        setIsModalOpen(false);
        setPromotionDetail(null);
        setExpenseDepartmentChange(null);
        setServiceDepartmentChange(null);
        form.resetFields();
    };

    // 開支部門異動處理函數
    const handleAddExpenseDepartment = () => {
        setIsExpenseModalOpen(true);
    };

    const handleEditExpenseDepartment = () => {
        setIsExpenseModalOpen(true);
    };

    const handleRemoveExpenseDepartment = () => {
        setExpenseDepartmentChange(null);
    };

    const handleExpenseModalOk = (data: ExpenseDepartmentChange) => {
        setExpenseDepartmentChange(data);
        setIsExpenseModalOpen(false);
        message.success('開支部門異動設定完成');
    };

    const handleExpenseModalCancel = () => {
        setIsExpenseModalOpen(false);
    };

    // 服務部門異動處理函數
    const handleAddServiceDepartment = () => {
        setIsServiceModalOpen(true);
    };

    const handleEditServiceDepartment = () => {
        setIsServiceModalOpen(true);
    };

    const handleRemoveServiceDepartment = () => {
        setServiceDepartmentChange(null);
    };

    const handleServiceModalOk = (data: ServiceDepartmentChange) => {
        setServiceDepartmentChange(data);
        setIsServiceModalOpen(false);
        message.success('服務部門異動設定完成');
    };

    const handleServiceModalCancel = () => {
        setIsServiceModalOpen(false);
    };

    const columns = [
        {
            title: '升遷類型',
            dataIndex: 'promotionTypeName',
            key: 'promotionTypeName',
            width: 100,
        },
        {
            title: '職稱',
            dataIndex: 'jobTitleName',
            key: 'jobTitleName',
            width: 120,
            render: (value: string, record: Promotion) => value || record.jobTitle
        },
        {
            title: '職等',
            dataIndex: 'jobLevelName',
            key: 'jobLevelName',
            width: 80,
            render: (value: string, record: Promotion) => value || record.jobLevel
        },
        {
            title: '級數',
            dataIndex: 'jobRankName',
            key: 'jobRankName',
            width: 80,
            render: (value: string, record: Promotion) => value || record.jobRank
        },
        {
            title: '任用資格',
            dataIndex: 'jobroleTypeName',
            key: 'jobroleTypeName',
            width: 100,
            render: (value: string, record: Promotion) => value || record.jobroleType || '-'
        },
        {
            title: '薪俸類型',
            dataIndex: 'salaryTypeName',
            key: 'salaryTypeName',
            width: 100,
            render: (value: string, record: Promotion) => value || record.salaryType || '-'
        },
        {
            title: (
                <Space>
                    薪俸
                    <Tooltip title="月薪產生會依當前生效薪俸計算">
                        <QuestionCircleOutlined style={{ color: '#1890ff', cursor: 'help' }} />
                    </Tooltip>
                </Space>
            ),
            dataIndex: 'salaryAmount',
            key: 'salaryAmount',
            width: 120,
            render: (value: string, record: Promotion) => {
                if (!value || value === '0' || value === '') return '-';
                const amount = parseFloat(value);
                if (isNaN(amount)) return '-';

                // 根據薪俸類型決定顯示單位：薪點(1)顯示"點"，其他顯示"元"
                const unit = record.salaryType === '1' ? '點' : '元';
                return `${amount.toLocaleString()}${unit}`;
            }
        },
        {
            title: '升遷日期',
            dataIndex: 'promotionDate',
            key: 'promotionDate',
            width: 100,
        },
        {
            title: '生效日期',
            dataIndex: 'effectiveDate',
            key: 'effectiveDate',
            width: 100,
        },
        {
            title: '部門異動',
            key: 'departmentChanges',
            width: 200,
            render: (text: any, record: Promotion) => {
                const hasExpense = record.expenseDepartmentChange?.expenseDepartmentName;
                const hasService = record.serviceDepartmentChange?.serviceDepartmentName;
                const expenseEffectiveDate = record.expenseDepartmentChange?.effectiveDate;
                const serviceEffectiveDate = record.serviceDepartmentChange?.effectiveDate;

                if (!hasExpense && !hasService) {
                    return <Text type="secondary">無</Text>;
                }

                return (
                    <div>
                        {hasExpense && (
                            <Tag color="green" style={{ marginBottom: 4, fontSize: '12px' }}>
                                開支：{hasExpense}
                                {expenseEffectiveDate && (
                                    <div style={{ fontSize: '10px', opacity: 0.8, marginTop: 2 }}>
                                        生效：{expenseEffectiveDate}
                                    </div>
                                )}
                            </Tag>
                        )}
                        {hasService && (
                            <Tag color="purple" style={{ fontSize: '12px' }}>
                                服務：{hasService}
                                {serviceEffectiveDate && (
                                    <div style={{ fontSize: '10px', opacity: 0.8, marginTop: 2 }}>
                                        生效：{serviceEffectiveDate}
                                    </div>
                                )}
                            </Tag>
                        )}
                    </div>
                );
            }
        },
        {
            title: '備註',
            dataIndex: 'remark',
            key: 'remark',
            width: 150,
            ellipsis: true,
            render: (text: string) => text || '-'
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (text: any, record: Promotion) => (
                <Space size="small">
                    <Button
                        type="link"
                        icon={<EditOutlined />}
                        onClick={() => handleRowClick(record.uid)}
                        style={{ padding: 0 }}
                    >
                        編輯
                    </Button>
                    <Popconfirm
                        title="確認刪除"
                        description="確定要刪除這筆升遷異動資料嗎？"
                        onConfirm={() => handleDeleteConfirm(record.uid)}
                        okText="確認"
                        cancelText="取消"
                    >
                        <Button
                            type="link"
                            danger
                            icon={<DeleteOutlined />}
                            style={{ padding: 0 }}
                        >
                            刪除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    return (
        <div className="promotion-info">
            <Card
                title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <UpOutlined />
                        <span>升遷異動資料</span>
                    </div>
                }
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddNew}
                        loading={modalLoading}
                    >
                        新增升遷異動
                    </Button>
                }
                style={{ marginBottom: 24 }}
            >
                {errorMsg && (
                    <div style={{ color: 'red', marginBottom: 16 }}>
                        錯誤: {errorMsg}
                    </div>
                )}

                {/* 篩選區域 */}
                <Card
                    size="small"
                    style={{ marginBottom: 16, background: '#fafafa' }}
                    title={
                        <Space>
                            <FilterOutlined />
                            <Text strong>篩選條件</Text>
                        </Space>
                    }
                >
                    <Row gutter={[16, 16]} align="middle">
                        <Col xs={24} sm={12} md={8}>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Text>升遷類型：</Text>
                                <Select
                                    value={selectedPromotionType || 'all'}
                                    onChange={handlePromotionTypeChange}
                                    style={{ width: '100%' }}
                                    placeholder="選擇升遷類型"
                                >
                                    <Option value="all">全部類型</Option>
                                    {promotionTypeOptions.map(type => (
                                        <Option key={type} value={type}>{type}</Option>
                                    ))}
                                </Select>
                            </Space>
                        </Col>
                        <Col xs={24} sm={12} md={8}>
                            <Space>
                                <Button onClick={clearFilters}>
                                    清除篩選
                                </Button>
                                <Text type="secondary">
                                    共 {filteredList.length} 筆資料
                                </Text>
                            </Space>
                        </Col>
                    </Row>
                </Card>

                <Table
                    columns={columns}
                    dataSource={filteredList}
                    rowKey="uid"
                    loading={loading}
                    size="small"
                    pagination={{
                        total: filteredList.length,
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
                    }}
                    scroll={{ x: 1220 }}
                    rowClassName={(record) =>
                        deletingRows.has(record.uid) ? 'row-deleting-pulse' : ''
                    }
                />
            </Card>

            <Modal
                title={promotionDetail ? '編輯升遷異動' : '新增升遷異動'}
                open={isModalOpen}
                onOk={handleModalOk}
                onCancel={handleModalCancel}
                confirmLoading={modalLoading}
                width={800}
                maskClosable={false}
            >
                <Form
                    form={form}
                    layout="vertical"
                    style={{ marginTop: 16 }}
                >
                    {/* 基本資訊區塊 */}
                    <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={12}>
                            <Form.Item
                                label="升遷類型"
                                name="promotionType"
                                rules={[{ required: true, message: '請選擇升遷類型' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getPromotionTypeOptions}
                                    placeholder="請選擇升遷類型"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="生效日期"
                                name="effectiveDate"
                                rules={[{ required: true, message: '請選擇生效日期' }]}
                            >
                                <DatePicker placeholder="請選擇生效日期" style={{ width: '100%' }} />
                            </Form.Item>
                        </Col>
                    </Row>

                    {/* 職務資訊區塊 */}
                    <Divider orientation="left" style={{ marginTop: 0, marginBottom: 16 }}>
                        <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#1890ff' }}>
                            <SettingOutlined style={{ marginRight: 8 }} />
                            職務資訊
                        </span>
                    </Divider>
                    <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={12}>
                            <Form.Item
                                label="職稱"
                                name="jobTitle"
                                rules={[{ required: true, message: '請選擇職稱' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getJobtitleOptions}
                                    placeholder="請選擇職稱"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="職等"
                                name="jobLevel"
                                rules={[{ required: true, message: '請選擇職等' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getJobLevelOptions}
                                    placeholder="請選擇職等"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="級數"
                                name="jobRank"
                                rules={[{ required: true, message: '請選擇級數' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getJobRankOptions}
                                    placeholder="請選擇級數"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="任用資格"
                                name="jobroleType"
                                rules={[{ required: true, message: '請選擇任用資格' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getJobroleTypeOptions}
                                    placeholder="請選擇任用資格"
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={12}>
                            <Form.Item
                                label="錄用類別"
                                name="categoryType"
                                rules={[{ required: true, message: '請選擇錄用類別' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getCategoryTypeOptions}
                                    placeholder="請選擇錄用類別"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            {/* 空白欄位保持佈局平衡 */}
                        </Col>
                    </Row>

                    {/* 薪俸資訊區塊 */}
                    <Divider orientation="left" style={{ marginTop: 0, marginBottom: 16 }}>
                        <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#52c41a' }}>
                            <BankOutlined style={{ marginRight: 8 }} />
                            薪俸資訊
                        </span>
                    </Divider>
                    <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={12}>
                            <Form.Item
                                label="薪俸類型"
                                name="salaryType"
                                rules={[{ required: true, message: '請選擇薪俸類型' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getSalaryTypeOptions}
                                    placeholder="請選擇薪俸類型"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="薪俸"
                                name="salaryAmount"
                                rules={[{ required: true, message: '請輸入薪俸金額' }]}
                            >
                                <InputNumber
                                    placeholder="請輸入薪俸金額"
                                    min={0}
                                    max={999999999}
                                    precision={2}
                                    style={{ width: '100%' }}
                                    formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    {/* 異動資訊區塊 */}
                    <Divider orientation="left" style={{ marginTop: 0, marginBottom: 16 }}>
                        <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#722ed1' }}>
                            <CalendarOutlined style={{ marginRight: 8 }} />
                            異動資訊
                        </span>
                    </Divider>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label="升遷日期"
                                name="promotionDate"
                                rules={[{ required: true, message: '請選擇升遷日期' }]}
                            >
                                <DatePicker placeholder="請選擇升遷日期" style={{ width: '100%' }} />
                            </Form.Item>
                        </Col>
                        <Col span={12} style={{ display: 'flex', alignItems: 'end' }}>
                            {/* 空白欄位保持佈局平衡 */}
                        </Col>
                        <Col span={24}>
                            <Form.Item
                                label="升遷原因"
                                name="promotionReason"
                            >
                                <Input.TextArea
                                    placeholder="請輸入升遷原因（選填）"
                                    rows={3}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={24}>
                            <Form.Item
                                label="備註"
                                name="remark"
                            >
                                <Input.TextArea
                                    placeholder="請輸入備註（選填）"
                                    rows={3}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    {/* 部門異動設定區塊 */}
                    <Divider orientation="left" style={{ marginTop: 24, marginBottom: 16 }}>
                        <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#fa541c' }}>
                            <SwapOutlined style={{ marginRight: 8 }} />
                            部門異動設定（選填）
                        </span>
                    </Divider>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Card size="small" style={{ textAlign: 'center' }}>
                                <div style={{ marginBottom: 12 }}>
                                    <BankOutlined style={{ fontSize: 24, color: '#52c41a' }} />
                                    <div style={{ marginTop: 8, fontWeight: 'bold' }}>開支部門異動</div>
                                </div>
                                {expenseDepartmentChange && expenseDepartmentChange.expenseDepartmentName ? (
                                    <div>
                                        <Tag color="green" style={{ marginBottom: 8 }}>
                                            {expenseDepartmentChange.expenseDepartmentName}
                                        </Tag>
                                        {expenseDepartmentChange.effectiveDate && (
                                            <div style={{ marginBottom: 8, fontSize: '12px', color: '#666' }}>
                                                生效日期：{expenseDepartmentChange.effectiveDate}
                                            </div>
                                        )}
                                        <div>
                                            <Button size="small" onClick={handleEditExpenseDepartment}>
                                                編輯
                                            </Button>
                                            <Button size="small" danger style={{ marginLeft: 8 }} onClick={handleRemoveExpenseDepartment}>
                                                移除
                                            </Button>
                                        </div>
                                    </div>
                                ) : (
                                    <Button type="dashed" icon={<PlusOutlined />} onClick={handleAddExpenseDepartment}>
                                        設定開支部門異動
                                    </Button>
                                )}
                            </Card>
                        </Col>
                        <Col span={12}>
                            <Card size="small" style={{ textAlign: 'center' }}>
                                <div style={{ marginBottom: 12 }}>
                                    <ApartmentOutlined style={{ fontSize: 24, color: '#722ed1' }} />
                                    <div style={{ marginTop: 8, fontWeight: 'bold' }}>服務部門異動</div>
                                </div>
                                {serviceDepartmentChange && serviceDepartmentChange.serviceDepartmentName ? (
                                    <div>
                                        <Tag color="purple" style={{ marginBottom: 8 }}>
                                            {serviceDepartmentChange.serviceDepartmentName}
                                        </Tag>
                                        {serviceDepartmentChange.effectiveDate && (
                                            <div style={{ marginBottom: 8, fontSize: '12px', color: '#666' }}>
                                                生效日期：{serviceDepartmentChange.effectiveDate}
                                            </div>
                                        )}
                                        <div>
                                            <Button size="small" onClick={handleEditServiceDepartment}>
                                                編輯
                                            </Button>
                                            <Button size="small" danger style={{ marginLeft: 8 }} onClick={handleRemoveServiceDepartment}>
                                                移除
                                            </Button>
                                        </div>
                                    </div>
                                ) : (
                                    <Button type="dashed" icon={<PlusOutlined />} onClick={handleAddServiceDepartment}>
                                        設定服務部門異動
                                    </Button>
                                )}
                            </Card>
                        </Col>
                    </Row>
                </Form>
            </Modal>

            {/* 開支部門異動設定Modal */}
            <ExpenseDepartmentChangeModal
                open={isExpenseModalOpen}
                userId={userId}
                departmentOptions={departmentOptions}
                expenseDepartmentChange={expenseDepartmentChange}
                onOk={handleExpenseModalOk}
                onCancel={handleExpenseModalCancel}
                loading={modalLoading}
            />

            {/* 服務部門異動設定Modal */}
            <ServiceDepartmentChangeModal
                open={isServiceModalOpen}
                userId={userId}
                departmentOptions={departmentOptions}
                divisionOptions={divisionOptions}
                serviceDepartmentChange={serviceDepartmentChange}
                onOk={handleServiceModalOk}
                onCancel={handleServiceModalCancel}
                loading={modalLoading}
            />

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={() => handleDelete(deleteUid)}
                    onCancel={handleDeleteCancel}
                />
            )}
        </div>
    );
};

export default PromotionInfo;
