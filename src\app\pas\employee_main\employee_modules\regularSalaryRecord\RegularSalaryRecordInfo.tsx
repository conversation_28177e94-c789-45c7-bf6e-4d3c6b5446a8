import React, { useEffect, useState } from 'react';
import {
    Table,
    Button,
    Modal,
    Card,
    message,
    Typography,
    Tag,
    Tabs,
    Space,
    Statistic,
    Popconfirm,
    Empty
} from 'antd';
import {
    EmployeeRegularSalary,
    getEmployeeRegularSalaryList,
    deleteEmployeeRegularSalary
} from '@/services/pas/RegularSalary/EmployeeRegularSalaryService';
import RegularSalaryRecordEditor from '@/app/pas/employee_main/employee_modules/regularSalaryRecord/RegularSalaryRecordEditor';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import { PlusOutlined, EditOutlined, DeleteOutlined, ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

type EmployeeRegularSalaryPageProps = {
    userId: string;
    active: boolean;
    tabUpdateidx?: number;
};

const RegularSalaryRecordInfo: React.FC<EmployeeRegularSalaryPageProps> = ({ userId, active, tabUpdateidx }) => {
    // 狀態
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);

    // 資料
    const [salaryList, setSalaryList] = useState<EmployeeRegularSalary[]>([]);
    const [selectedSalaryUid, setSelectedSalaryUid] = useState<string | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [activeTab, setActiveTab] = useState<string>('addition');

    useEffect(() => {
        if (active) {
            fetchRegularSalaryList();
        }
    }, [active, userId, tabUpdateidx]);

    const fetchRegularSalaryList = async () => {
        setLoading(true);
        setErrorMsg('');
        try {
            const res = await getEmployeeRegularSalaryList(userId);
            if (res.success && res.data) {
                setSalaryList(res.data);
            } else {
                message.error(res.message || '載入失敗');
                setErrorMsg(res.message || '載入失敗');
            }
        } catch (err) {
            message.error('發生錯誤，無法載入');
            setErrorMsg('發生錯誤，無法載入');
        } finally {
            setLoading(false);
        }
    };

    const handleAddNew = () => {
        setSelectedSalaryUid(null);
        setIsModalOpen(true);
    };

    const handleRowClick = (uid: string) => {
        setSelectedSalaryUid(uid);
        setIsModalOpen(true);
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteEmployeeRegularSalary(uid);
            if (res.success) {
                message.success('刪除成功');
                fetchRegularSalaryList();
            } else {
                message.error(res.message || '刪除失敗');
            }
        } catch {
            message.error('刪除時發生錯誤');
        } finally {
            setDeleteUid(null);
        }
    };

    const handleFormSuccess = () => {
        setIsModalOpen(false);
        fetchRegularSalaryList();
    };

    // 分類薪資項目
    const additionItems = salaryList.filter(item => item.itemType === '1');
    const deductionItems = salaryList.filter(item => item.itemType === '2');

    // 計算總額
    const additionTotal = additionItems.reduce((sum, item) => sum + Number(item.amount), 0);
    const deductionTotal = deductionItems.reduce((sum, item) => sum + Number(item.amount), 0);
    const netTotal = additionTotal - deductionTotal;

    const renderSalaryTable = (items: EmployeeRegularSalary[]) => (
        <Table
            rowKey="uid"
            dataSource={items}
            columns={[
                {
                    title: '薪資項目',
                    dataIndex: 'salaryItemName',
                    render: (text) => (
                        <Text strong style={{ fontSize: '15px' }}>{text}</Text>
                    ),
                },
                {
                    title: '金額',
                    dataIndex: 'amount',
                    align: 'right' as const,
                    render: (amount) => (
                        <Text style={{ fontSize: '15px', color: Number(amount) >= 0 ? '#3f8600' : '#cf1322' }}>
                            {Number(amount).toLocaleString()}
                        </Text>
                    ),
                },
                {
                    title: '狀態',
                    dataIndex: 'isEnable',
                    render: (isEnable) => (
                        <Tag
                            color={isEnable ? 'success' : 'error'}
                            style={{
                                padding: '4px 12px',
                                borderRadius: '12px',
                                background: isEnable ? 'rgba(82, 196, 26, 0.1)' : 'rgba(255, 77, 79, 0.1)'
                            }}
                        >
                            {isEnable ? '啟用' : '停用'}
                        </Tag>
                    ),
                },
                {
                    title: '操作',
                    render: (_, record) => (
                        <Space onClick={(e) => e.stopPropagation()}>
                            <Button
                                type="text"
                                icon={<EditOutlined />}
                                onClick={() => handleRowClick(record.uid)}
                            >
                                編輯
                            </Button>
                            <Popconfirm
                                title={
                                    <div>
                                        <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                        <Text>確定要刪除此筆資料嗎？</Text>
                                    </div>
                                }
                                onConfirm={() => setDeleteUid(record.uid)}
                                cancelText="取消"
                                okText="確認"
                                okButtonProps={{ danger: true }}
                            >
                                <Button
                                    type="text"
                                    danger
                                    icon={<DeleteOutlined />}
                                >
                                    刪除
                                </Button>
                            </Popconfirm>
                        </Space>
                    ),
                }
            ]}
            expandable={{
                expandedRowRender: (record) => (
                    <div style={{
                        padding: '16px 24px',
                        background: 'rgba(0, 0, 0, 0.02)',
                        borderRadius: '8px',
                        margin: '0 24px'
                    }}>
                        <Space>
                            <InfoCircleOutlined style={{ color: '#1890ff' }} />
                            <Text strong>備註：</Text>
                        </Space>
                        <div style={{ marginTop: 8, color: 'rgba(0, 0, 0, 0.65)' }}>
                            {record.remark || '無'}
                        </div>
                    </div>
                ),
                rowExpandable: (record) => !!record.remark,
            }}
            onRow={(record) => ({
                onClick: () => handleRowClick(record.uid),
                className: record.uid === deleteUid ? 'row-deleting-pulse' : '',
            })}
            pagination={false}
            footer={() => (
                <div style={{
                    textAlign: 'right',
                    padding: '16px 24px',
                    background: '#fafafa',
                    borderRadius: '0 0 8px 8px'
                }}>
                    <Text strong style={{ fontSize: '16px' }}>
                        小計：
                        <span style={{
                            color: items.reduce((sum, item) => sum + Number(item.amount), 0) >= 0 ? '#3f8600' : '#cf1322',
                            marginLeft: 8
                        }}>
                            {items.reduce((sum, item) => sum + Number(item.amount), 0).toLocaleString()} 元
                        </span>
                    </Text>
                </div>
            )}
            locale={{
                emptyText: <Empty description="暫無資料" />
            }}
            style={{ marginTop: 16 }}
        />
    );

    if (!active) return null;
    if (errorMsg) return (
        <div style={{
            color: '#ff4d4f',
            textAlign: 'center',
            padding: '40px 20px',
            background: '#fff1f0',
            borderRadius: '8px',
            border: '1px solid #ffccc7'
        }}>
            <ExclamationCircleOutlined style={{ marginRight: 8 }} />
            錯誤：{errorMsg}
        </div>
    );

    return (
        <>
            <Card
                title={<Title level={4} style={{ margin: 0 }}>常態性薪資管理</Title>}
                loading={loading}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddNew}
                        style={{ borderRadius: '6px' }}
                    >
                        新增常態性薪資
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <div style={{
                    marginBottom: 24,
                    padding: '24px',
                    background: '#fafafa',
                    borderRadius: '8px'
                }}>
                    <Space size={48}>
                        <Statistic
                            title={<Text strong>加項總額</Text>}
                            value={additionTotal}
                            precision={0}
                            prefix="$"
                            valueStyle={{
                                color: '#3f8600',
                                fontSize: '24px'
                            }}
                        />
                        <Statistic
                            title={<Text strong>減項總額</Text>}
                            value={deductionTotal}
                            precision={0}
                            prefix="$"
                            valueStyle={{
                                color: '#cf1322',
                                fontSize: '24px'
                            }}
                        />
                        <Statistic
                            title={<Text strong>淨額</Text>}
                            value={netTotal}
                            precision={0}
                            prefix="$"
                            valueStyle={{
                                color: netTotal >= 0 ? '#3f8600' : '#cf1322',
                                fontSize: '28px',
                                fontWeight: 'bold'
                            }}
                        />
                    </Space>
                </div>

                <Tabs
                    activeKey={activeTab}
                    onChange={setActiveTab}
                    type="card"
                    items={[
                        {
                            key: 'addition',
                            label: (
                                <Space>
                                    加項
                                    <Tag color="blue" style={{ marginLeft: 8, borderRadius: '10px' }}>
                                        {additionItems.length}
                                    </Tag>
                                </Space>
                            ),
                            children: renderSalaryTable(additionItems)
                        },
                        {
                            key: 'deduction',
                            label: (
                                <Space>
                                    減項
                                    <Tag color="orange" style={{ marginLeft: 8, borderRadius: '10px' }}>
                                        {deductionItems.length}
                                    </Tag>
                                </Space>
                            ),
                            children: renderSalaryTable(deductionItems)
                        }
                    ]}
                />
            </Card>

            <Modal
                title={
                    <Title level={5} style={{ margin: 0 }}>
                        {selectedSalaryUid ? '編輯常態性薪資' : '新增常態性薪資'}
                    </Title>
                }
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                footer={null}
                width={720}
                centered
                maskClosable={false}
                destroyOnClose
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    content: {
                        padding: '24px'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <RegularSalaryRecordEditor
                    userId={userId}
                    regularsalaryItemUid={selectedSalaryUid || undefined}
                    onSuccess={handleFormSuccess}
                    onCancel={() => setIsModalOpen(false)}
                />
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </>
    );
};

export default RegularSalaryRecordInfo; 