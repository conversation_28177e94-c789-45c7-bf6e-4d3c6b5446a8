import { useEffect, useState } from 'react';
import { Modal, Button, Spin, message, Table, Form, Input, DatePicker, Popconfirm, Row, Col, Card, Typography, Space, Divider } from 'antd';
import dayjs from 'dayjs';
import {
    getUndergoDetail,
    getUndergoList,
    addUndergo,
    editUndergo,
    deleteUndergo,
    Undergo,
    createEmptyUndergo,
} from '@/services/pas/UndergoService';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    BankOutlined,
    TeamOutlined,
    CalendarOutlined,
    FileTextOutlined,
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    PlusOutlined
} from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;

type UndergoInfoProps = {
    userId: string;
    active: boolean;
    tabUpdateidx?: number;
};

const UndergoInfo: React.FC<UndergoInfoProps> = ({ userId, active, tabUpdateidx }) => {
    const [list, setList] = useState<Undergo[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [detail, setDetail] = useState<Undergo | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [form] = Form.useForm();

    useEffect(() => {
        if (active) {
            fetchUndergoList();
        }
    }, [active, userId, tabUpdateidx]);

    const fetchUndergoList = async () => {
        setLoading(true);
        try {
            const res = await getUndergoList(userId);
            if (res.success && res.data) setList(res.data);
            else message.error(res.message || '載入失敗');
        } catch (err: any) {
            setErrorMsg(err.message || '未知錯誤');
        } finally {
            setLoading(false);
        }
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        try {
            const res = await getUndergoDetail(uid);
            if (res.success && res.data) {
                const d = res.data;
                form.resetFields();
                form.setFieldsValue({
                    ...d,
                    hireDate: d.hireDate ? dayjs(d.hireDate) : null,
                    terminationDate: d.terminationDate ? dayjs(d.terminationDate) : null,
                    certificateDate: d.certificateDate ? dayjs(d.certificateDate) : null,
                });
                setDetail(d);
                setIsModalOpen(true);
            } else message.error(res.message || '載入失敗');
        } catch (err: any) {
            message.error(err.message || '錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleAddNew = () => {
        form.resetFields();
        form.setFieldsValue(createEmptyUndergo());
        setDetail(null);
        setIsModalOpen(true);
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            const payload: Undergo = {
                ...values,
                userId,
                hireDate: values.hireDate ? values.hireDate.format('YYYY-MM') : '',
                terminationDate: values.terminationDate ? values.terminationDate.format('YYYY-MM') : '',
                certificateDate: values.certificateDate ? values.certificateDate.format('YYYY-MM-DD') : '',
            };

            setModalLoading(true);
            const res = detail
                ? await editUndergo({ ...payload, uid: detail.uid })
                : await addUndergo(payload);

            if (res.success && res.data?.result) {
                message.success(detail ? '更新成功' : '新增成功');
                setIsModalOpen(false);
                fetchUndergoList();
            } else {
                message.error(res.data?.msg || res.message || '儲存失敗');
            }
        } catch (err: any) {
            if (!err?.errorFields) message.error(err.message || '儲存錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteUndergo(uid);
            if (res.success && res.data?.result) {
                message.success('刪除成功');
                fetchUndergoList();
            } else {
                message.error(res.data?.msg || '刪除失敗');
            }
        } catch (err: any) {
            message.error(err.message || '刪除錯誤');
        }
    };

    if (!active) return null;
    if (errorMsg) return (
        <div style={{
            color: '#ff4d4f',
            textAlign: 'center',
            padding: '40px 20px',
            background: '#fff1f0',
            borderRadius: '8px',
            border: '1px solid #ffccc7'
        }}>
            <ExclamationCircleOutlined style={{ marginRight: 8 }} />
            錯誤：{errorMsg}
        </div>
    );

    return (
        <>
            <Card
                title={<Title level={4} style={{ margin: 0 }}>經歷資料</Title>}
                loading={loading}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddNew}
                        style={{ borderRadius: '6px' }}
                    >
                        新增經歷資料
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <Table
                    rowKey="uid"
                    dataSource={list}
                    pagination={{ pageSize: 10 }}
                    onRow={(record) => ({
                        onClick: () => handleRowClick(record.uid),
                    })}
                    rowClassName={(record) =>
                        record.uid === deleteUid ? 'row-deleting-pulse' : ''
                    }
                    columns={[
                        {
                            title: '服務機關',
                            dataIndex: 'agencyName',
                            render: (text) => (
                                <Space>
                                    <BankOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '部門',
                            dataIndex: 'departmentName',
                            render: (text) => (
                                <Space>
                                    <TeamOutlined style={{ color: '#52c41a' }} />
                                    <Text>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '職稱',
                            dataIndex: 'jobTitle',
                            render: (text) => <Text>{text}</Text>
                        },
                        {
                            title: '服務起訖',
                            render: (_, record) => {
                                const hire = record.hireDate ? dayjs(record.hireDate).format('YYYY-MM') : '-';
                                const term = record.terminationDate ? dayjs(record.terminationDate).format('YYYY-MM') : '-';
                                return (
                                    <Space>
                                        <CalendarOutlined style={{ color: '#722ed1' }} />
                                        <Text>{`${hire} ~ ${term}`}</Text>
                                    </Space>
                                );
                            },
                        },
                        {
                            title: '操作',
                            render: (_, record) => (
                                <Space onClick={(e) => e.stopPropagation()}>
                                    <Button
                                        type="text"
                                        icon={<EditOutlined />}
                                        onClick={() => handleRowClick(record.uid)}
                                    >
                                        編輯
                                    </Button>
                                    <Popconfirm
                                        title={
                                            <div>
                                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                                <Text>確定要刪除此筆資料嗎？</Text>
                                            </div>
                                        }
                                        onConfirm={() => setDeleteUid(record.uid)}
                                        okText="確認"
                                        cancelText="取消"
                                        okButtonProps={{ danger: true }}
                                    >
                                        <Button
                                            type="text"
                                            danger
                                            icon={<DeleteOutlined />}
                                        >
                                            刪除
                                        </Button>
                                    </Popconfirm>
                                </Space>
                            ),
                        },
                    ]}
                    expandable={{
                        expandedRowRender: (record) => (
                            <div style={{
                                padding: '16px 24px',
                                background: 'rgba(0, 0, 0, 0.02)',
                                borderRadius: '8px',
                                margin: '0 24px'
                            }}>
                                <Space>
                                    <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>備註：</Text>
                                </Space>
                                <div style={{ marginTop: 8, color: 'rgba(0, 0, 0, 0.65)' }}>
                                    {record.remark || '無'}
                                </div>
                            </div>
                        ),
                        rowExpandable: (record) => !!record.remark,
                    }}
                />
            </Card>

            <Modal
                title={
                    <Title level={5} style={{ margin: 0 }}>
                        {detail ? '編輯歷任經歷資料' : '新增歷任經歷資料'}
                    </Title>
                }
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                onOk={handleModalOk}
                confirmLoading={modalLoading}
                width={720}
                centered
                maskClosable={false}
                destroyOnClose
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <Form
                    layout="vertical"
                    form={form}
                    className="mt-4"
                >
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="agencyName"
                                label={<Text strong>服務機關</Text>}
                                rules={[{ required: true, message: '請輸入服務機關' }]}
                            >
                                <Input placeholder="請輸入服務機關名稱" />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="departmentName"
                                label={<Text strong>部門</Text>}
                                rules={[{ required: true, message: '請輸入部門' }]}
                            >
                                <Input placeholder="請輸入部門名稱" />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="jobTitle"
                                label={<Text strong>職稱</Text>}
                            >
                                <Input placeholder="請輸入職稱名稱" />
                            </Form.Item>
                        </Col>

                        <Col span={12}>
                            <Form.Item
                                name="duty"
                                label={<Text strong>職務</Text>}
                            >
                                <Input placeholder="請輸入職務名稱" />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="jobGrade"
                                label={<Text strong>薪級</Text>}
                            >
                                <Input placeholder="請輸入薪級" />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="supervisorName"
                                label={<Text strong>主管姓名</Text>}
                            >
                                <Input placeholder="請輸入主管姓名" />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="hireDate"
                                label={<Text strong>到職日期</Text>}
                                rules={[{ required: true, message: '請選擇到職日期' }]}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    format="YYYY-MM"
                                    picker="month"
                                    placeholder="請選擇到職日期"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="terminationDate"
                                label={<Text strong>離職日期</Text>}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    format="YYYY-MM"
                                    picker="month"
                                    placeholder="請選擇離職日期"
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item
                        name="remark"
                        label={<Text strong>備註</Text>}
                    >
                        <Input.TextArea
                            rows={4}
                            placeholder="請輸入備註內容"
                            style={{ borderRadius: '6px' }}
                        />
                    </Form.Item>
                </Form>
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </>
    );
};

export default UndergoInfo;
