using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Server.Tools;

namespace FAST_ERP_Backend.Services.Pas
{
    public class ExpenseDepartmentChangeService : IExpenseDepartmentChangeService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public ExpenseDepartmentChangeService(
            ERPDbContext context,
            Baseform baseform,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<ExpenseDepartmentChangeDTO>> GetExpenseDepartmentChangeListAsync(string userId)
        {
            var expenseChanges = await _context.Pas_ExpenseDepartmentChange
                .Where(e => e.UserId == userId && !e.IsDeleted)
                .OrderByDescending(e => e.CreateTime)
                .ToListAsync();

            var result = new List<ExpenseDepartmentChangeDTO>();

            foreach (var change in expenseChanges)
            {
                var dto = new ExpenseDepartmentChangeDTO
                {
                    Uid = change.Uid,
                    UserId = change.UserId,
                    ExpenseDepartmentId = change.ExpenseDepartmentId,
                    ExpenseDepartmentName = GetDepartmentName(change.ExpenseDepartmentId),
                    ChangeDate = _baseform.TimestampToDateStr(change.ChangeDate),
                    EffectiveDate = _baseform.TimestampToDateStr(change.EffectiveDate),
                    ChangeReason = change.ChangeReason,
                    Remark = change.Remark,
                    UpdateTime = change.UpdateTime
                };

                result.Add(dto);
            }

            return result;
        }

        public async Task<ExpenseDepartmentChangeDTO> GetExpenseDepartmentChangeDetailAsync(string uid)
        {
            var expenseChange = await _context.Pas_ExpenseDepartmentChange
                .Where(e => e.Uid == uid && !e.IsDeleted)
                .FirstOrDefaultAsync();

            if (expenseChange == null)
                return null;

            return new ExpenseDepartmentChangeDTO
            {
                Uid = expenseChange.Uid,
                UserId = expenseChange.UserId,
                ExpenseDepartmentId = expenseChange.ExpenseDepartmentId,
                ExpenseDepartmentName = GetDepartmentName(expenseChange.ExpenseDepartmentId),
                ChangeDate = _baseform.TimestampToDateStr(expenseChange.ChangeDate),
                EffectiveDate = _baseform.TimestampToDateStr(expenseChange.EffectiveDate),
                ChangeReason = expenseChange.ChangeReason,
                Remark = expenseChange.Remark,
                UpdateTime = expenseChange.UpdateTime
            };
        }

        public async Task<string> AddExpenseDepartmentChangeAsync(ExpenseDepartmentChangeDTO data, string userId)
        {
            var uid = Guid.NewGuid().ToString().Trim();

            var newExpenseChange = new ExpenseDepartmentChange
            {
                Uid = uid,
                UserId = userId,
                ExpenseDepartmentId = data.ExpenseDepartmentId ?? "",
                ChangeDate = _baseform.DateStrToTimestamp(data.ChangeDate ?? ""),
                EffectiveDate = _baseform.DateStrToTimestamp(data.EffectiveDate ?? ""),
                ChangeReason = data.ChangeReason ?? "",
                Remark = data.Remark ?? "",
                CreateTime = _baseform.GetCurrentLocalTimestamp(),
                CreateUserId = _currentUserService.UserId,
            };

            await _context.Pas_ExpenseDepartmentChange.AddAsync(newExpenseChange);
            await _context.SaveChangesAsync();

            return uid;
        }

        public async Task UpdateExpenseDepartmentChangeAsync(ExpenseDepartmentChangeDTO data, string uid)
        {
            var existing = await _context.Pas_ExpenseDepartmentChange
                .FirstOrDefaultAsync(e => e.Uid == uid && !e.IsDeleted);

            if (existing != null)
            {
                existing.ExpenseDepartmentId = data.ExpenseDepartmentId ?? "";
                existing.ChangeDate = _baseform.DateStrToTimestamp(data.ChangeDate ?? "");
                existing.EffectiveDate = _baseform.DateStrToTimestamp(data.EffectiveDate ?? "");
                existing.ChangeReason = data.ChangeReason ?? "";
                existing.Remark = data.Remark ?? "";
                existing.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                existing.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
            }
        }

        public async Task DeleteExpenseDepartmentChangeAsync(string uid)
        {
            var existing = await _context.Pas_ExpenseDepartmentChange
                .FirstOrDefaultAsync(e => e.Uid == uid && !e.IsDeleted);

            if (existing != null)
            {
                existing.IsDeleted = true;
                existing.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                existing.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
            }
        }

        private string GetDepartmentName(string departmentId)
        {
            if (string.IsNullOrEmpty(departmentId))
                return "";

            var department = _context.Common_Departments
                .FirstOrDefault(d => d.DepartmentId == departmentId && !d.IsDeleted);

            return department?.Name ?? departmentId;
        }
    }
}