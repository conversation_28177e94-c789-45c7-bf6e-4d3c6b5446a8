import { useEffect, useState } from 'react';
import { Employee, getEmployeeDetail } from '@/services/pas/EmployeeService';
import {
    Button,
    message,
    Card,
    Row,
    Col,
    Descriptions,
    Alert,
    Space,
    Tag,
    Typography,
    Avatar,
} from 'antd';
import {
    UserOutlined,
    CalendarOutlined,
    IdcardOutlined,
    TeamOutlined,
    BankOutlined,
    PhoneOutlined,
    EditOutlined
} from '@ant-design/icons';
import EditEmployeeForm from './EditEmployeeForm';

const { Text } = Typography;

type EmployeeInfoProps = {
    userId: string;
    active: boolean;
    tabUpdateidx: number;
    onEditSuccess?: () => void;
};

const EmployeeInfo: React.FC<EmployeeInfoProps> = ({ userId, active, tabUpdateidx, onEditSuccess }) => {
    const [employeeDetail, setEmployeeDetail] = useState<Employee | null>(null);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [editVisible, setEditVisible] = useState(false);

    useEffect(() => {
        if (active) {
            fetchEmployeeDetail(userId);
        }
    }, [active, userId, tabUpdateidx]);

    const fetchEmployeeDetail = async (userId: string) => {
        if (userId === '') {
            setEmployeeDetail(null);
            return;
        }

        setLoading(true);
        setErrorMsg('');

        try {
            const response = await getEmployeeDetail(userId);
            if (response.success) {
                if (response.data) {
                    setEmployeeDetail(response.data);
                } else {
                    setEmployeeDetail(null);
                    message.info('查無資料');
                }
            } else {
                setErrorMsg(response.message || '取得員工明細資料失敗');
                message.error(response.message || '取得員工明細資料失敗');
            }
        } catch (err: any) {
            setErrorMsg(err.message || '未知錯誤');
            message.error(err.message || '載入失敗');
        } finally {
            setLoading(false);
        }
    };

    const handleEditSuccess = () => {
        setEditVisible(false);
        if (onEditSuccess) {
            onEditSuccess();
        }
        fetchEmployeeDetail(userId);
    };

    if (!active) return null;

    if (errorMsg) {
        return (
            <Alert
                message="錯誤"
                description={errorMsg}
                type="error"
                showIcon
                style={{ margin: '20px' }}
            />
        );
    }

    return (
        <>
            <Card
                className="employee-info-card"
                loading={loading}
                title={
                    <Space>
                        <UserOutlined />
                        <span>員工資料</span>
                    </Space>
                }
                extra={
                    (employeeDetail && employeeDetail?.userId !== "") && (
                        <Button
                            type="primary"
                            icon={<EditOutlined />}
                            onClick={() => setEditVisible(true)}
                        >
                            編輯
                        </Button>
                    )
                }
            >
                {(employeeDetail && employeeDetail?.userId !== "") ? (
                    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                        {/* 基本資料 */}
                        <Card
                            size="small"
                            title={<Space><IdcardOutlined />基本資料</Space>}
                            bordered={false}
                            style={{ background: '#fafafa' }}
                        >
                            <Row gutter={[16, 16]} align="middle">
                                <Col span={24}>
                                    <Space align="center" size="large">
                                        <Avatar size={64} icon={<UserOutlined />} />
                                        <Space direction="vertical" size={4}>
                                            <Text strong style={{ fontSize: '16px' }}>{employeeDetail.usersDTO.name}</Text>
                                            <Space>
                                                <Tag color="blue">員編:{employeeDetail.empNo}</Tag>
                                                <Tag color="green">{
                                                    employeeDetail.currentPromotion?.serviceDepartmentChange ?
                                                        (employeeDetail.currentPromotion.serviceDepartmentChange.serviceDepartmentName +
                                                            (employeeDetail.currentPromotion.serviceDepartmentChange.serviceDivisionName ?
                                                                ` (${employeeDetail.currentPromotion.serviceDepartmentChange.serviceDivisionName})` : '')) :
                                                        ''
                                                }</Tag>
                                                <Tag color="gold">{employeeDetail.currentPromotion?.jobTitleName}</Tag>
                                            </Space>
                                        </Space>
                                    </Space>
                                </Col>
                                <Col span={24}>
                                    <Descriptions column={2} size="small">
                                        <Descriptions.Item label="身分證字號">
                                            <Text strong>{employeeDetail.idNo}</Text>
                                        </Descriptions.Item>
                                        <Descriptions.Item label="證號別">
                                            <Text strong>{employeeDetail.idTypeName}</Text>
                                        </Descriptions.Item>
                                        <Descriptions.Item label="帳號">
                                            <Text strong>{employeeDetail.usersDTO.account}</Text>
                                        </Descriptions.Item>
                                        <Descriptions.Item label="錯誤註記">
                                            <Text strong>{employeeDetail.errorMarkName}</Text>
                                        </Descriptions.Item>
                                    </Descriptions>
                                </Col>
                            </Row>
                        </Card>

                        {/* 職務資訊 */}
                        <Card
                            size="small"
                            title={<Space><TeamOutlined />職務資訊</Space>}
                            bordered={false}
                            style={{ background: '#fafafa' }}
                        >
                            <Descriptions column={2} size="small">
                                <Descriptions.Item label="服務部門">
                                    <Text strong>{
                                        employeeDetail.currentPromotion?.serviceDepartmentChange ?
                                            employeeDetail.currentPromotion.serviceDepartmentChange.serviceDepartmentName : ''
                                    }</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="組別">
                                    <Text strong>{
                                        employeeDetail.currentPromotion?.serviceDepartmentChange ?
                                            employeeDetail.currentPromotion.serviceDepartmentChange.serviceDivisionName : ''
                                    }</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="任用資格">
                                    <Text strong>{employeeDetail.currentPromotion?.jobroleTypeName}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="錄用類別">
                                    <Text strong>{employeeDetail.currentPromotion?.categoryTypeName}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="職稱">
                                    <Text strong>{employeeDetail.currentPromotion?.jobTitleName}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="職務">
                                    <Text strong>{employeeDetail.usersDTO.positionName}</Text>
                                </Descriptions.Item>
                            </Descriptions>
                        </Card>

                        {/* 日期資訊 */}
                        <Card
                            size="small"
                            title={<Space><CalendarOutlined />日期資訊</Space>}
                            bordered={false}
                            style={{ background: '#fafafa' }}
                        >
                            <Descriptions column={2} size="small">
                                <Descriptions.Item label="到職日">
                                    <Text strong>{employeeDetail.hireDate}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="正式任用日">
                                    <Text strong>{employeeDetail.officialHireDate}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="試用日">
                                    <Text strong>{employeeDetail.probStartDate}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="轉任日">
                                    <Text strong>{employeeDetail.transferDate}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="勞保加保日">
                                    <Text strong>{employeeDetail.laborInsStartDate}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="健保加保日">
                                    <Text strong>{employeeDetail.healthInsStartDate}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="離職日">
                                    <Text strong>{employeeDetail.leaveDate}</Text>
                                </Descriptions.Item>
                            </Descriptions>
                        </Card>

                        {/* 聯絡資訊 */}
                        <Card
                            size="small"
                            title={<Space><PhoneOutlined />聯絡資訊</Space>}
                            bordered={false}
                            style={{ background: '#fafafa' }}
                        >
                            <Descriptions column={2} size="small">
                                <Descriptions.Item label="Email">
                                    <Text strong>{employeeDetail.usersDTO.eMail}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="電話">
                                    <Text strong>{employeeDetail.usersDTO.telNo}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="手機">
                                    <Text strong>{employeeDetail.usersDTO.phone}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="備用電話">
                                    <Text strong>{employeeDetail.usersDTO.altPhone}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="戶籍地址" span={2}>
                                    <Text strong>{employeeDetail.usersDTO.permanentAddress}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="通訊地址" span={2}>
                                    <Text strong>{employeeDetail.usersDTO.mailingAddress}</Text>
                                </Descriptions.Item>
                            </Descriptions>
                        </Card>

                        {/* 個人資訊 */}
                        <Card
                            size="small"
                            title={<Space><UserOutlined />個人資訊</Space>}
                            bordered={false}
                            style={{ background: '#fafafa' }}
                        >
                            <Descriptions column={2} size="small">
                                <Descriptions.Item label="生日">
                                    <Text strong>{employeeDetail.birthday}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="血型">
                                    <Text strong>{employeeDetail.bloodTypeName}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="配偶姓名">
                                    <Text strong>{employeeDetail.spouseName}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="配偶身分證">
                                    <Text strong>{employeeDetail.spouseIdNo}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="備註" span={2}>
                                    <div style={{ whiteSpace: 'pre-wrap' }}>
                                        <Text strong>{employeeDetail.remark}</Text>
                                    </div>
                                </Descriptions.Item>
                            </Descriptions>
                        </Card>

                        {/* 系統資訊 */}
                        <Card
                            size="small"
                            title={<Space><BankOutlined />系統資訊</Space>}
                            bordered={false}
                            style={{ background: '#fafafa' }}
                        >
                            <Descriptions column={2} size="small">
                                <Descriptions.Item label="登錄者">
                                    <Text type="secondary">{employeeDetail.createUserId}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="最後更新時間">
                                    <Text type="secondary">{employeeDetail.updateTime}</Text>
                                </Descriptions.Item>
                            </Descriptions>
                        </Card>
                    </Space>
                ) : (
                    <Alert
                        message="提示"
                        description="請先選取員工，或尚未建立員工資料"
                        type="info"
                        showIcon
                        style={{ margin: '20px' }}
                    />
                )}
            </Card>

            <EditEmployeeForm
                userId={userId}
                mode="edit"
                visible={editVisible}
                onSuccess={handleEditSuccess}
                onCancel={() => setEditVisible(false)}
            />
        </>
    );
};

export default EmployeeInfo;
