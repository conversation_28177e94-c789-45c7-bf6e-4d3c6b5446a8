using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("服務部門異動資料管理")]
    public class ServiceDepartmentChangeController : ControllerBase
    {
        private readonly IServiceDepartmentChangeService _serviceDepartmentChangeService;

        public ServiceDepartmentChangeController(IServiceDepartmentChangeService serviceDepartmentChangeService)
        {
            _serviceDepartmentChangeService = serviceDepartmentChangeService;
        }

        [HttpGet]
        [Route("GetAll/{userId}")]
        [SwaggerOperation(Summary = "取得服務部門異動列表", Description = "取得指定使用者的所有服務部門異動資料列表")]
        public async Task<IActionResult> GetServiceDepartmentChangeList(string userId)
        {
            try
            {
                var result = await _serviceDepartmentChangeService.GetServiceDepartmentChangeListAsync(userId);
                return Ok(new { success = true, message = "取得服務部門異動列表成功", data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpGet]
        [Route("Get/{uid}")]
        [SwaggerOperation(Summary = "取得服務部門異動明細", Description = "依服務部門異動編號取得明細資料")]
        public async Task<IActionResult> GetServiceDepartmentChangeDetail(string uid)
        {
            try
            {
                var result = await _serviceDepartmentChangeService.GetServiceDepartmentChangeDetailAsync(uid);
                if (result == null)
                {
                    return NotFound(new { success = false, message = "找不到指定的服務部門異動資料" });
                }
                return Ok(new { success = true, message = "取得服務部門異動明細成功", data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增服務部門異動", Description = "新增服務部門異動資料")]
        public async Task<IActionResult> AddServiceDepartmentChange([FromForm] ServiceDepartmentChangeFormDTO formData)
        {
            try
            {
                var changeData = MapFormToDTO(formData);
                var uid = await _serviceDepartmentChangeService.AddServiceDepartmentChangeAsync(changeData, formData.UserId ?? "");
                return Ok(new { success = !string.IsNullOrEmpty(uid), message = !string.IsNullOrEmpty(uid) ? "新增成功" : "新增失敗", data = new { uid } });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯服務部門異動", Description = "編輯服務部門異動資料")]
        public async Task<IActionResult> EditServiceDepartmentChange([FromForm] ServiceDepartmentChangeFormDTO formData)
        {
            try
            {
                var changeData = MapFormToDTO(formData);
                await _serviceDepartmentChangeService.UpdateServiceDepartmentChangeAsync(changeData, formData.Uid ?? "");
                return Ok(new { success = true, message = "更新成功", data = (object?)null });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除服務部門異動", Description = "刪除服務部門異動資料")]
        public async Task<IActionResult> DeleteServiceDepartmentChange([FromBody] string uid)
        {
            try
            {
                await _serviceDepartmentChangeService.DeleteServiceDepartmentChangeAsync(uid);
                return Ok(new { success = true, message = "刪除成功", data = (object?)null });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        #region 私有方法

        private ServiceDepartmentChangeDTO MapFormToDTO(ServiceDepartmentChangeFormDTO formData)
        {
            return new ServiceDepartmentChangeDTO
            {
                ServiceDepartmentId = formData.ServiceDepartmentId ?? "",
                ServiceDivisionId = formData.ServiceDivisionId ?? "",
                ChangeDate = formData.ChangeDate ?? "",
                EffectiveDate = formData.EffectiveDate ?? "",
                ChangeReason = formData.ChangeReason ?? "",
                Remark = formData.Remark ?? ""
            };
        }

        #endregion
    }

    /// <summary>
    /// 服務部門異動表單DTO
    /// </summary>
    public class ServiceDepartmentChangeFormDTO
    {
        public string? Uid { get; set; }
        public string? UserId { get; set; }
        public string? ServiceDepartmentId { get; set; }
        public string? ServiceDivisionId { get; set; }
        public string? ChangeDate { get; set; }
        public string? EffectiveDate { get; set; }
        public string? ChangeReason { get; set; }
        public string? Remark { get; set; }
    }
}