using FAST_ERP_Backend.Models.Pas;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IServiceDepartmentChangeService
    {
        /// <summary>
        /// 取得使用者的服務部門異動列表
        /// </summary>
        /// <param name="userId">使用者編號</param>
        /// <returns>服務部門異動列表</returns>
        Task<List<ServiceDepartmentChangeDTO>> GetServiceDepartmentChangeListAsync(string userId);

        /// <summary>
        /// 取得服務部門異動明細
        /// </summary>
        /// <param name="uid">服務部門異動編號</param>
        /// <returns>服務部門異動明細</returns>
        Task<ServiceDepartmentChangeDTO> GetServiceDepartmentChangeDetailAsync(string uid);

        /// <summary>
        /// 新增服務部門異動資料
        /// </summary>
        /// <param name="data">服務部門異動資料</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>服務部門異動編號</returns>
        Task<string> AddServiceDepartmentChangeAsync(ServiceDepartmentChangeDTO data, string userId);

        /// <summary>
        /// 編輯服務部門異動資料
        /// </summary>
        /// <param name="data">服務部門異動資料</param>
        /// <param name="uid">服務部門異動編號</param>
        /// <returns>無回傳值</returns>
        Task UpdateServiceDepartmentChangeAsync(ServiceDepartmentChangeDTO data, string uid);

        /// <summary>
        /// 刪除服務部門異動資料
        /// </summary>
        /// <param name="uid">服務部門異動編號</param>
        /// <returns>無回傳值</returns>
        Task DeleteServiceDepartmentChangeAsync(string uid);
    }
}