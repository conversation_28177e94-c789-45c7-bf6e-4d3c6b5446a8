# IMS 舊版組件清理執行計劃

## 🎯 **清理目標**

移除 IMS 模組中已被統一架構替代的舊版組件，減少代碼冗餘和維護複雜度。

## 📋 **待清理組件清單**

### **高優先級 - 立即清理**

| 組件名稱 | 文件路徑 | 代碼行數 | 替代組件 | 狀態 |
|----------|----------|----------|----------|------|
| **CustomerCategoryManagement** | `src/app/ims/components/CustomerCategoryManagement.tsx` | 500+ 行 | CustomerCategoryAdapter | ⚠️ 待移除 |
| **SupplierCategoryManagement** | `src/app/ims/components/SupplierCategoryManagement.tsx` | 500+ 行 | SupplierCategoryAdapter | ⚠️ 待移除 |

### **清理效益**
- **減少代碼**：1000+ 行冗餘代碼
- **降低複雜度**：移除重複的分類管理邏輯
- **避免混淆**：防止開發者使用過時組件

## 🔍 **清理前驗證**

### **步驟 1：確認無外部引用**

```bash
# 檢查 CustomerCategoryManagement 的引用
echo "=== 檢查 CustomerCategoryManagement 引用 ==="
grep -r "CustomerCategoryManagement" src/app/ims/ --exclude-dir=node_modules
grep -r "from.*CustomerCategoryManagement" src/app/ims/ --exclude-dir=node_modules
grep -r "import.*CustomerCategoryManagement" src/app/ims/ --exclude-dir=node_modules

# 檢查 SupplierCategoryManagement 的引用
echo "=== 檢查 SupplierCategoryManagement 引用 ==="
grep -r "SupplierCategoryManagement" src/app/ims/ --exclude-dir=node_modules
grep -r "from.*SupplierCategoryManagement" src/app/ims/ --exclude-dir=node_modules
grep -r "import.*SupplierCategoryManagement" src/app/ims/ --exclude-dir=node_modules
```

### **步驟 2：確認適配器組件正常工作**

```bash
# 檢查適配器組件是否正確使用
echo "=== 檢查適配器組件使用情況 ==="
grep -r "CustomerCategoryAdapter" src/app/ims/
grep -r "SupplierCategoryAdapter" src/app/ims/
```

### **步驟 3：功能測試**

**測試清單：**
- [ ] Partner 頁面客戶分類管理功能
- [ ] Partner 頁面供應商分類管理功能
- [ ] 分類新增功能
- [ ] 分類編輯功能
- [ ] 分類刪除功能
- [ ] 分類樹狀結構顯示
- [ ] 響應式設計

## 🗑️ **執行清理**

### **清理命令**

```bash
# 備份舊版組件（可選）
mkdir -p backup/legacy_components/$(date +%Y%m%d)
cp src/app/ims/components/CustomerCategoryManagement.tsx backup/legacy_components/$(date +%Y%m%d)/
cp src/app/ims/components/SupplierCategoryManagement.tsx backup/legacy_components/$(date +%Y%m%d)/

# 移除舊版組件
rm src/app/ims/components/CustomerCategoryManagement.tsx
rm src/app/ims/components/SupplierCategoryManagement.tsx

echo "✅ 舊版分類管理組件已清理完成"
```

### **清理後驗證**

```bash
# 確認文件已移除
echo "=== 確認文件已移除 ==="
ls -la src/app/ims/components/CustomerCategoryManagement.tsx 2>/dev/null || echo "✅ CustomerCategoryManagement.tsx 已移除"
ls -la src/app/ims/components/SupplierCategoryManagement.tsx 2>/dev/null || echo "✅ SupplierCategoryManagement.tsx 已移除"

# 確認無殘留引用
echo "=== 檢查是否有殘留引用 ==="
grep -r "CustomerCategoryManagement" src/app/ims/ --exclude-dir=node_modules || echo "✅ 無 CustomerCategoryManagement 引用"
grep -r "SupplierCategoryManagement" src/app/ims/ --exclude-dir=node_modules || echo "✅ 無 SupplierCategoryManagement 引用"
```

## 🧪 **測試計劃**

### **自動化測試**

```bash
# TypeScript 編譯檢查
npm run build

# ESLint 檢查
npm run lint

# 單元測試
npm run test
```

### **手動測試**

**測試場景：**

1. **客戶分類管理**
   - 開啟 Partner 頁面
   - 點擊「客戶分類管理」按鈕
   - 驗證分類樹正常顯示
   - 測試新增、編輯、刪除功能

2. **供應商分類管理**
   - 開啟 Partner 頁面
   - 點擊「供應商分類管理」按鈕
   - 驗證分類樹正常顯示
   - 測試新增、編輯、刪除功能

3. **響應式測試**
   - 在不同螢幕尺寸下測試
   - 確認移動端體驗正常

## 📊 **清理效益評估**

### **代碼減少量**

| 指標 | 清理前 | 清理後 | 減少量 |
|------|--------|--------|--------|
| **總代碼行數** | 1000+ 行 | 0 行 | ⬇️ 100% |
| **重複邏輯** | 高 | 無 | ⬇️ 100% |
| **維護複雜度** | 高 | 低 | ⬇️ 80% |

### **維護效益**

- **統一性**：所有分類管理使用相同的適配器模式
- **可維護性**：只需要維護一套核心邏輯
- **擴展性**：新增分類類型更加容易
- **一致性**：UI 和 UX 完全統一

## ⚠️ **風險評估**

### **低風險**
- 適配器組件已經過充分測試
- 功能完全等價
- 有完整的備份機制

### **緩解措施**
- 清理前進行完整的功能測試
- 保留備份文件
- 分階段執行清理
- 監控清理後的系統穩定性

## 📅 **執行時間表**

| 階段 | 任務 | 預估時間 | 負責人 |
|------|------|----------|--------|
| **準備階段** | 驗證和測試 | 30 分鐘 | 開發者 |
| **執行階段** | 清理組件 | 15 分鐘 | 開發者 |
| **驗證階段** | 功能測試 | 30 分鐘 | 開發者 |
| **總計** | | **75 分鐘** | |

## ✅ **完成檢查清單**

- [ ] 確認無外部引用
- [ ] 適配器組件功能正常
- [ ] 備份舊版組件
- [ ] 執行清理命令
- [ ] 驗證文件已移除
- [ ] TypeScript 編譯通過
- [ ] 功能測試通過
- [ ] 更新文檔

## 📝 **清理報告模板**

```markdown
# IMS 舊版組件清理報告

## 執行時間
- 開始時間：[YYYY-MM-DD HH:MM]
- 結束時間：[YYYY-MM-DD HH:MM]
- 執行人員：[姓名]

## 清理結果
- [x] CustomerCategoryManagement.tsx 已移除
- [x] SupplierCategoryManagement.tsx 已移除
- [x] 無殘留引用
- [x] 功能測試通過

## 效益統計
- 減少代碼：1000+ 行
- 降低複雜度：80%
- 提升一致性：100%

## 備註
[任何特殊情況或注意事項]
```

---

**執行此清理計劃將顯著提升 IMS 模組的代碼品質和維護性，為後續開發工作奠定更好的基礎。**
