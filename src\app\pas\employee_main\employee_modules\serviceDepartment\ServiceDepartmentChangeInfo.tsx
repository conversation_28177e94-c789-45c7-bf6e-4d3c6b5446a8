import { useEffect, useState } from 'react';
import { Button, message, Table, Card, Typography, Space, Row, Col, Select, Popconfirm, Tag, Tooltip } from 'antd';
import dayjs from 'dayjs';
import {
    EditOutlined,
    DeleteOutlined,
    PlusOutlined,
    ApartmentOutlined,
    TeamOutlined,
    FilterOutlined,
    QuestionCircleOutlined,
    UpOutlined
} from '@ant-design/icons';
import {
    getServiceDepartmentChangeList,
    getServiceDepartmentChangeDetail,
    addServiceDepartmentChange,
    editServiceDepartmentChange,
    deleteServiceDepartmentChange,
    createEmptyServiceDepartmentChange,
    createServiceDepartmentChangeFormData,
    type ServiceDepartmentChange
} from '@/services/pas/ServiceDepartmentChangeService';
import { getDepartments } from '@/services/common/departmentService';
import { getDivisions } from '@/services/common/divisionService';
import { getPromotionList } from '@/services/pas/PromotionService';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import ServiceDepartmentChangeModal from './ServiceDepartmentChangeModal';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;
const { Option } = Select;

type ServiceDepartmentChangeInfoProps = {
    userId: string;
    active: boolean;
    tabUpdateidx?: number;
};

const ServiceDepartmentChangeInfo: React.FC<ServiceDepartmentChangeInfoProps> = ({ userId, active, tabUpdateidx }) => {
    const [changeList, setChangeList] = useState<ServiceDepartmentChange[]>([]);
    const [filteredList, setFilteredList] = useState<ServiceDepartmentChange[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [changeDetail, setChangeDetail] = useState<ServiceDepartmentChange | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [deletingRows, setDeletingRows] = useState<Set<string>>(new Set());

    // 篩選狀態
    const [selectedSource, setSelectedSource] = useState<string | null>(null);
    const [selectedDepartment, setSelectedDepartment] = useState<string | null>(null);
    const [selectedDivision, setSelectedDivision] = useState<string | null>(null);

    // 選項資料
    const [departmentOptions, setDepartmentOptions] = useState<any[]>([]);
    const [divisionOptions, setDivisionOptions] = useState<any[]>([]);
    const [promotionList, setPromotionList] = useState<any[]>([]);

    useEffect(() => {
        if (active) {
            // 重設篩選條件
            setSelectedSource(null);
            setSelectedDepartment(null);
            setSelectedDivision(null);

            fetchChangeList();
            loadOptions();
        }
    }, [active, userId, tabUpdateidx]);

    // 當篩選條件改變時，重新篩選資料
    useEffect(() => {
        filterData();
    }, [changeList, selectedSource, selectedDepartment, selectedDivision]);

    const loadOptions = async () => {
        try {
            const [deptRes, divRes, promRes] = await Promise.all([
                getDepartments(),
                getDivisions(),
                getPromotionList(userId)
            ]);

            if (deptRes.success) setDepartmentOptions(deptRes.data || []);
            if (divRes.success) setDivisionOptions(divRes.data || []);
            if (promRes.success) setPromotionList(promRes.data || []);
        } catch (error) {
            console.error('載入選項資料失敗:', error);
        }
    };

    const fetchChangeList = async () => {
        setLoading(true);
        setErrorMsg('');
        try {
            const { success, data, message: msg } = await getServiceDepartmentChangeList(userId);
            if (success && data) {
                setChangeList(data);
            } else {
                message.error(msg || '載入服務部門異動資料失敗');
            }
        } catch (error: any) {
            setErrorMsg(error.message || '未知錯誤');
            message.error(error.message || '載入失敗');
        } finally {
            setLoading(false);
        }
    };

    const filterData = () => {
        let filtered = [...changeList];

        // 來源篩選（升遷異動產生 vs 直接新增）
        if (selectedSource) {
            if (selectedSource === 'promotion') {
                // 篩選出由升遷產生的異動
                filtered = filtered.filter(item => {
                    return promotionList.some(promotion =>
                        promotion.serviceDepartmentChangeUid === item.uid
                    );
                });
            } else if (selectedSource === 'direct') {
                // 篩選出直接新增的異動
                filtered = filtered.filter(item => {
                    return !promotionList.some(promotion =>
                        promotion.serviceDepartmentChangeUid === item.uid
                    );
                });
            }
        }

        // 部門篩選
        if (selectedDepartment) {
            filtered = filtered.filter(item => item.serviceDepartmentId === selectedDepartment);
        }

        // 組別篩選
        if (selectedDivision) {
            filtered = filtered.filter(item => item.serviceDivisionId === selectedDivision);
        }

        setFilteredList(filtered);
    };

    // 檢查部門異動是否由升遷產生
    const getRelatedPromotion = (changeUid: string) => {
        return promotionList.find(promotion =>
            promotion.serviceDepartmentChangeUid === changeUid
        );
    };

    const handleSourceChange = (value: string) => {
        setSelectedSource(value === 'all' ? null : value);
    };

    const handleDepartmentChange = (value: string) => {
        setSelectedDepartment(value === 'all' ? null : value);
    };

    const handleDivisionChange = (value: string) => {
        setSelectedDivision(value === 'all' ? null : value);
    };

    const clearFilters = () => {
        setSelectedSource(null);
        setSelectedDepartment(null);
        setSelectedDivision(null);
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        try {
            const { success, data, message: msg } = await getServiceDepartmentChangeDetail(uid);
            if (success && data) {
                setChangeDetail(data);
                setIsModalOpen(true);
            } else {
                message.error(msg || '載入服務部門異動資料失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '載入服務部門異動資料時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleModalOk = async (data: ServiceDepartmentChange) => {
        try {
            setModalLoading(true);

            // 轉換為FormData
            const formData = createServiceDepartmentChangeFormData(data);

            let res;
            if (changeDetail) {
                res = await editServiceDepartmentChange(formData);
            } else {
                res = await addServiceDepartmentChange(formData);
            }

            if (res.success) {
                message.success(changeDetail ? '更新成功' : '新增成功');
                setIsModalOpen(false);
                fetchChangeList();
            } else {
                throw new Error(res.message || '儲存失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '儲存時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleModalCancel = () => {
        setIsModalOpen(false);
        setChangeDetail(null);
    };

    const handleDeleteConfirm = (uid: string) => {
        // 檢查是否由升遷產生
        const relatedPromotion = getRelatedPromotion(uid);
        if (relatedPromotion) {
            message.error('此部門異動由升遷異動產生，無法直接刪除。請先刪除相關的升遷異動。');
            return;
        }

        setDeletingRows(prev => new Set(prev.add(uid)));
        setDeleteUid(uid);
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteServiceDepartmentChange(uid);
            if (res.success) {
                message.success('刪除成功');
                fetchChangeList();
            } else {
                throw new Error(res.message || '刪除失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '刪除時發生錯誤');
        } finally {
            setDeleteUid(null);
            setDeletingRows(prev => {
                const newSet = new Set(prev);
                newSet.delete(uid);
                return newSet;
            });
        }
    };

    const handleDeleteCancel = () => {
        if (deleteUid) {
            setDeletingRows(prev => {
                const newSet = new Set(prev);
                newSet.delete(deleteUid);
                return newSet;
            });
        }
        setDeleteUid(null);
    };

    const handleAddNew = () => {
        setChangeDetail(null);
        setIsModalOpen(true);
    };

    const columns = [
        {
            title: '服務部門',
            dataIndex: 'serviceDepartmentName',
            key: 'serviceDepartmentName',
            width: 200,
            render: (value: string, record: ServiceDepartmentChange) => value || record.serviceDepartmentId
        },
        {
            title: '服務組別',
            dataIndex: 'serviceDivisionName',
            key: 'serviceDivisionName',
            width: 150,
            render: (value: string, record: ServiceDepartmentChange) => value || record.serviceDivisionId || '-'
        },
        {
            title: '異動日期',
            dataIndex: 'changeDate',
            key: 'changeDate',
            width: 120,
        },
        {
            title: '生效日期',
            dataIndex: 'effectiveDate',
            key: 'effectiveDate',
            width: 120,
            sorter: (a: ServiceDepartmentChange, b: ServiceDepartmentChange) => {
                if (!a.effectiveDate && !b.effectiveDate) return 0;
                if (!a.effectiveDate) return 1;
                if (!b.effectiveDate) return -1;
                return dayjs(a.effectiveDate).valueOf() - dayjs(b.effectiveDate).valueOf();
            },
            defaultSortOrder: 'descend' as const,
            showSorterTooltip: false,
        },
        {
            title: '來源',
            key: 'source',
            width: 140,
            render: (text: any, record: ServiceDepartmentChange) => {
                const relatedPromotion = getRelatedPromotion(record.uid!);

                if (relatedPromotion) {
                    return (
                        <Tooltip title={`由升遷異動產生\n升遷日期：${relatedPromotion.promotionDate}\n生效日期：${relatedPromotion.effectiveDate}`}>
                            <Tag color="orange" icon={<UpOutlined />} style={{ cursor: 'help' }}>
                                升遷異動
                            </Tag>
                        </Tooltip>
                    );
                } else {
                    return (
                        <Tag color="blue" icon={<PlusOutlined />}>
                            直接新增
                        </Tag>
                    );
                }
            }
        },
        {
            title: '異動原因',
            dataIndex: 'changeReason',
            key: 'changeReason',
            width: 200,
            ellipsis: true,
            render: (text: string) => text || '-'
        },
        {
            title: '備註',
            dataIndex: 'remark',
            key: 'remark',
            width: 200,
            ellipsis: true,
            render: (text: string) => text || '-'
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (text: any, record: ServiceDepartmentChange) => {
                const relatedPromotion = getRelatedPromotion(record.uid!);
                const isFromPromotion = !!relatedPromotion;

                return (
                    <Space size="small">
                        <Button
                            type="link"
                            icon={<EditOutlined />}
                            onClick={() => handleRowClick(record.uid!)}
                            style={{ padding: 0 }}
                            disabled={isFromPromotion}
                        >
                            編輯
                        </Button>
                        <Popconfirm
                            title="確認刪除"
                            description={
                                isFromPromotion
                                    ? "此部門異動由升遷異動產生，無法直接刪除"
                                    : "確定要刪除這筆服務部門異動資料嗎？"
                            }
                            onConfirm={() => handleDeleteConfirm(record.uid!)}
                            okText="確認"
                            cancelText="取消"
                            disabled={isFromPromotion}
                        >
                            <Button
                                type="link"
                                danger
                                icon={<DeleteOutlined />}
                                style={{ padding: 0 }}
                                disabled={isFromPromotion}
                            >
                                刪除
                            </Button>
                        </Popconfirm>
                    </Space>
                );
            },
        },
    ];

    return (
        <div className="service-department-change-info">
            <Card
                title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <ApartmentOutlined />
                        <span>服務部門異動資料</span>
                    </div>
                }
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddNew}
                        loading={modalLoading}
                    >
                        新增服務部門異動
                    </Button>
                }
                style={{ marginBottom: 24 }}
            >
                {errorMsg && (
                    <div style={{ color: 'red', marginBottom: 16 }}>
                        錯誤: {errorMsg}
                    </div>
                )}

                {/* 篩選區域 */}
                <Card
                    size="small"
                    style={{ marginBottom: 16, background: '#fafafa' }}
                    title={
                        <Space>
                            <FilterOutlined />
                            <Text strong>篩選條件</Text>
                        </Space>
                    }
                >
                    <Row gutter={[16, 16]} align="middle">
                        <Col xs={24} sm={12} md={6}>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Text>異動來源：</Text>
                                <Select
                                    value={selectedSource || 'all'}
                                    onChange={handleSourceChange}
                                    style={{ width: '100%' }}
                                    placeholder="選擇異動來源"
                                >
                                    <Option value="all">全部</Option>
                                    <Option value="promotion">升遷異動產生</Option>
                                    <Option value="direct">直接新增</Option>
                                </Select>
                            </Space>
                        </Col>
                        <Col xs={24} sm={12} md={6}>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Text>服務部門：</Text>
                                <Select
                                    value={selectedDepartment || 'all'}
                                    onChange={handleDepartmentChange}
                                    style={{ width: '100%' }}
                                    placeholder="選擇服務部門"
                                    showSearch
                                    filterOption={(input, option) =>
                                        (option?.children?.toString() || '')?.toLowerCase()?.includes(input.toLowerCase())
                                    }
                                >
                                    <Option value="all">全部</Option>
                                    {departmentOptions.map(dept => (
                                        <Option key={dept.departmentId} value={dept.departmentId}>
                                            {dept.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Space>
                        </Col>
                        <Col xs={24} sm={12} md={6}>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Text>服務組別：</Text>
                                <Select
                                    value={selectedDivision || 'all'}
                                    onChange={handleDivisionChange}
                                    style={{ width: '100%' }}
                                    placeholder="選擇服務組別"
                                    showSearch
                                    filterOption={(input, option) =>
                                        (option?.children?.toString() || '')?.toLowerCase()?.includes(input.toLowerCase())
                                    }
                                >
                                    <Option value="all">全部</Option>
                                    {divisionOptions.map(div => (
                                        <Option key={div.divisionId} value={div.divisionId}>
                                            {div.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Space>
                        </Col>
                        <Col xs={24} sm={12} md={6}>
                            <Button onClick={clearFilters} style={{ marginTop: 22 }}>
                                清除篩選
                            </Button>
                        </Col>
                    </Row>
                </Card>

                <Table
                    columns={columns}
                    dataSource={filteredList}
                    rowKey="uid"
                    loading={loading}
                    size="small"
                    pagination={{
                        total: filteredList.length,
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
                    }}
                    scroll={{ x: 950 }}
                    rowClassName={(record) =>
                        deletingRows.has(record.uid!) ? 'row-deleting-pulse' : ''
                    }
                />
            </Card>

            <ServiceDepartmentChangeModal
                open={isModalOpen}
                userId={userId}
                departmentOptions={departmentOptions}
                divisionOptions={divisionOptions}
                serviceDepartmentChange={changeDetail}
                onOk={handleModalOk}
                onCancel={handleModalCancel}
                loading={modalLoading}
            />

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={() => handleDelete(deleteUid)}
                    onCancel={handleDeleteCancel}
                />
            )}
        </div>
    );
};

export default ServiceDepartmentChangeInfo; 