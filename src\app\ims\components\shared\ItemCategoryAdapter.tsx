"use client";

import React from 'react';
import { ApartmentOutlined } from '@ant-design/icons';
import GenericCategoryManagement, { GenericCategory, CategoryService, CategoryConfig } from './GenericCategoryManagement';
import { ItemCategory } from '@/services/ims/ItemCategoryService';
import { addItemCategory, editItemCategory, deleteItemCategory, buildCategoryTree } from '@/services/ims/ItemCategoryService';

interface ItemCategoryAdapterProps {
  visible: boolean;
  onClose: () => void;
  categories: ItemCategory[];
  categoryTreeData: any[];
  sortedCategoriesForDisplay: any[];
  onDataChange: () => void;
}

// 庫存品分類服務適配器
const itemCategoryService: CategoryService<ItemCategory> = {
  add: async (category: Partial<ItemCategory>) => {
    return await addItemCategory({
      name: category.name || '',
      description: category.description || '',
      parentID: category.parentID || null,
      sortCode: category.sortCode || 0,
    });
  },
  
  edit: async (category: Partial<ItemCategory>) => {
    return await editItemCategory({
      itemCategoryID: category.itemCategoryID || '',
      name: category.name || '',
      description: category.description || '',
      parentID: category.parentID || null,
      sortCode: category.sortCode || 0,
    });
  },
  
  delete: async (id: string) => {
    return await deleteItemCategory(id);
  },
  
  buildTree: (categories: ItemCategory[]) => {
    return buildCategoryTree(categories);
  }
};

// 庫存品分類配置
const itemCategoryConfig: CategoryConfig = {
  title: '庫存品分類管理',
  icon: <ApartmentOutlined style={{ color: '#1890ff' }} />,
  emptyMessage: '尚無庫存品分類',
  emptyDescription: '點擊右側「新增分類」按鈕開始建立庫存品分類結構。',
  entityName: '庫存品分類'
};

// 映射函數
const mapToGeneric = (category: ItemCategory): GenericCategory => ({
  id: category.itemCategoryID,
  name: category.name,
  description: category.description,
  parentID: category.parentID,
  sortCode: category.sortCode,
  children: category.children?.map(mapToGeneric)
});

const mapFromGeneric = (generic: GenericCategory, original?: ItemCategory): Partial<ItemCategory> => ({
  itemCategoryID: generic.id || original?.itemCategoryID,
  name: generic.name,
  description: generic.description,
  parentID: generic.parentID,
  sortCode: generic.sortCode
});

const getIdField = (category: ItemCategory): string => category.itemCategoryID;

const ItemCategoryAdapter: React.FC<ItemCategoryAdapterProps> = (props) => {
  // 忽略舊的 categoryTreeData 和 sortedCategoriesForDisplay props，因為通用組件會自動生成
  const { categoryTreeData, sortedCategoriesForDisplay, ...restProps } = props;
  
  return (
    <GenericCategoryManagement
      {...restProps}
      config={itemCategoryConfig}
      service={itemCategoryService}
      getIdField={getIdField}
      mapToGeneric={mapToGeneric}
      mapFromGeneric={mapFromGeneric}
    />
  );
};

export default ItemCategoryAdapter;
