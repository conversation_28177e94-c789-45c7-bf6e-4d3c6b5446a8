.employee-page-container {
    padding: 1.5rem;
}

.employee-page-title {
    font-size    : 1.5rem;
    font-weight  : bold;
    margin-bottom: 1rem;
}

.employee-card {
    width        : 100%;
    border-radius: 12px;
    overflow     : hidden;
    transition   : all 0.3s ease;
}

.employee-card-selected {
    border    : 2px solid #1890ff;
    box-shadow: 0 0 10px #1890ff;
    background: linear-gradient(145deg, #f6f8ff, #ffffff);
}

.employee-card-normal {
    border    : 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    background: linear-gradient(145deg, #ffffff, #f8fafc);
}

.employee-card-body {
    padding : 20px;
    height  : 100%;
    position: relative;
}

.employee-card-header {
    position      : relative;
    margin-bottom : 16px;
    padding-bottom: 12px;
    border-bottom : 1px solid rgba(0, 0, 0, 0.06);
}

.employee-name-tag {
    background   : #1890ff;
    color        : white;
    padding      : 2px 8px;
    border-radius: 4px;
    font-size    : 12px;
    font-weight  : 500;
    display      : inline-block;
}

.employee-name {
    margin         : 0;
    font-size      : 18px;
    font-weight    : 600;
    color          : #1f2937;
    flex           : 1;
    display        : flex;
    align-items    : center;
    justify-content: space-between;
}

.selected-badge {
    font-size    : 11px;
    padding      : 2px 6px;
    background   : #e6f7ff;
    color        : #1890ff;
    border-radius: 10px;
    font-weight  : normal;
}

.employee-info-row {
    display      : flex;
    align-items  : center;
    padding      : 6px 10px;
    background   : rgba(0, 0, 0, 0.02);
    border-radius: 6px;
    gap          : 8px;
}

.info-label {
    color    : #6b7280;
    font-size: 13px;
    width    : 70px;
}

.info-value {
    color      : #111827;
    font-size  : 13px;
    font-weight: 500;
}

.incomplete-warning {
    margin-top   : 12px;
    padding      : 8px 12px;
    background   : #fff2f0;
    border-radius: 6px;
    border       : 1px solid #ffccc7;
    display      : flex;
    align-items  : center;
    gap          : 6px;
}

.warning-dot {
    width        : 5px;
    height       : 5px;
    border-radius: 50%;
    background   : #ff4d4f;
    display      : inline-block;
}

.warning-text {
    color      : #cf1322;
    font-size  : 13px;
    font-weight: 500;
}

.function-menu-card {
    background   : linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: 12px;
    box-shadow   : 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.function-button {
    height         : 40px;
    border-radius  : 8px;
    display        : flex;
    align-items    : center;
    justify-content: center;
}

.primary-function-button {
    background: linear-gradient(to right, #1890ff, #096dd9);
    border    : none;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.default-function-button {
    background: #ffffff;
    border    : 1px solid #e6e6e6;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.danger-function-button {
    background: #fff1f0;
    border    : 1px solid #ffccc7;
    box-shadow: 0 1px 2px rgba(255, 77, 79, 0.05);
}

.tabs-container {
    background   : linear-gradient(to bottom, #ffffff, #f5f7fa);
    border-radius: 12px;
    padding      : 24px;
    box-shadow   : 0 2px 12px rgba(0, 0, 0, 0.08);
}

.tabs-header {
    margin-bottom  : 24px;
    display        : flex;
    justify-content: space-between;
    align-items    : center;
}

.tabs-title {
    margin     : 0;
    font-size  : 18px;
    font-weight: 600;
    color      : #1f2937;
}

.tab-content {
    background   : #ffffff;
    border-radius: 12px;
    padding      : 28px;
    min-height   : 400px;
    box-shadow   : 0 1px 3px rgba(0, 0, 0, 0.05);
    position     : relative;
    overflow     : hidden;
}

.empty-state {
    text-align    : center;
    padding       : 60px 20px;
    color         : #6B7280;
    font-size     : 16px;
    background    : #f9fafb;
    border-radius : 12px;
    border        : 1px dashed #e5e7eb;
    display       : flex;
    flex-direction: column;
    align-items   : center;
    gap           : 16px;
}

.empty-state-title {
    font-size  : 16px;
    font-weight: 500;
    color      : #374151;
}

.empty-state-subtitle {
    font-size: 14px;
    color    : #6B7280;
}

.pagination-controls {
    position        : absolute;
    top             : 16px;
    right           : 16px;
    display         : flex;
    align-items     : center;
    gap             : 8px;
    background-color: rgba(255, 255, 255, 0.9);
    padding         : 8px;
    border-radius   : 8px;
    box-shadow      : 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index         : 10;
}

.page-number {
    font-weight: bold;
    min-width  : 50px;
    text-align : center;
}

/* 詳細資料區塊樣式 */
.detail-main-card {
    border-radius: 12px;
    box-shadow   : 0 4px 12px rgba(0, 0, 0, 0.1);
    border       : 1px solid #e8f4fd;
}

.detail-card-title {
    display    : flex;
    align-items: center;
    gap        : 12px;
    color      : #1890ff;
    font-size  : 18px;
    font-weight: 600;
}

.detail-card-title .material-icons {
    font-size: 24px;
}

.detail-content-wrapper {
    padding: 16px 0;
}

/* 確保 Card 樣式不被覆蓋 */
.detail-content-wrapper .ant-card {
    overflow: hidden;
}

.detail-content-wrapper .ant-card * {
    transition: background-color 0.3s ease;
}

/* 人事資料區塊 */
.personnel-info-card {
    border-radius   : 8px !important;
    border          : 1px solid #f6ffed !important;
    background-color: #f6ffed !important;
}

.personnel-info-card .ant-card-body {
    background-color: #f6ffed !important;
}

.personnel-info-card .ant-card-head {
    background-color: #f6ffed !important;
    border-bottom   : 1px solid #d9f7be !important;
}

.personnel-info-title {
    display    : flex;
    align-items: center;
    gap        : 8px;
    color      : #52c41a;
    font-size  : 16px;
    font-weight: 600;
}

.personnel-info-title .material-icons {
    font-size: 20px;
}

.personnel-info-body {
    padding: 20px;
}

.personnel-info-column {
    display       : flex;
    flex-direction: column;
    gap           : 12px;
}

/* 薪俸資料區塊 - 只在詳細資料區域生效 */
.detail-content-wrapper .salary-info-card {
    border-radius   : 8px !important;
    border          : 1px solid #fff7e6 !important;
    background-color: #fff7e6 !important;
    height          : fit-content;
}

.detail-content-wrapper .salary-info-card .ant-card-body {
    background-color: #fff7e6 !important;
}

.detail-content-wrapper .salary-info-card .ant-card-head {
    background-color: #fff7e6 !important;
    border-bottom   : 1px solid #ffe7ba !important;
}

.salary-info-title {
    display    : flex;
    align-items: center;
    gap        : 8px;
    color      : #fa8c16;
    font-size  : 16px;
    font-weight: 600;
}

.salary-info-title .material-icons {
    font-size: 20px;
}

.salary-info-body {
    padding: 20px;
}

.salary-info-column {
    display       : flex;
    flex-direction: column;
    gap           : 12px;
}

/* 通用資料項目樣式 */
.detail-data-item {
    display      : flex;
    align-items  : center;
    padding      : 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-data-item:last-child {
    border-bottom: none;
}

.detail-data-label {
    color      : #666;
    font-weight: 500;
    font-size  : 14px;
}

.detail-data-label-wide {
    min-width: 90px;
}

.detail-data-label-normal {
    min-width: 70px;
}

.detail-data-value {
    color      : #262626;
    font-weight: 500;
    font-size  : 14px;
}

.detail-data-value-highlight {
    color      : #1890ff;
    font-weight: 600;
    font-size  : 16px;
}

.detail-data-value-salary {
    color      : #fa8c16;
    font-weight: 600;
    font-size  : 16px;
}

.detail-data-link {
    transition: all 0.2s ease;
}

.detail-data-link:hover {
    color          : #40a9ff !important;
    text-decoration: underline !important;
    cursor         : pointer;
}

/* 空狀態樣式 */
.detail-empty-state {
    text-align: center;
    padding   : 40px 20px;
    color     : #999;
    font-size : 16px;
}

.detail-empty-icon {
    margin-bottom: 16px;
}

.detail-empty-icon .material-icons {
    font-size: 48px;
    color    : #d9d9d9;
}

/* ========== Loading 效果樣式 ========== */

/* 主要 Loading 覆蓋層 */
.loading-overlay {
    position       : fixed;
    top            : 0;
    left           : 0;
    width          : 100%;
    height         : 100vh;
    background     : rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    display        : flex;
    justify-content: center;
    align-items    : center;
    z-index        : 9999;
    animation      : fadeIn 0.3s ease-in-out;
}

/* 區域 Loading 覆蓋層 */
.section-loading-overlay {
    position       : absolute;
    top            : 0;
    left           : 0;
    width          : 100%;
    height         : 100%;
    background     : rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(2px);
    display        : flex;
    justify-content: center;
    align-items    : center;
    z-index        : 1000;
    border-radius  : 12px;
    animation      : fadeIn 0.2s ease-in-out;
}

/* Loading 容器 */
.loading-container {
    display       : flex;
    flex-direction: column;
    align-items   : center;
    gap           : 16px;
    padding       : 24px;
    background    : rgba(255, 255, 255, 0.95);
    border-radius : 16px;
    box-shadow    : 0 8px 32px rgba(0, 0, 0, 0.12);
    border        : 1px solid rgba(255, 255, 255, 0.2);
}

/* Loading 動畫圓環 */
.loading-spinner {
    width        : 48px;
    height       : 48px;
    border       : 4px solid #f3f4f6;
    border-top   : 4px solid #1890ff;
    border-radius: 50%;
    animation    : spin 1s linear infinite;
    position     : relative;
}

.loading-spinner::after {
    content      : '';
    position     : absolute;
    top          : -4px;
    left         : -4px;
    width        : 48px;
    height       : 48px;
    border       : 4px solid transparent;
    border-top   : 4px solid #096dd9;
    border-radius: 50%;
    animation    : spin 1.5s linear infinite reverse;
}

/* 小型 Loading 動畫 */
.loading-spinner-small {
    width        : 24px;
    height       : 24px;
    border       : 2px solid #f3f4f6;
    border-top   : 2px solid #1890ff;
    border-radius: 50%;
    animation    : spin 0.8s linear infinite;
}

/* Loading 文字 */
.loading-text {
    color      : #374151;
    font-size  : 14px;
    font-weight: 500;
    text-align : center;
}

.loading-text-large {
    color      : #1f2937;
    font-size  : 16px;
    font-weight: 600;
}

/* 按鈕 Loading 狀態 */
.button-loading {
    pointer-events: none;
    opacity       : 0.7;
    position      : relative;
}

.button-loading .ant-btn-icon {
    animation: spin 1s linear infinite;
}

/* 卡片 Loading 狀態 */
.card-loading {
    position: relative;
    overflow: hidden;
}

.card-loading::before {
    content : '';
    position: absolute;
    top     : 0;
    left    : -100%;
    width   : 100%;
    height  : 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.6),
            transparent);
    animation: shimmer 1.5s infinite;
    z-index  : 1;
}

/* 骨架屏效果 */
.skeleton-card {
    background   : #f5f5f5;
    border-radius: 8px;
    overflow     : hidden;
    position     : relative;
}

.skeleton-card::before {
    content : '';
    position: absolute;
    top     : 0;
    left    : -100%;
    width   : 100%;
    height  : 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.8),
            transparent);
    animation: shimmer 1.8s infinite;
}

.skeleton-line {
    height       : 16px;
    background   : #e5e7eb;
    border-radius: 4px;
    margin-bottom: 12px;
    position     : relative;
    overflow     : hidden;
}

.skeleton-line::before {
    content : '';
    position: absolute;
    top     : 0;
    left    : -100%;
    width   : 100%;
    height  : 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.8),
            transparent);
    animation: shimmer 1.5s infinite;
}

.skeleton-line.short {
    width: 60%;
}

.skeleton-line.medium {
    width: 80%;
}

.skeleton-line.long {
    width: 95%;
}

/* 點狀 Loading */
.dots-loading {
    display    : flex;
    gap        : 4px;
    align-items: center;
}

.dots-loading span {
    width        : 8px;
    height       : 8px;
    border-radius: 50%;
    background   : #1890ff;
    animation    : bounce 1.4s infinite ease-in-out both;
}

.dots-loading span:nth-child(1) {
    animation-delay: -0.32s;
}

.dots-loading span:nth-child(2) {
    animation-delay: -0.16s;
}

.dots-loading span:nth-child(3) {
    animation-delay: 0s;
}

/* 進度條 Loading */
.progress-loading {
    width        : 200px;
    height       : 4px;
    background   : #f0f0f0;
    border-radius: 2px;
    overflow     : hidden;
    position     : relative;
}

.progress-loading::before {
    content : '';
    position: absolute;
    top     : 0;
    left    : -100%;
    width   : 100%;
    height  : 100%;
    background: linear-gradient(90deg,
            transparent,
            #1890ff,
            transparent);
    animation: progress 1.5s infinite;
}

/* 動畫關鍵幀 */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

@keyframes bounce {

    0%,
    80%,
    100% {
        transform: scale(0);
    }

    40% {
        transform: scale(1);
    }
}

@keyframes progress {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

/* 響應式調整 */
@media (max-width: 768px) {
    .loading-container {
        padding: 16px;
        margin : 0 16px;
    }

    .loading-spinner {
        width : 40px;
        height: 40px;
    }

    .loading-spinner::after {
        width : 40px;
        height: 40px;
    }
}