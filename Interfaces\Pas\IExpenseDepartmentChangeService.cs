using FAST_ERP_Backend.Models.Pas;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IExpenseDepartmentChangeService
    {
        /// <summary>
        /// 取得使用者的開支部門異動列表
        /// </summary>
        /// <param name="userId">使用者編號</param>
        /// <returns>開支部門異動列表</returns>
        Task<List<ExpenseDepartmentChangeDTO>> GetExpenseDepartmentChangeListAsync(string userId);

        /// <summary>
        /// 取得開支部門異動明細
        /// </summary>
        /// <param name="uid">開支部門異動編號</param>
        /// <returns>開支部門異動明細</returns>
        Task<ExpenseDepartmentChangeDTO> GetExpenseDepartmentChangeDetailAsync(string uid);

        /// <summary>
        /// 新增開支部門異動資料
        /// </summary>
        /// <param name="data">開支部門異動資料</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>開支部門異動編號</returns>
        Task<string> AddExpenseDepartmentChangeAsync(ExpenseDepartmentChangeDTO data, string userId);

        /// <summary>
        /// 編輯開支部門異動資料
        /// </summary>
        /// <param name="data">開支部門異動資料</param>
        /// <param name="uid">開支部門異動編號</param>
        /// <returns>無回傳值</returns>
        Task UpdateExpenseDepartmentChangeAsync(ExpenseDepartmentChangeDTO data, string uid);

        /// <summary>
        /// 刪除開支部門異動資料
        /// </summary>
        /// <param name="uid">開支部門異動編號</param>
        /// <returns>無回傳值</returns>
        Task DeleteExpenseDepartmentChangeAsync(string uid);
    }
}