import { httpClient } from '@/services/http';

// 服務部門異動資料類型定義
export interface ServiceDepartmentChange {
    uid?: string;
    userId?: string;
    serviceDepartmentId: string;
    serviceDepartmentName?: string;
    serviceDivisionId: string;
    serviceDivisionName?: string;
    changeDate: string;
    effectiveDate: string;
    changeReason: string;
    remark: string;
    updateTime?: number;
}

// API回應類型
interface ApiResponse<T> {
    success: boolean;
    data?: T;
    message?: string;
}

// 取得服務部門異動列表
export const getServiceDepartmentChangeList = async (userId: string): Promise<ApiResponse<ServiceDepartmentChange[]>> => {
    try {
        const response = await httpClient<ServiceDepartmentChange[]>(`/ServiceDepartmentChange/GetAll/${userId}`);
        return {
            success: response.success || true,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '載入服務部門異動資料失敗'
        };
    }
};

// 取得服務部門異動明細
export const getServiceDepartmentChangeDetail = async (uid: string): Promise<ApiResponse<ServiceDepartmentChange>> => {
    try {
        const response = await httpClient<ServiceDepartmentChange>(`/ServiceDepartmentChange/Get/${uid}`);
        return {
            success: response.success || true,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '載入服務部門異動明細失敗'
        };
    }
};

// 新增服務部門異動
export const addServiceDepartmentChange = async (formData: FormData): Promise<ApiResponse<any>> => {
    try {
        const response = await httpClient<any>('/ServiceDepartmentChange/Add', {
            method: 'POST',
            body: formData
        });
        return {
            success: response.success,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '新增服務部門異動失敗'
        };
    }
};

// 編輯服務部門異動
export const editServiceDepartmentChange = async (formData: FormData): Promise<ApiResponse<any>> => {
    try {
        const response = await httpClient<any>('/ServiceDepartmentChange/Edit', {
            method: 'POST',
            body: formData
        });
        return {
            success: response.success,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '編輯服務部門異動失敗'
        };
    }
};

// 刪除服務部門異動
export const deleteServiceDepartmentChange = async (uid: string): Promise<ApiResponse<any>> => {
    try {
        const response = await httpClient<any>('/ServiceDepartmentChange/Delete', {
            method: 'POST',
            body: JSON.stringify(uid)
        });
        return {
            success: response.success,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '刪除服務部門異動失敗'
        };
    }
};

// 建立空的服務部門異動資料
export const createEmptyServiceDepartmentChange = (userId: string): ServiceDepartmentChange => {
    return {
        uid: '',
        userId: userId,
        serviceDepartmentId: '',
        serviceDepartmentName: '',
        serviceDivisionId: '',
        serviceDivisionName: '',
        changeDate: '',
        effectiveDate: '',
        changeReason: '',
        remark: ''
    };
};

// 將服務部門異動資料轉換為FormData
export const createServiceDepartmentChangeFormData = (data: ServiceDepartmentChange): FormData => {
    const formData = new FormData();

    formData.append('uid', data.uid || '');
    formData.append('userId', data.userId || '');
    formData.append('serviceDepartmentId', data.serviceDepartmentId);
    formData.append('serviceDivisionId', data.serviceDivisionId);
    formData.append('changeDate', data.changeDate);
    formData.append('effectiveDate', data.effectiveDate);
    formData.append('changeReason', data.changeReason);
    formData.append('remark', data.remark);

    return formData;
}; 